"""
Excel文件读取工具
支持读取.xlsx和.xls格式的Excel文件
提供多种读取选项和数据处理功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ExcelReader:
    """Excel文件读取器类"""
    
    def __init__(self):
        self.data = None
        self.file_path = None
        self.sheet_names = None
    
    def read_excel(self, file_path, sheet_name=0, header=0, **kwargs):
        """
        读取Excel文件
        
        参数:
        - file_path: Excel文件路径
        - sheet_name: 工作表名称或索引 (默认为0，即第一个工作表)
        - header: 标题行位置 (默认为0，即第一行)
        - **kwargs: pandas.read_excel的其他参数
        
        返回:
        - DataFrame: 读取的数据
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件扩展名
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in ['.xlsx', '.xls']:
                raise ValueError(f"不支持的文件格式: {file_ext}，请使用.xlsx或.xls文件")
            
            # 读取Excel文件
            print(f"正在读取Excel文件: {file_path}")
            
            # 首先获取所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            self.sheet_names = excel_file.sheet_names
            print(f"发现工作表: {self.sheet_names}")
            
            # 读取指定工作表
            self.data = pd.read_excel(file_path, sheet_name=sheet_name, header=header, **kwargs)
            self.file_path = file_path
            
            print(f"成功读取数据，形状: {self.data.shape}")
            print(f"列名: {list(self.data.columns)}")
            
            return self.data
            
        except Exception as e:
            print(f"读取Excel文件时出错: {str(e)}")
            return None
    
    def read_multiple_sheets(self, file_path, sheet_names=None, **kwargs):
        """
        读取多个工作表
        
        参数:
        - file_path: Excel文件路径
        - sheet_names: 工作表名称列表，如果为None则读取所有工作表
        - **kwargs: pandas.read_excel的其他参数
        
        返回:
        - dict: 包含所有工作表数据的字典
        """
        try:
            if sheet_names is None:
                # 读取所有工作表
                data_dict = pd.read_excel(file_path, sheet_name=None, **kwargs)
            else:
                # 读取指定工作表
                data_dict = pd.read_excel(file_path, sheet_name=sheet_names, **kwargs)
            
            print(f"成功读取 {len(data_dict)} 个工作表")
            for sheet_name, df in data_dict.items():
                print(f"工作表 '{sheet_name}': {df.shape}")
            
            return data_dict
            
        except Exception as e:
            print(f"读取多个工作表时出错: {str(e)}")
            return None
    
    def get_basic_info(self):
        """获取数据基本信息"""
        if self.data is None:
            print("请先读取Excel文件")
            return
        
        print("=" * 50)
        print("数据基本信息")
        print("=" * 50)
        print(f"数据形状: {self.data.shape}")
        print(f"列数: {self.data.shape[1]}")
        print(f"行数: {self.data.shape[0]}")
        print(f"列名: {list(self.data.columns)}")
        print("\n数据类型:")
        print(self.data.dtypes)
        print("\n缺失值统计:")
        print(self.data.isnull().sum())
        print("\n前5行数据:")
        print(self.data.head())
    
    def get_summary_statistics(self):
        """获取数值列的描述性统计"""
        if self.data is None:
            print("请先读取Excel文件")
            return
        
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) > 0:
            print("=" * 50)
            print("数值列描述性统计")
            print("=" * 50)
            print(self.data[numeric_columns].describe())
        else:
            print("没有找到数值列")
    
    def plot_data_overview(self, figsize=(15, 10)):
        """绘制数据概览图"""
        if self.data is None:
            print("请先读取Excel文件")
            return
        
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) == 0:
            print("没有数值列可以绘图")
            return
        
        # 创建子图
        n_cols = min(3, len(numeric_columns))
        n_rows = (len(numeric_columns) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)
        if n_rows == 1:
            axes = [axes] if n_cols == 1 else axes
        else:
            axes = axes.flatten()
        
        # 绘制每个数值列的直方图
        for i, col in enumerate(numeric_columns):
            if i < len(axes):
                axes[i].hist(self.data[col].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
                axes[i].set_title(f'{col} 分布')
                axes[i].set_xlabel(col)
                axes[i].set_ylabel('频次')
        
        # 隐藏多余的子图
        for i in range(len(numeric_columns), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.show()
    
    def save_to_csv(self, output_path=None):
        """将数据保存为CSV文件"""
        if self.data is None:
            print("请先读取Excel文件")
            return
        
        if output_path is None:
            # 自动生成输出路径
            if self.file_path:
                base_name = Path(self.file_path).stem
                output_path = f"{base_name}_converted.csv"
            else:
                output_path = "excel_data.csv"
        
        try:
            self.data.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"数据已保存到: {output_path}")
        except Exception as e:
            print(f"保存CSV文件时出错: {str(e)}")

def demo_usage():
    """演示如何使用ExcelReader"""
    print("Excel文件读取器演示")
    print("=" * 50)
    
    # 创建读取器实例
    reader = ExcelReader()
    
    # 示例文件路径（请替换为你的实际文件路径）
    example_file = "example.xlsx"  # 替换为你的Excel文件路径
    
    # 检查示例文件是否存在
    if not os.path.exists(example_file):
        print(f"示例文件 {example_file} 不存在")
        print("请将你的Excel文件路径替换到 example_file 变量中")
        return
    
    # 读取Excel文件
    data = reader.read_excel(example_file)
    
    if data is not None:
        # 显示基本信息
        reader.get_basic_info()
        
        # 显示描述性统计
        reader.get_summary_statistics()
        
        # 绘制数据概览
        reader.plot_data_overview()
        
        # 保存为CSV
        reader.save_to_csv()

if __name__ == "__main__":
    # 运行演示
    demo_usage()
    
    # 你也可以直接使用pandas读取Excel文件的简单方法：
    print("\n" + "=" * 50)
    print("简单读取Excel文件的方法:")
    print("=" * 50)
    
    # 方法1: 读取第一个工作表
    # df = pd.read_excel('your_file.xlsx')
    
    # 方法2: 读取指定工作表
    # df = pd.read_excel('your_file.xlsx', sheet_name='Sheet1')
    
    # 方法3: 读取多个工作表
    # data_dict = pd.read_excel('your_file.xlsx', sheet_name=None)
    
    # 方法4: 指定列范围
    # df = pd.read_excel('your_file.xlsx', usecols='A:E')
    
    # 方法5: 跳过行
    # df = pd.read_excel('your_file.xlsx', skiprows=2)
    
    print("请参考上面的注释代码来读取你的Excel文件")
