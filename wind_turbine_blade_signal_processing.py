import numpy as np
import matplotlib.pyplot as plt
import pywt
from scipy import signal
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def generate_blade_cavity_signal(duration=5, fs=2000):
    """
    生成风机叶片内腔微小声信号
    
    参数:
    duration: 信号持续时间(秒)
    fs: 采样频率(Hz)
    
    返回:
    t: 时间轴
    clean_signal: 原始微小声信号
    noisy_signal: 含噪声信号
    """
    # 时间轴
    t = np.linspace(0, duration, int(fs * duration), endpoint=False)
    
    # 风机叶片内腔微小声信号特征：
    # 1. 叶片振动基频 (约3-8 Hz)
    blade_vibration = 0.02 * np.sin(2 * np.pi * 5.5 * t)
    
    # 2. 气流湍流声 (约20-50 Hz)
    phase_noise = np.random.uniform(0, 2*np.pi)
    turbulence_noise = 0.015 * np.sin(2 * np.pi * 35 * t + phase_noise + 0.1 * np.sin(2 * np.pi * 2 * t))
    
    # 3. 结构共振 (约80-120 Hz)
    structural_resonance = 0.008 * np.sin(2 * np.pi * 95 * t)
    
    # 4. 轴承噪声 (约150-300 Hz)
    bearing_noise = 0.005 * np.sin(2 * np.pi * 220 * t + 0.3 * np.sin(2 * np.pi * 15 * t))
    
    # 5. 随机脉冲 (模拟叶片与气流相互作用)
    pulse_times = np.random.choice(len(t), size=int(duration * 2), replace=False)
    pulse_signal = np.zeros_like(t)
    for pulse_time in pulse_times:
        if pulse_time < len(t) - 100:
            pulse_signal[pulse_time:pulse_time+50] += 0.01 * np.exp(-np.arange(50) / 10)
    
    # 合成微小声信号 (幅度很小，模拟真实情况)
    clean_signal = (blade_vibration + turbulence_noise + structural_resonance + 
                   bearing_noise + pulse_signal)
    
    # 添加环境噪声和电子噪声
    np.random.seed(42)
    
    # 白噪声 (电子噪声)
    white_noise = np.random.normal(0, 0.05, len(t))
    
    # 低频漂移 (温度变化等)
    drift = 0.02 * np.sin(2 * np.pi * 0.1 * t) + 0.01 * np.sin(2 * np.pi * 0.05 * t)
    
    # 工频干扰 (50Hz)
    power_interference = 0.03 * np.sin(2 * np.pi * 50 * t)
    
    # 高频噪声
    high_freq_noise_raw = np.random.normal(0, 0.02, len(t))
    # 归一化截止频率
    nyquist = fs / 2
    normalized_cutoff = 500 / nyquist
    sos = signal.butter(4, normalized_cutoff, 'low', output='sos')
    high_freq_noise = signal.sosfilt(sos, high_freq_noise_raw)
    
    # 合成含噪声信号
    noisy_signal = clean_signal + white_noise + drift + power_interference + high_freq_noise
    
    return t, clean_signal, noisy_signal

def amplify_signal(signal, gain_db=40):
    """
    信号放大
    
    参数:
    signal: 输入信号
    gain_db: 增益(dB)
    
    返回:
    amplified_signal: 放大后的信号
    gain_linear: 线性增益
    """
    gain_linear = 10 ** (gain_db / 20)
    amplified_signal = signal * gain_linear
    
    return amplified_signal, gain_linear

def wavelet_denoise_vi_levels(signal, wavelet='db8', threshold_mode='soft'):
    """
    VI级小波降噪 (6级分解)
    
    参数:
    signal: 输入信号
    wavelet: 小波基函数
    threshold_mode: 阈值模式
    
    返回:
    denoised_signal: 降噪后信号
    coeffs_original: 原始小波系数
    coeffs_denoised: 降噪后小波系数
    thresholds: 各级阈值
    """
    # VI级小波分解
    coeffs_original = pywt.wavedec(signal, wavelet, level=6)
    
    # 估计噪声标准差
    sigma = np.median(np.abs(coeffs_original[-1])) / 0.6745
    
    # 计算各级阈值 (不同级别使用不同阈值策略)
    thresholds = []
    coeffs_denoised = [coeffs_original[0]]  # 保留近似系数
    
    for i in range(1, 7):  # 6个细节层
        # 根据分解级别调整阈值
        level_factor = 1.2 ** (7 - i)  # 高频级别使用更大阈值
        threshold = sigma * np.sqrt(2 * np.log(len(signal))) * level_factor
        thresholds.append(threshold)
        
        # 软阈值处理
        denoised_coeff = pywt.threshold(coeffs_original[i], threshold, mode=threshold_mode)
        coeffs_denoised.append(denoised_coeff)
    
    # 小波重构
    denoised_signal = pywt.waverec(coeffs_denoised, wavelet)
    
    return denoised_signal, coeffs_original, coeffs_denoised, thresholds

def calculate_signal_metrics(original, processed):
    """计算信号质量指标"""
    # 信噪比
    signal_power = np.mean(original**2)
    noise_power = np.mean((processed - original)**2)
    if noise_power > 0:
        snr = 10 * np.log10(signal_power / noise_power)
    else:
        snr = float('inf')
    
    # 均方根误差
    rmse = np.sqrt(np.mean((processed - original)**2))
    
    # 相关系数
    correlation = np.corrcoef(original, processed)[0, 1]
    
    return snr, rmse, correlation

def plot_signal_processing_steps(t, clean_signal, noisy_signal, amplified_signal, 
                                denoised_signal, gain_linear, thresholds):
    """绘制信号处理各步骤的可视化图表"""
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 原始微小声信号
    ax1 = plt.subplot(4, 2, 1)
    plt.plot(t, clean_signal, 'b-', linewidth=1.5, alpha=0.8)
    plt.title('步骤1: 风机叶片内腔微小声信号', fontsize=14, fontweight='bold')
    plt.ylabel('幅度 (V)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)  # 显示前2秒
    
    # 添加信号特征标注
    plt.text(0.1, max(clean_signal)*0.8, f'最大幅度: {max(abs(clean_signal)):.4f} V', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    
    # 2. 含噪声信号
    ax2 = plt.subplot(4, 2, 2)
    plt.plot(t, noisy_signal, 'r-', linewidth=1, alpha=0.7)
    plt.title('步骤2: 添加环境噪声和干扰', fontsize=14, fontweight='bold')
    plt.ylabel('幅度 (V)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    # 计算噪声水平
    noise_level = np.std(noisy_signal - clean_signal)
    plt.text(0.1, max(noisy_signal)*0.8, f'噪声水平: {noise_level:.4f} V', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    
    # 3. 信号放大
    ax3 = plt.subplot(4, 2, 3)
    plt.plot(t, amplified_signal, 'g-', linewidth=1, alpha=0.8)
    plt.title(f'步骤3: 信号放大 (增益: {20*np.log10(gain_linear):.1f} dB)', 
              fontsize=14, fontweight='bold')
    plt.ylabel('幅度 (V)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    plt.text(0.1, max(amplified_signal)*0.8, 
             f'放大后幅度: {max(abs(amplified_signal)):.2f} V', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
    
    # 4. VI级小波降噪
    ax4 = plt.subplot(4, 2, 4)
    plt.plot(t, denoised_signal, 'm-', linewidth=1.5, alpha=0.8)
    plt.title('步骤4: VI级小波降噪处理', fontsize=14, fontweight='bold')
    plt.ylabel('幅度 (V)')
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    # 计算降噪效果
    snr_before = calculate_signal_metrics(clean_signal * gain_linear, amplified_signal)[0]
    snr_after = calculate_signal_metrics(clean_signal * gain_linear, denoised_signal)[0]
    plt.text(0.1, max(denoised_signal)*0.8, 
             f'SNR改善: {snr_after - snr_before:.1f} dB', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="plum"))
    
    # 5. 信号对比 (放大前后)
    ax5 = plt.subplot(4, 2, 5)
    plt.plot(t, clean_signal, 'b-', linewidth=2, label='原始微小信号', alpha=0.8)
    plt.plot(t, noisy_signal, 'r-', linewidth=1, label='含噪声信号', alpha=0.6)
    plt.title('放大前信号对比', fontsize=14, fontweight='bold')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅度 (V)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    # 6. 信号对比 (降噪前后)
    ax6 = plt.subplot(4, 2, 6)
    plt.plot(t, amplified_signal, 'g-', linewidth=1, label='放大后含噪信号', alpha=0.6)
    plt.plot(t, denoised_signal, 'm-', linewidth=2, label='VI级小波降噪', alpha=0.8)
    plt.plot(t, clean_signal * gain_linear, 'b--', linewidth=2, label='理想信号', alpha=0.8)
    plt.title('降噪前后信号对比', fontsize=14, fontweight='bold')
    plt.xlabel('时间 (秒)')
    plt.ylabel('幅度 (V)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 2)
    
    # 7. 频域分析
    ax7 = plt.subplot(4, 2, 7)
    
    # FFT分析
    N = len(t)
    freqs = np.fft.fftfreq(N, 1/2000)[:N//2]
    
    fft_clean = np.abs(np.fft.fft(clean_signal * gain_linear))[:N//2]
    fft_noisy = np.abs(np.fft.fft(amplified_signal))[:N//2]
    fft_denoised = np.abs(np.fft.fft(denoised_signal))[:N//2]
    
    plt.semilogy(freqs, fft_clean, 'b-', linewidth=2, label='理想信号', alpha=0.8)
    plt.semilogy(freqs, fft_noisy, 'r-', linewidth=1, label='含噪信号', alpha=0.6)
    plt.semilogy(freqs, fft_denoised, 'm-', linewidth=2, label='降噪后信号', alpha=0.8)
    plt.title('频域分析对比', fontsize=14, fontweight='bold')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅度 (对数尺度)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 500)
    
    # 8. VI级小波分解系数
    ax8 = plt.subplot(4, 2, 8)
    
    # 显示各级阈值
    levels = ['D1', 'D2', 'D3', 'D4', 'D5', 'D6']
    plt.bar(levels, thresholds, color=['red', 'orange', 'yellow', 'green', 'blue', 'purple'], 
            alpha=0.7)
    plt.title('VI级小波分解各级阈值', fontsize=14, fontweight='bold')
    plt.xlabel('分解级别')
    plt.ylabel('阈值')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标注
    for i, (level, threshold) in enumerate(zip(levels, thresholds)):
        plt.text(i, threshold + max(thresholds)*0.02, f'{threshold:.3f}', 
                ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('wind_turbine_blade_signal_processing.png', dpi=300, bbox_inches='tight')
    print("风机叶片信号处理流程图已保存为: wind_turbine_blade_signal_processing.png")

    return fig

def plot_wavelet_decomposition_analysis(coeffs_original, coeffs_denoised, thresholds):
    """绘制VI级小波分解详细分析图"""

    fig, axes = plt.subplots(7, 2, figsize=(16, 20))

    # 近似系数 (A6)
    axes[0, 0].plot(coeffs_original[0], 'b-', linewidth=1, alpha=0.8)
    axes[0, 0].set_title('A6 - 近似系数 (原始)', fontweight='bold')
    axes[0, 0].grid(True, alpha=0.3)

    axes[0, 1].plot(coeffs_denoised[0], 'r-', linewidth=1, alpha=0.8)
    axes[0, 1].set_title('A6 - 近似系数 (保留)', fontweight='bold')
    axes[0, 1].grid(True, alpha=0.3)

    # 细节系数 D1-D6
    detail_names = ['D6', 'D5', 'D4', 'D3', 'D2', 'D1']
    for i in range(6):
        # 原始细节系数
        axes[i+1, 0].plot(coeffs_original[i+1], 'b-', linewidth=1, alpha=0.8)
        axes[i+1, 0].set_title(f'{detail_names[i]} - 细节系数 (原始)', fontweight='bold')
        axes[i+1, 0].grid(True, alpha=0.3)
        axes[i+1, 0].axhline(y=thresholds[i], color='red', linestyle='--',
                            label=f'阈值: {thresholds[i]:.3f}')
        axes[i+1, 0].axhline(y=-thresholds[i], color='red', linestyle='--')
        axes[i+1, 0].legend(fontsize=8)

        # 降噪后细节系数
        axes[i+1, 1].plot(coeffs_denoised[i+1], 'r-', linewidth=1, alpha=0.8)
        axes[i+1, 1].set_title(f'{detail_names[i]} - 细节系数 (降噪后)', fontweight='bold')
        axes[i+1, 1].grid(True, alpha=0.3)

        # 计算保留率
        original_energy = np.sum(coeffs_original[i+1]**2)
        denoised_energy = np.sum(coeffs_denoised[i+1]**2)
        retention_rate = (denoised_energy / original_energy * 100) if original_energy > 0 else 0
        axes[i+1, 1].text(0.02, 0.95, f'能量保留: {retention_rate:.1f}%',
                         transform=axes[i+1, 1].transAxes,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

    plt.tight_layout()
    plt.savefig('wavelet_decomposition_vi_levels.png', dpi=300, bbox_inches='tight')
    print("VI级小波分解详细分析图已保存为: wavelet_decomposition_vi_levels.png")

    return fig

def plot_performance_metrics(t, clean_signal, noisy_signal, amplified_signal,
                           denoised_signal, gain_linear):
    """绘制性能指标分析图"""

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. SNR改善分析
    ax1 = axes[0, 0]

    # 计算各阶段SNR
    clean_amplified = clean_signal * gain_linear
    snr_original = calculate_signal_metrics(clean_signal, noisy_signal)[0]
    snr_amplified = calculate_signal_metrics(clean_amplified, amplified_signal)[0]
    snr_denoised = calculate_signal_metrics(clean_amplified, denoised_signal)[0]

    stages = ['原始信号', '放大后', '降噪后']
    snr_values = [snr_original, snr_amplified, snr_denoised]
    colors = ['blue', 'red', 'green']

    bars = ax1.bar(stages, snr_values, color=colors, alpha=0.7)
    ax1.set_title('各阶段信噪比(SNR)对比', fontweight='bold')
    ax1.set_ylabel('SNR (dB)')
    ax1.grid(True, alpha=0.3)

    # 添加数值标注
    for bar, snr in zip(bars, snr_values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{snr:.1f} dB', ha='center', va='bottom', fontweight='bold')

    # 2. 信号幅度分析
    ax2 = axes[0, 1]

    signal_names = ['原始微小信号', '含噪声信号', '放大后信号', '降噪后信号']
    max_amplitudes = [max(abs(clean_signal)), max(abs(noisy_signal)),
                     max(abs(amplified_signal)), max(abs(denoised_signal))]

    bars = ax2.bar(signal_names, max_amplitudes,
                  color=['blue', 'red', 'orange', 'green'], alpha=0.7)
    ax2.set_title('各阶段信号最大幅度对比', fontweight='bold')
    ax2.set_ylabel('最大幅度 (V)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)

    # 添加数值标注
    for bar, amp in zip(bars, max_amplitudes):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(max_amplitudes)*0.01,
                f'{amp:.3f} V', ha='center', va='bottom', fontweight='bold', fontsize=9)

    # 3. 频域能量分析
    ax3 = axes[1, 0]

    # 计算各频段能量
    N = len(t)
    freqs = np.fft.fftfreq(N, 1/2000)[:N//2]

    def calculate_band_energy(signal, freq_bands):
        fft_signal = np.abs(np.fft.fft(signal))[:N//2]
        energies = []
        for low, high in freq_bands:
            mask = (freqs >= low) & (freqs <= high)
            energy = np.sum(fft_signal[mask]**2)
            energies.append(energy)
        return energies

    freq_bands = [(0, 10), (10, 50), (50, 150), (150, 500)]
    band_names = ['0-10Hz', '10-50Hz', '50-150Hz', '150-500Hz']

    energy_amplified = calculate_band_energy(amplified_signal, freq_bands)
    energy_denoised = calculate_band_energy(denoised_signal, freq_bands)

    x = np.arange(len(band_names))
    width = 0.35

    bars1 = ax3.bar(x - width/2, energy_amplified, width, label='放大后',
                   color='orange', alpha=0.7)
    bars2 = ax3.bar(x + width/2, energy_denoised, width, label='降噪后',
                   color='green', alpha=0.7)

    ax3.set_title('各频段能量对比', fontweight='bold')
    ax3.set_xlabel('频率段')
    ax3.set_ylabel('能量')
    ax3.set_xticks(x)
    ax3.set_xticklabels(band_names)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 相关性分析
    ax4 = axes[1, 1]

    # 计算与理想信号的相关性
    ideal_signal = clean_signal * gain_linear

    # 滑动窗口相关性分析
    window_size = 1000
    correlations_amplified = []
    correlations_denoised = []
    time_windows = []

    for i in range(0, len(t) - window_size, window_size//2):
        end_idx = i + window_size
        time_windows.append(t[i + window_size//2])

        corr_amp = np.corrcoef(ideal_signal[i:end_idx], amplified_signal[i:end_idx])[0, 1]
        corr_den = np.corrcoef(ideal_signal[i:end_idx], denoised_signal[i:end_idx])[0, 1]

        correlations_amplified.append(corr_amp)
        correlations_denoised.append(corr_den)

    ax4.plot(time_windows, correlations_amplified, 'o-', color='orange',
            label='放大后信号', linewidth=2, markersize=4)
    ax4.plot(time_windows, correlations_denoised, 's-', color='green',
            label='降噪后信号', linewidth=2, markersize=4)

    ax4.set_title('与理想信号的相关性分析', fontweight='bold')
    ax4.set_xlabel('时间 (秒)')
    ax4.set_ylabel('相关系数')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 1)

    plt.tight_layout()
    plt.savefig('signal_performance_metrics.png', dpi=300, bbox_inches='tight')
    print("信号性能指标分析图已保存为: signal_performance_metrics.png")

    return fig

def main():
    """主函数"""
    print("="*70)
    print("风机叶片内腔微小声信号处理与VI级小波降噪系统")
    print("="*70)

    # 步骤1: 生成风机叶片内腔微小声信号
    print("步骤1: 生成风机叶片内腔微小声信号...")
    t, clean_signal, noisy_signal = generate_blade_cavity_signal()

    # 步骤2: 信号放大
    print("步骤2: 信号放大处理...")
    amplified_signal, gain_linear = amplify_signal(noisy_signal, gain_db=40)

    # 步骤3: VI级小波降噪
    print("步骤3: VI级小波降噪处理...")
    denoised_signal, coeffs_original, coeffs_denoised, thresholds = wavelet_denoise_vi_levels(amplified_signal)

    # 步骤4: 生成可视化图表
    print("步骤4: 生成可视化图表...")

    # 主要处理流程图
    plot_signal_processing_steps(t, clean_signal, noisy_signal, amplified_signal,
                                denoised_signal, gain_linear, thresholds)

    # VI级小波分解详细分析
    plot_wavelet_decomposition_analysis(coeffs_original, coeffs_denoised, thresholds)

    # 性能指标分析
    plot_performance_metrics(t, clean_signal, noisy_signal, amplified_signal,
                           denoised_signal, gain_linear)

    # 计算最终性能指标
    clean_amplified = clean_signal * gain_linear
    snr_before = calculate_signal_metrics(clean_amplified, amplified_signal)[0]
    snr_after = calculate_signal_metrics(clean_amplified, denoised_signal)[0]
    rmse_before = calculate_signal_metrics(clean_amplified, amplified_signal)[1]
    rmse_after = calculate_signal_metrics(clean_amplified, denoised_signal)[1]
    corr_before = calculate_signal_metrics(clean_amplified, amplified_signal)[2]
    corr_after = calculate_signal_metrics(clean_amplified, denoised_signal)[2]

    # 打印结果统计
    print("\n" + "="*70)
    print("风机叶片内腔微小声信号处理结果统计:")
    print("="*70)

    print(f"\n📊 信号特征:")
    print(f"  原始微小信号最大幅度: {max(abs(clean_signal)):.6f} V")
    print(f"  噪声水平: {np.std(noisy_signal - clean_signal):.6f} V")
    print(f"  信号放大增益: {20*np.log10(gain_linear):.1f} dB")
    print(f"  放大后最大幅度: {max(abs(amplified_signal)):.3f} V")

    print(f"\n🔧 VI级小波降噪参数:")
    print(f"  小波基函数: Daubechies 8 (db8)")
    print(f"  分解级数: VI级 (6级)")
    print(f"  阈值模式: 软阈值")
    print(f"  各级阈值: {[f'{th:.3f}' for th in thresholds]}")

    print(f"\n📈 降噪效果:")
    print(f"  降噪前SNR: {snr_before:.2f} dB")
    print(f"  降噪后SNR: {snr_after:.2f} dB")
    print(f"  SNR改善: {snr_after - snr_before:.2f} dB")
    print(f"  降噪前RMSE: {rmse_before:.4f}")
    print(f"  降噪后RMSE: {rmse_after:.4f}")
    print(f"  降噪前相关性: {corr_before:.4f}")
    print(f"  降噪后相关性: {corr_after:.4f}")

    print(f"\n📁 生成的文件:")
    print(f"  - wind_turbine_blade_signal_processing.png (主要处理流程)")
    print(f"  - wavelet_decomposition_vi_levels.png (VI级小波分解详细分析)")
    print(f"  - signal_performance_metrics.png (性能指标分析)")

    print(f"\n✅ 风机叶片内腔微小声信号处理完成！")

    return t, clean_signal, noisy_signal, amplified_signal, denoised_signal

if __name__ == "__main__":
    t, clean_signal, noisy_signal, amplified_signal, denoised_signal = main()
