<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>叶片内腔缺陷数据集展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border-radius: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .defect-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .defect-card {
            border: 3px solid;
            border-radius: 12px;
            padding: 25px;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }

        .defect-card:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .defect-fiber {
            border-color: #FF6B6B;
        }

        .defect-crack {
            border-color: #4ECDC4;
        }

        .defect-delamination {
            border-color: #45B7D1;
        }

        .defect-bulge {
            border-color: #96CEB4;
        }

        .defect-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            color: white;
            text-align: center;
        }

        .defect-fiber .defect-title {
            background-color: #FF6B6B;
        }

        .defect-crack .defect-title {
            background-color: #4ECDC4;
        }

        .defect-delamination .defect-title {
            background-color: #45B7D1;
        }

        .defect-bulge .defect-title {
            background-color: #96CEB4;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-name {
            font-weight: bold;
            color: #333;
        }

        .feature-value {
            color: #666;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .image-container {
            text-align: center;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .image-title {
            font-size: 1.2em;
            font-weight: bold;
            margin: 15px 0 10px 0;
            color: #333;
        }

        .image-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .features-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }

        .features-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .features-table tr:hover {
            background-color: #f8f9fa;
        }

        .download-section {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin: 40px 0;
        }

        .download-btn {
            display: inline-block;
            background-color: white;
            color: #11998e;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .section-title {
            font-size: 2em;
            color: #333;
            margin: 40px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 3px solid #4CAF50;
            text-align: center;
        }

        .highlight-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .defect-types {
                grid-template-columns: 1fr;
            }

            .image-gallery {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔍 叶片内腔缺陷数据集</h1>
            <p>专业的工业缺陷检测与分类数据集</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">30,000</div>
                <div class="stat-label">总样本数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div class="stat-label">缺陷类型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">11</div>
                <div class="stat-label">特征维度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">分类准确率</div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 数据集特点</h3>
            <p>本数据集基于真实工业场景设计，结合了超声检测、热成像、表面分析等多种无损检测技术，为叶片内腔缺陷的自动化检测和分类提供了高质量的训练数据。数据集包含平衡分布的四种主要缺陷类型，每种缺陷都有详细的物理特征描述。
            </p>
        </div>

        <h2 class="section-title">🔬 缺陷类型详解</h2>
        <div class="defect-types">
            <div class="defect-card defect-fiber">
                <div class="defect-title">纤维褶皱 (Fiber Wrinkle)</div>
                <ul class="feature-list">
                    <li><span class="feature-name">频率响应</span><span class="feature-value">2,502 Hz (低频)</span></li>
                    <li><span class="feature-name">信号幅度</span><span class="feature-value">-15.0 dB (中等)</span></li>
                    <li><span class="feature-name">相位偏移</span><span class="feature-value">15.0° (小)</span></li>
                    <li><span class="feature-name">厚度变化</span><span class="feature-value">0.050 mm (轻微)</span></li>
                    <li><span class="feature-name">缺陷面积</span><span class="feature-value">2.08% (小)</span></li>
                </ul>
                <p style="margin-top: 15px; color: #666; font-size: 0.9em;">
                    纤维排列不规则造成的轻微结构异常，常见于复合材料制造过程中的纤维铺层缺陷。
                </p>
            </div>

            <div class="defect-card defect-crack">
                <div class="defect-title">裂纹 (Crack)</div>
                <ul class="feature-list">
                    <li><span class="feature-name">频率响应</span><span class="feature-value">8,516 Hz (高频)</span></li>
                    <li><span class="feature-name">信号幅度</span><span class="feature-value">-5.0 dB (高)</span></li>
                    <li><span class="feature-name">相位偏移</span><span class="feature-value">45.0° (大)</span></li>
                    <li><span class="feature-name">厚度变化</span><span class="feature-value">0.151 mm (显著)</span></li>
                    <li><span class="feature-name">缺陷面积</span><span class="feature-value">1.20% (线性)</span></li>
                </ul>
                <p style="margin-top: 15px; color: #666; font-size: 0.9em;">
                    材料断裂产生的线性缺陷，通常由疲劳载荷或应力集中导致，具有强烈的高频响应特征。
                </p>
            </div>

            <div class="defect-card defect-delamination">
                <div class="defect-title">分层 (Delamination)</div>
                <ul class="feature-list">
                    <li><span class="feature-name">频率响应</span><span class="feature-value">5,199 Hz (中频)</span></li>
                    <li><span class="feature-name">信号幅度</span><span class="feature-value">-24.9 dB (低)</span></li>
                    <li><span class="feature-name">相位偏移</span><span class="feature-value">28.0° (中等)</span></li>
                    <li><span class="feature-name">厚度变化</span><span class="feature-value">0.080 mm (突变)</span></li>
                    <li><span class="feature-name">缺陷面积</span><span class="feature-value">5.48% (大)</span></li>
                </ul>
                <p style="margin-top: 15px; color: #666; font-size: 0.9em;">
                    层间结合失效造成的面状缺陷，在复合材料中表现为层间粘接失效，具有独特的厚度突变特征。
                </p>
            </div>

            <div class="defect-card defect-bulge">
                <div class="defect-title">鼓包 (Bulge)</div>
                <ul class="feature-list">
                    <li><span class="feature-name">频率响应</span><span class="feature-value">1,798 Hz (低频)</span></li>
                    <li><span class="feature-name">信号幅度</span><span class="feature-value">-8.0 dB (高)</span></li>
                    <li><span class="feature-name">相位偏移</span><span class="feature-value">12.0° (小)</span></li>
                    <li><span class="feature-name">厚度变化</span><span class="feature-value">0.250 mm (大)</span></li>
                    <li><span class="feature-name">缺陷面积</span><span class="feature-value">8.24% (最大)</span></li>
                </ul>
                <p style="margin-top: 15px; color: #666; font-size: 0.9em;">
                    材料变形造成的体积缺陷，通常由内部压力或热应力导致，具有最大的厚度变化和缺陷面积。
                </p>
            </div>
        </div>

        <h2 class="section-title">📊 数据可视化</h2>
        <div class="image-gallery">
            <div class="image-container">
                <img src="blade_cavity_defect_dataset_visualization.png" alt="数据集可视化">
                <div class="image-title">数据集综合可视化</div>
                <div class="image-description">
                    展示了各缺陷类型的特征分布、相关性分析、以及衍生特征的分布情况，帮助理解数据集的整体特性。
                </div>
            </div>
            <div class="image-container">
                <img src="blade_defect_classification_results.png" alt="分类结果">
                <div class="image-title">机器学习分类结果</div>
                <div class="image-description">
                    随机森林分类器的性能评估结果，包括混淆矩阵、特征重要性排序和各类别的分类性能指标。
                </div>
            </div>
        </div>

        <h2 class="section-title">📋 特征说明表</h2>
        <table class="features-table">
            <thead>
                <tr>
                    <th>特征名称</th>
                    <th>单位</th>
                    <th>检测方法</th>
                    <th>物理意义</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>频率特征</td>
                    <td>Hz</td>
                    <td>超声波检测</td>
                    <td>超声检测频率响应，反映缺陷的声学特性</td>
                </tr>
                <tr>
                    <td>幅度特征</td>
                    <td>dB</td>
                    <td>超声波检测</td>
                    <td>信号幅度强度，表征缺陷的反射强度</td>
                </tr>
                <tr>
                    <td>相位特征</td>
                    <td>度</td>
                    <td>超声波检测</td>
                    <td>相位偏移角度，反映声波传播路径变化</td>
                </tr>
                <tr>
                    <td>厚度变化</td>
                    <td>mm</td>
                    <td>超声测厚</td>
                    <td>壁厚变化量，直接反映缺陷的几何特征</td>
                </tr>
                <tr>
                    <td>表面粗糙度</td>
                    <td>μm</td>
                    <td>表面轮廓仪</td>
                    <td>表面质量参数，反映缺陷对表面的影响</td>
                </tr>
                <tr>
                    <td>温度分布</td>
                    <td>°C</td>
                    <td>红外热成像</td>
                    <td>热成像温度差异，反映缺陷的热学特性</td>
                </tr>
                <tr>
                    <td>应力集中系数</td>
                    <td>无量纲</td>
                    <td>应力分析</td>
                    <td>应力分布特征，表征缺陷引起的应力集中</td>
                </tr>
                <tr>
                    <td>缺陷面积比</td>
                    <td>%</td>
                    <td>图像分析</td>
                    <td>缺陷占总面积比例，量化缺陷的规模</td>
                </tr>
            </tbody>
        </table>

        <div class="download-section">
            <h3>📥 数据集下载</h3>
            <p>获取完整的叶片内腔缺陷数据集，包含所有特征数据、可视化图表和详细说明文档</p>
            <a href="blade_cavity_defects_30k.csv" class="download-btn" download>📊 下载CSV数据集 (30K)</a>
            <a href="叶片内腔缺陷数据集说明.md" class="download-btn" download>📖 下载说明文档</a>
            <a href="blade_cavity_defect_dataset.py" class="download-btn" download>💻 下载生成脚本</a>
        </div>

        <div
            style="text-align: center; margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <p style="color: #666; margin: 0;">
                <strong>数据集版本:</strong> v2.0 |
                <strong>生成日期:</strong> 2025-08-15 |
                <strong>样本总数:</strong> 30,000 |
                <strong>分类准确率:</strong> 100%
            </p>
        </div>
    </div>
</body>

</html>