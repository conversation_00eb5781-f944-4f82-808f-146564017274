import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle
import seaborn as sns
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_severity_overview():
    """创建严重程度总览图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 严重程度定义
    severity_levels = {
        1: {'name': '轻微', 'color': '#2ECC71', 'action': '继续监测', 'risk': '低风险'},
        2: {'name': '中等', 'color': '#F39C12', 'action': '加强监测', 'risk': '中风险'},
        3: {'name': '严重', 'color': '#E67E22', 'action': '计划维修', 'risk': '高风险'},
        4: {'name': '危险', 'color': '#E74C3C', 'action': '立即停机', 'risk': '极高风险'}
    }
    
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    defect_colors = ['red', 'gold', 'blue', 'green']
    
    # 生成示例数据
    np.random.seed(42)
    
    # 1. 严重程度分级饼图
    ax1 = axes[0, 0]
    severity_counts = [60, 60, 60, 60]  # 每个等级60个样本
    colors = [severity_levels[i]['color'] for i in range(1, 5)]
    labels = [f"等级{i}\n{severity_levels[i]['name']}" for i in range(1, 5)]
    
    wedges, texts, autotexts = ax1.pie(severity_counts, labels=labels, autopct='%1.1f%%',
                                      colors=colors, startangle=90, textprops={'fontsize': 11})
    ax1.set_title('缺陷严重程度分级分布', fontsize=14, fontweight='bold')
    
    # 2. 缺陷类型严重程度矩阵
    ax2 = axes[0, 1]
    
    # 创建模拟的严重程度矩阵数据
    severity_matrix = np.array([
        [15, 35, 65, 90],  # 裂纹
        [12, 28, 55, 85],  # 纤维褶皱
        [18, 38, 70, 95],  # 分层
        [10, 25, 50, 80]   # 鼓包
    ])
    
    im = ax2.imshow(severity_matrix, cmap='Reds', aspect='auto')
    ax2.set_title('缺陷类型-严重程度风险矩阵', fontsize=14, fontweight='bold')
    ax2.set_xlabel('严重程度等级')
    ax2.set_ylabel('缺陷类型')
    ax2.set_xticks(range(4))
    ax2.set_yticks(range(4))
    ax2.set_xticklabels(['等级1', '等级2', '等级3', '等级4'])
    ax2.set_yticklabels(defect_types)
    
    # 添加数值标签
    for i in range(4):
        for j in range(4):
            text = ax2.text(j, i, f'{severity_matrix[i, j]:.0f}',
                           ha="center", va="center", color="white", fontweight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax2)
    cbar.set_label('风险评分', rotation=270, labelpad=15)
    
    # 3. 信号幅值与严重程度关系
    ax3 = axes[1, 0]
    
    for i, (defect_type, color) in enumerate(zip(defect_types, defect_colors)):
        # 生成模拟数据：信号幅值随严重程度增加
        severities = np.repeat(range(1, 5), 15)  # 每个等级15个样本
        base_amplitudes = {1: 1.5, 2: 3.0, 3: 5.5, 4: 8.5}
        amplitudes = []
        
        for sev in severities:
            base_amp = base_amplitudes[sev] * (1 + i * 0.1)  # 不同缺陷类型略有差异
            amp = base_amp + np.random.normal(0, base_amp * 0.15)
            amplitudes.append(max(0.1, amp))
        
        # 添加抖动避免重叠
        jittered_severities = severities + np.random.normal(0, 0.05, len(severities))
        
        ax3.scatter(jittered_severities, amplitudes, label=defect_type, 
                   color=color, alpha=0.7, s=40)
    
    ax3.set_title('信号幅值与严重程度关系', fontsize=14, fontweight='bold')
    ax3.set_xlabel('严重程度等级')
    ax3.set_ylabel('信号幅值')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_xticks(range(1, 5))
    
    # 4. 维修策略决策图
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 创建维修策略可视化
    strategy_y_positions = [0.8, 0.6, 0.4, 0.2]
    
    for i, (severity, info) in enumerate(severity_levels.items()):
        y_pos = strategy_y_positions[i]
        
        # 严重程度标识圆圈
        circle = plt.Circle((0.1, y_pos), 0.05, facecolor=info['color'], 
                           edgecolor='black', linewidth=2)
        ax4.add_patch(circle)
        
        # 等级文本
        ax4.text(0.2, y_pos, f"等级{severity}", fontsize=12, fontweight='bold', 
                va='center', color=info['color'])
        
        # 严重程度名称
        ax4.text(0.35, y_pos, info['name'], fontsize=11, va='center', fontweight='bold')
        
        # 维修策略
        ax4.text(0.5, y_pos, info['action'], fontsize=11, va='center')
        
        # 风险等级
        ax4.text(0.7, y_pos, info['risk'], fontsize=11, va='center', 
                style='italic', color=info['color'])
        
        # 分隔线
        if i < 3:
            ax4.axhline(y=y_pos - 0.1, xmin=0.05, xmax=0.95, color='gray', 
                       linestyle='--', alpha=0.5)
    
    ax4.set_title('维修策略决策表', fontsize=14, fontweight='bold')
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    
    # 添加表头
    ax4.text(0.1, 0.95, '等级', fontsize=12, fontweight='bold', ha='center')
    ax4.text(0.35, 0.95, '程度', fontsize=12, fontweight='bold', ha='center')
    ax4.text(0.5, 0.95, '维修策略', fontsize=12, fontweight='bold', ha='center')
    ax4.text(0.7, 0.95, '风险等级', fontsize=12, fontweight='bold', ha='center')
    
    plt.tight_layout()
    plt.savefig('defect_severity_overview.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 严重程度总览图已生成: defect_severity_overview.png")

def create_detailed_severity_charts():
    """创建详细的严重程度图表"""
    
    # 严重程度定义
    severity_levels = {
        1: {'name': '轻微', 'color': '#2ECC71'},
        2: {'name': '中等', 'color': '#F39C12'},
        3: {'name': '严重', 'color': '#E67E22'},
        4: {'name': '危险', 'color': '#E74C3C'}
    }
    
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    
    # 创建2x2子图，每个子图展示一种缺陷的严重程度
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    fig.suptitle('四种缺陷类型的严重程度分级标注', fontsize=20, fontweight='bold')
    
    np.random.seed(42)
    
    for i, defect_type in enumerate(defect_types):
        ax = axes[i]
        
        # 为每个严重程度生成示例数据
        x_positions = []
        y_values = []
        colors = []
        sizes = []
        
        for severity in range(1, 5):
            # 生成该严重程度的样本
            num_samples = 20
            
            if defect_type == '裂纹':
                # 裂纹：长度 vs 深度
                base_length = {1: 3, 2: 10, 3: 25, 4: 60}[severity]
                base_depth = {1: 0.5, 2: 2.5, 3: 6, 4: 15}[severity]
                
                for _ in range(num_samples):
                    length = base_length + np.random.normal(0, base_length * 0.2)
                    depth = base_depth + np.random.normal(0, base_depth * 0.2)
                    
                    x_positions.append(max(0.1, length))
                    y_values.append(max(0.1, depth))
                    colors.append(severity_levels[severity]['color'])
                    sizes.append(30 + severity * 15)
            
            elif defect_type == '纤维褶皱':
                # 纤维褶皱：面积 vs 高度
                base_area = {1: 8, 2: 25, 3: 90, 4: 220}[severity]
                base_height = {1: 1.2, 2: 3.5, 3: 8.5, 4: 18}[severity]
                
                for _ in range(num_samples):
                    area = base_area + np.random.normal(0, base_area * 0.2)
                    height = base_height + np.random.normal(0, base_height * 0.2)
                    
                    x_positions.append(max(0.1, area))
                    y_values.append(max(0.1, height))
                    colors.append(severity_levels[severity]['color'])
                    sizes.append(30 + severity * 15)
            
            elif defect_type == '分层':
                # 分层：面积 vs 厚度
                base_area = {1: 5, 2: 18, 3: 70, 4: 180}[severity]
                base_thickness = {1: 0.3, 2: 1.2, 3: 3.5, 4: 9}[severity]
                
                for _ in range(num_samples):
                    area = base_area + np.random.normal(0, base_area * 0.2)
                    thickness = base_thickness + np.random.normal(0, base_thickness * 0.2)
                    
                    x_positions.append(max(0.1, area))
                    y_values.append(max(0.1, thickness))
                    colors.append(severity_levels[severity]['color'])
                    sizes.append(30 + severity * 15)
            
            else:  # 鼓包
                # 鼓包：直径 vs 高度
                base_diameter = {1: 3, 2: 8, 3: 18, 4: 35}[severity]
                base_height = {1: 1.5, 2: 5, 3: 12, 4: 28}[severity]
                
                for _ in range(num_samples):
                    diameter = base_diameter + np.random.normal(0, base_diameter * 0.2)
                    height = base_height + np.random.normal(0, base_height * 0.2)
                    
                    x_positions.append(max(0.1, diameter))
                    y_values.append(max(0.1, height))
                    colors.append(severity_levels[severity]['color'])
                    sizes.append(30 + severity * 15)
        
        # 绘制散点图
        scatter = ax.scatter(x_positions, y_values, c=colors, s=sizes, alpha=0.7, edgecolors='black', linewidth=0.5)
        
        # 设置标题和标签
        ax.set_title(f'{defect_type}缺陷严重程度分级', fontsize=14, fontweight='bold')
        
        if defect_type == '裂纹':
            ax.set_xlabel('裂纹长度 (mm)')
            ax.set_ylabel('裂纹深度 (mm)')
        elif defect_type == '纤维褶皱':
            ax.set_xlabel('褶皱面积 (cm²)')
            ax.set_ylabel('褶皱高度 (mm)')
        elif defect_type == '分层':
            ax.set_xlabel('分层面积 (cm²)')
            ax.set_ylabel('分层厚度 (mm)')
        else:  # 鼓包
            ax.set_xlabel('鼓包直径 (cm)')
            ax.set_ylabel('鼓包高度 (mm)')
        
        ax.grid(True, alpha=0.3)
        
        # 添加严重程度区域划分线
        if defect_type == '裂纹':
            ax.axvline(x=5, color='orange', linestyle='--', alpha=0.7, label='中等阈值')
            ax.axvline(x=15, color='red', linestyle='--', alpha=0.7, label='严重阈值')
            ax.axvline(x=35, color='darkred', linestyle='--', alpha=0.7, label='危险阈值')
        
        # 添加图例
        legend_elements = []
        for severity, info in severity_levels.items():
            legend_elements.append(plt.scatter([], [], c=info['color'], s=100, 
                                             label=f"等级{severity} ({info['name']})"))
        ax.legend(handles=legend_elements, loc='upper left', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('defect_severity_detailed_classification.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 详细严重程度分类图已生成: defect_severity_detailed_classification.png")

def create_severity_decision_tree():
    """创建严重程度判定决策树"""
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 标题
    ax.text(5, 7.5, '缺陷严重程度判定决策树', fontsize=20, fontweight='bold', ha='center')
    
    # 根节点
    root_box = FancyBboxPatch((4, 6.5), 2, 0.6, boxstyle="round,pad=0.1", 
                             facecolor='lightblue', edgecolor='black', linewidth=2)
    ax.add_patch(root_box)
    ax.text(5, 6.8, '缺陷检测', fontsize=12, fontweight='bold', ha='center')
    
    # 第一层分支：缺陷类型
    defect_positions = [(1.5, 5.5), (3.5, 5.5), (6.5, 5.5), (8.5, 5.5)]
    defect_colors = ['red', 'gold', 'blue', 'green']
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    
    for i, ((x, y), color, defect_type) in enumerate(zip(defect_positions, defect_colors, defect_types)):
        # 缺陷类型节点
        defect_box = FancyBboxPatch((x-0.4, y-0.3), 0.8, 0.6, boxstyle="round,pad=0.05", 
                                   facecolor=color, alpha=0.8, edgecolor='black')
        ax.add_patch(defect_box)
        ax.text(x, y, defect_type, fontsize=10, fontweight='bold', ha='center', color='white')
        
        # 连接到根节点的线
        ax.plot([5, x], [6.5, y+0.3], 'k-', linewidth=2)
        
        # 第二层分支：严重程度
        severity_colors = ['#2ECC71', '#F39C12', '#E67E22', '#E74C3C']
        severity_names = ['轻微', '中等', '严重', '危险']
        
        for j, (sev_color, sev_name) in enumerate(zip(severity_colors, severity_names)):
            sev_x = x + (j - 1.5) * 0.3
            sev_y = y - 1.2
            
            # 严重程度节点
            sev_circle = Circle((sev_x, sev_y), 0.15, facecolor=sev_color, 
                               edgecolor='black', linewidth=1)
            ax.add_patch(sev_circle)
            ax.text(sev_x, sev_y, f'{j+1}', fontsize=9, fontweight='bold', 
                   ha='center', va='center', color='white')
            
            # 连接线
            ax.plot([x, sev_x], [y-0.3, sev_y+0.15], 'k-', linewidth=1)
            
            # 严重程度标签
            ax.text(sev_x, sev_y-0.4, sev_name, fontsize=8, ha='center', va='center')
    
    # 添加判定标准说明
    criteria_text = """
    判定标准说明:
    
    🔥 裂纹缺陷:
    等级1: 长度<5mm, 深度<1mm
    等级2: 长度5-15mm, 深度1-3mm  
    等级3: 长度15-35mm, 深度3-8mm
    等级4: 长度>35mm, 深度>8mm
    
    🌊 纤维褶皱:
    等级1: 面积<15cm², 高度<2mm
    等级2: 面积15-50cm², 高度2-5mm
    等级3: 面积50-150cm², 高度5-12mm
    等级4: 面积>150cm², 高度>12mm
    """
    
    ax.text(0.5, 2.5, criteria_text, fontsize=10, ha='left', va='top',
           bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
    
    criteria_text2 = """
    📈 分层缺陷:
    等级1: 面积<10cm², 厚度<0.5mm
    等级2: 面积10-40cm², 厚度0.5-2mm
    等级3: 面积40-120cm², 厚度2-5mm  
    等级4: 面积>120cm², 厚度>5mm
    
    🎈 鼓包缺陷:
    等级1: 直径<5cm, 高度<3mm
    等级2: 直径5-12cm, 高度3-8mm
    等级3: 直径12-25cm, 高度8-20mm
    等级4: 直径>25cm, 高度>20mm
    """
    
    ax.text(5.5, 2.5, criteria_text2, fontsize=10, ha='left', va='top',
           bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('severity_decision_tree.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 严重程度决策树已生成: severity_decision_tree.png")

def create_risk_assessment_matrix():
    """创建风险评估矩阵"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 风险评估矩阵
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    severity_levels = ['轻微', '中等', '严重', '危险']
    
    # 创建风险评分矩阵
    risk_matrix = np.array([
        [18, 42, 78, 95],  # 裂纹
        [15, 35, 65, 88],  # 纤维褶皱
        [20, 45, 82, 98],  # 分层
        [12, 30, 58, 85]   # 鼓包
    ])
    
    # 绘制热力图
    im1 = ax1.imshow(risk_matrix, cmap='RdYlGn_r', aspect='auto', vmin=0, vmax=100)
    ax1.set_title('缺陷风险评估矩阵', fontsize=16, fontweight='bold')
    ax1.set_xlabel('严重程度等级', fontsize=12)
    ax1.set_ylabel('缺陷类型', fontsize=12)
    ax1.set_xticks(range(4))
    ax1.set_yticks(range(4))
    ax1.set_xticklabels(severity_levels)
    ax1.set_yticklabels(defect_types)
    
    # 添加数值和颜色编码
    for i in range(4):
        for j in range(4):
            risk_score = risk_matrix[i, j]
            text_color = 'white' if risk_score > 50 else 'black'
            
            # 风险评分
            ax1.text(j, i, f'{risk_score}', ha="center", va="center", 
                    color=text_color, fontweight='bold', fontsize=12)
            
            # 风险等级标识
            if risk_score < 25:
                risk_level = '低'
            elif risk_score < 50:
                risk_level = '中'
            elif risk_score < 80:
                risk_level = '高'
            else:
                risk_level = '极高'
            
            ax1.text(j, i-0.2, f'({risk_level})', ha="center", va="center", 
                    color=text_color, fontsize=9, style='italic')
    
    # 添加颜色条
    cbar1 = plt.colorbar(im1, ax=ax1)
    cbar1.set_label('风险评分 (0-100)', rotation=270, labelpad=15)
    
    # 维修紧急程度矩阵
    urgency_matrix = np.array([
        [1.1, 2.2, 3.3, 4.4],  # 裂纹 (稍高)
        [1.0, 2.0, 3.0, 4.0],  # 纤维褶皱
        [1.05, 2.1, 3.15, 4.2],  # 分层 (稍高)
        [1.0, 2.0, 3.0, 4.0]   # 鼓包
    ], dtype=float)
    
    im2 = ax2.imshow(urgency_matrix, cmap='Oranges', aspect='auto', vmin=1, vmax=4)
    ax2.set_title('维修紧急程度矩阵', fontsize=16, fontweight='bold')
    ax2.set_xlabel('严重程度等级', fontsize=12)
    ax2.set_ylabel('缺陷类型', fontsize=12)
    ax2.set_xticks(range(4))
    ax2.set_yticks(range(4))
    ax2.set_xticklabels(severity_levels)
    ax2.set_yticklabels(defect_types)
    
    # 添加维修策略标签
    strategies = [
        ['继续监测', '继续监测', '继续监测', '继续监测'],
        ['加强监测', '加强监测', '加强监测', '加强监测'],
        ['计划维修', '计划维修', '计划维修', '计划维修'],
        ['立即停机', '立即停机', '立即停机', '立即停机']
    ]
    
    for i in range(4):
        for j in range(4):
            strategy = strategies[j][i]
            ax2.text(j, i, strategy, ha="center", va="center", 
                    color='black', fontweight='bold', fontsize=9)
    
    # 添加颜色条
    cbar2 = plt.colorbar(im2, ax=ax2)
    cbar2.set_label('紧急程度 (1-4)', rotation=270, labelpad=15)
    
    plt.tight_layout()
    plt.savefig('risk_assessment_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 风险评估矩阵已生成: risk_assessment_matrix.png")

def main():
    """主函数"""
    print("🎨 开始创建缺陷严重程度分级标注可视化...")
    
    # 1. 创建严重程度总览
    print("1. 生成严重程度总览图...")
    create_severity_overview()
    
    # 2. 创建详细分类图表
    print("2. 生成详细严重程度分类图...")
    create_detailed_severity_charts()
    
    # 3. 创建决策树
    print("3. 生成严重程度决策树...")
    create_severity_decision_tree()
    
    # 4. 创建风险评估矩阵
    print("4. 生成风险评估矩阵...")
    create_risk_assessment_matrix()
    
    print("\n🎉 所有严重程度标注可视化图表生成完成！")
    print("生成的文件:")
    print("• defect_severity_overview.png - 严重程度总览图")
    print("• defect_severity_detailed_classification.png - 详细分类图")
    print("• severity_decision_tree.png - 严重程度决策树")
    print("• risk_assessment_matrix.png - 风险评估矩阵")


if __name__ == "__main__":
    main()
