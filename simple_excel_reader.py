"""
简单的Excel文件读取示例
快速上手读取Excel文件
"""

import pandas as pd
import os

def read_excel_simple(file_path):
    """
    简单读取Excel文件
    
    参数:
    - file_path: Excel文件路径
    
    返回:
    - DataFrame: 读取的数据
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return None
        
        # 读取Excel文件（默认读取第一个工作表）
        df = pd.read_excel(file_path)
        
        print(f"成功读取Excel文件: {file_path}")
        print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
        print(f"列名: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return None

def show_excel_info(file_path):
    """显示Excel文件的基本信息"""
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"Excel文件: {file_path}")
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")
        
        # 读取每个工作表的基本信息
        for sheet_name in sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"\n工作表 '{sheet_name}':")
            print(f"  - 形状: {df.shape}")
            print(f"  - 列名: {list(df.columns)}")
            
    except Exception as e:
        print(f"获取Excel信息时出错: {str(e)}")

# 使用示例
if __name__ == "__main__":
    print("Excel文件读取示例")
    print("=" * 40)
    
    # 请将下面的文件路径替换为你的实际Excel文件路径
    excel_file_path = "your_excel_file.xlsx"  # 替换为你的Excel文件路径
    
    # 如果你有具体的Excel文件，可以直接指定路径，例如：
    # excel_file_path = "data.xlsx"
    # excel_file_path = "C:/Users/<USER>/Documents/data.xlsx"
    
    # 检查文件是否存在
    if os.path.exists(excel_file_path):
        # 显示Excel文件信息
        show_excel_info(excel_file_path)
        
        # 读取Excel文件
        data = read_excel_simple(excel_file_path)
        
        if data is not None:
            print("\n前5行数据:")
            print(data.head())
            
            print("\n数据类型:")
            print(data.dtypes)
            
            print("\n缺失值统计:")
            print(data.isnull().sum())
    else:
        print(f"请将 excel_file_path 变量设置为你的实际Excel文件路径")
        print("当前设置的路径:", excel_file_path)
        
        # 显示一些常用的读取方法
        print("\n常用的Excel读取方法:")
        print("1. 读取第一个工作表:")
        print("   df = pd.read_excel('file.xlsx')")
        
        print("\n2. 读取指定工作表:")
        print("   df = pd.read_excel('file.xlsx', sheet_name='Sheet1')")
        
        print("\n3. 读取所有工作表:")
        print("   data_dict = pd.read_excel('file.xlsx', sheet_name=None)")
        
        print("\n4. 指定标题行:")
        print("   df = pd.read_excel('file.xlsx', header=1)  # 第2行作为标题")
        
        print("\n5. 读取指定列:")
        print("   df = pd.read_excel('file.xlsx', usecols='A:C')  # 只读取A到C列")
        print("   df = pd.read_excel('file.xlsx', usecols=[0, 1, 2])  # 读取前3列")
        
        print("\n6. 跳过行:")
        print("   df = pd.read_excel('file.xlsx', skiprows=2)  # 跳过前2行")
