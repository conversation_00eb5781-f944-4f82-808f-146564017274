# -*- coding: utf-8 -*-
"""
Generate a 2D cross-section schematic of a wind turbine blade showing internal cavity.
- Outer/inner shell (hollow), and two shear webs (腹板) at specified chordwise positions.
- Default airfoil: NACA 4418 (typical thicker mid-span section)
- Saves PNG to output/blade_section_internal.png

No extra dependencies beyond numpy + matplotlib.
"""

import os
import argparse
import numpy as np
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt


def parse_args():
    p = argparse.ArgumentParser(description="Render a 2D cross-section with internal cavity and webs.")
    p.add_argument("--naca", type=str, default="4418", help="NACA 4-digit code, e.g., 2412, 0012, 4418")
    p.add_argument("--chord", type=float, default=3.2, help="Chord length (m)")
    p.add_argument("--shell", type=float, default=0.03, help="Outer-to-inner shell thickness (m)")
    p.add_argument("--webs", type=float, nargs="*", default=[0.25, 0.45], help="Web positions as fraction of chord (e.g., 0.25 0.45)")
    p.add_argument("--outfile", type=str, default="output/blade_section_internal.png", help="Output PNG path")
    p.add_argument("--resolution", type=int, default=500, help="Number of chordwise points")
    return p.parse_args()


def naca4_profile(code: str, n_points: int = 400):
    if len(code) != 4 or not code.isdigit():
        raise ValueError("NACA code must be 4 digits, e.g., '4418'.")
    m = int(code[0]) / 100.0
    p = int(code[1]) / 10.0
    t = int(code[2:]) / 100.0

    beta = np.linspace(0.0, np.pi, n_points)
    x = (1 - np.cos(beta)) / 2.0

    yt = 5 * t * (
        0.2969 * np.sqrt(x)
        - 0.1260 * x
        - 0.3516 * x**2
        + 0.2843 * x**3
        - 0.1015 * x**4
    )

    yc = np.zeros_like(x)
    dyc_dx = np.zeros_like(x)

    for i, xi in enumerate(x):
        if p == 0:
            yc[i] = 0.0
            dyc_dx[i] = 0.0
        elif xi < p:
            yc[i] = (m / p**2) * (2 * p * xi - xi**2)
            dyc_dx[i] = (2 * m / p**2) * (p - xi)
        else:
            yc[i] = (m / (1 - p) ** 2) * ((1 - 2 * p) + 2 * p * xi - xi**2)
            dyc_dx[i] = (2 * m / (1 - p) ** 2) * (p - xi)

    theta = np.arctan(dyc_dx)

    xu = x - yt * np.sin(theta)
    yu = yc + yt * np.cos(theta)
    xl = x + yt * np.sin(theta)
    yl = yc - yt * np.cos(theta)

    return x, yt, yc, theta, xu, yu, xl, yl


def inner_surface(x, yt, yc, theta, chord: float, shell_thickness_m: float):
    # Convert shell thickness to non-dimensional half-thickness delta
    delta = max(shell_thickness_m / chord, 1e-5)
    yt_in = np.maximum(yt - delta, 1e-6)

    xu_in = x - yt_in * np.sin(theta)
    yu_in = yc + yt_in * np.cos(theta)
    xl_in = x + yt_in * np.sin(theta)
    yl_in = yc - yt_in * np.cos(theta)

    return xu_in, yu_in, xl_in, yl_in


def render_internal_section(naca: str, chord: float, shell: float, web_positions, resolution: int, outfile: str):
    x, yt, yc, theta, xu, yu, xl, yl = naca4_profile(naca, n_points=resolution)
    xu_in, yu_in, xl_in, yl_in = inner_surface(x, yt, yc, theta, chord, shell)

    # Scale to meters
    xu_m, yu_m = xu * chord, yu * chord
    xl_m, yl_m = xl * chord, yl * chord
    xu_in_m, yu_in_m = xu_in * chord, yu_in * chord
    xl_in_m, yl_in_m = xl_in * chord, yl_in * chord

    fig, ax = plt.subplots(figsize=(10, 4), dpi=150)

    # Fill shell thickness areas (upper and lower)
    ax.fill_between(xu_m, yu_m, yu_in_m, color="#D0D3D4", alpha=1.0, label="Outer/Inner Shell")
    ax.fill_between(xl_m, yl_in_m, yl_m, color="#D0D3D4", alpha=1.0)

    # Draw cavity (implicitly the white area between inner upper and inner lower)
    ax.plot(xu_in_m, yu_in_m, color="#7B7D7D", linewidth=1.2)
    ax.plot(xl_in_m, yl_in_m, color="#7B7D7D", linewidth=1.2)

    # Outer outline
    ax.plot(xu_m, yu_m, color="#2C3E50", linewidth=1.4, label="Outer Surface")
    ax.plot(xl_m, yl_m, color="#2C3E50", linewidth=1.4)

    # Shear webs at specified chord fractions
    for wp in web_positions:
        x_target = wp * chord
        # Find closest points on inner upper/lower
        iu = np.argmin(np.abs(xu_in_m - x_target))
        il = np.argmin(np.abs(xl_in_m - x_target))
        x_web = 0.5 * (xu_in_m[iu] + xl_in_m[il])
        y_top = yu_in_m[iu]
        y_bot = yl_in_m[il]
        ax.plot([x_web, x_web], [y_bot, y_top], color="#566573", linewidth=6, solid_capstyle='round', label="Shear Web" if wp == web_positions[0] else None)

    # Optional: indicate spar cap region between webs on inner surfaces
    if len(web_positions) >= 2:
        x1, x2 = [w * chord for w in (min(web_positions), max(web_positions))]
        # Upper cap segment indices
        mask_u = (xu_in_m >= x1) & (xu_in_m <= x2)
        mask_l = (xl_in_m >= x1) & (xl_in_m <= x2)
        ax.plot(xu_in_m[mask_u], yu_in_m[mask_u], color="#1ABC9C", linewidth=4, alpha=0.9, label="Spar Cap (upper)")
        ax.plot(xl_in_m[mask_l], yl_in_m[mask_l], color="#1ABC9C", linewidth=4, alpha=0.9, label="Spar Cap (lower)")

    ax.set_aspect('equal', adjustable='box')
    pad = 0.05 * chord
    ax.set_xlim(-pad, chord + pad)
    y_all = np.concatenate([yu_m, yl_m])
    y_min, y_max = y_all.min(), y_all.max()
    y_pad = 0.15 * (y_max - y_min)
    ax.set_ylim(y_min - y_pad, y_max + y_pad)

    ax.set_title(f"Blade Cross-Section with Internal Cavity (NACA {naca}, chord={chord:.2f} m, shell={shell*1000:.0f} mm)")
    ax.set_xlabel("Chordwise X (m)")
    ax.set_ylabel("Thickness/Camber Z (m)")
    ax.grid(True, alpha=0.25)
    ax.legend(loc='upper right')

    outdir = os.path.dirname(outfile)
    if outdir and not os.path.exists(outdir):
        os.makedirs(outdir, exist_ok=True)

    plt.tight_layout()
    fig.savefig(outfile, bbox_inches='tight')
    plt.close(fig)
    print(f"Saved internal cross-section image to: {outfile}")


if __name__ == "__main__":
    args = parse_args()
    render_internal_section(naca=args.naca, chord=args.chord, shell=args.shell, web_positions=args.webs, resolution=args.resolution, outfile=args.outfile)

