"""
叶片内腔缺陷数据集使用示例
演示如何加载和使用30,000个样本的数据集进行机器学习任务
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_dataset(filename='blade_cavity_defects_30k.csv'):
    """加载数据集"""
    print(f"正在加载数据集: {filename}")
    start_time = time.time()
    
    df = pd.read_csv(filename, encoding='utf-8-sig')
    
    load_time = time.time() - start_time
    print(f"数据集加载完成，耗时: {load_time:.2f}秒")
    print(f"数据集大小: {df.shape}")
    
    return df

def explore_dataset(df):
    """探索数据集"""
    print("\n" + "="*60)
    print("数据集探索")
    print("="*60)
    
    # 基本信息
    print(f"样本数量: {len(df):,}")
    print(f"特征数量: {len(df.columns) - 2}")  # 减去缺陷类型和编码列
    print(f"缺陷类型: {df['缺陷类型'].unique()}")
    
    # 类别分布
    print(f"\n各缺陷类型分布:")
    class_counts = df['缺陷类型'].value_counts()
    for defect_type, count in class_counts.items():
        percentage = count / len(df) * 100
        print(f"  {defect_type}: {count:,} 个样本 ({percentage:.1f}%)")
    
    # 缺失值检查
    missing_values = df.isnull().sum()
    if missing_values.sum() > 0:
        print(f"\n缺失值情况:")
        for col, missing in missing_values.items():
            if missing > 0:
                print(f"  {col}: {missing} 个缺失值")
    else:
        print(f"\n✅ 数据集无缺失值")
    
    return class_counts

def prepare_data(df):
    """准备训练数据"""
    print("\n正在准备训练数据...")
    
    # 分离特征和标签
    feature_columns = [col for col in df.columns if col not in ['缺陷类型', '缺陷编码']]
    X = df[feature_columns].values
    y = df['缺陷编码'].values
    
    print(f"特征维度: {X.shape}")
    print(f"标签维度: {y.shape}")
    
    return X, y, feature_columns

def compare_classifiers(X, y, feature_names):
    """比较不同分类器的性能"""
    print("\n" + "="*60)
    print("分类器性能对比")
    print("="*60)
    
    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集大小: {len(X_train):,}")
    print(f"测试集大小: {len(X_test):,}")
    
    # 定义分类器
    classifiers = {
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        '支持向量机': SVC(random_state=42),
        '逻辑回归': LogisticRegression(random_state=42, max_iter=1000)
    }
    
    results = {}
    
    for name, clf in classifiers.items():
        print(f"\n正在训练 {name}...")
        start_time = time.time()
        
        # 训练模型
        clf.fit(X_train, y_train)
        train_time = time.time() - start_time
        
        # 预测
        start_time = time.time()
        y_pred = clf.predict(X_test)
        predict_time = time.time() - start_time
        
        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        
        results[name] = {
            'accuracy': accuracy,
            'train_time': train_time,
            'predict_time': predict_time,
            'y_pred': y_pred
        }
        
        print(f"  准确率: {accuracy:.4f}")
        print(f"  训练时间: {train_time:.2f}秒")
        print(f"  预测时间: {predict_time:.4f}秒")
    
    return results, X_test, y_test

def visualize_results(results, X_test, y_test):
    """可视化结果"""
    print("\n正在生成可视化图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 准确率对比
    names = list(results.keys())
    accuracies = [results[name]['accuracy'] for name in names]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    bars = axes[0, 0].bar(names, accuracies, color=colors, alpha=0.8)
    axes[0, 0].set_title('分类器准确率对比')
    axes[0, 0].set_ylabel('准确率')
    axes[0, 0].set_ylim(0, 1)
    axes[0, 0].grid(True, alpha=0.3)
    
    # 在柱状图上添加数值
    for bar, acc in zip(bars, accuracies):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                       f'{acc:.3f}', ha='center', va='bottom')
    
    # 2. 训练时间对比
    train_times = [results[name]['train_time'] for name in names]
    axes[0, 1].bar(names, train_times, color=colors, alpha=0.8)
    axes[0, 1].set_title('训练时间对比')
    axes[0, 1].set_ylabel('时间 (秒)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 混淆矩阵 (使用随机森林的结果)
    rf_pred = results['随机森林']['y_pred']
    cm = confusion_matrix(y_test, rf_pred)
    defect_labels = ['纤维褶皱', '裂纹', '分层', '鼓包']
    
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
               xticklabels=defect_labels, yticklabels=defect_labels,
               ax=axes[1, 0])
    axes[1, 0].set_title('随机森林混淆矩阵')
    axes[1, 0].set_xlabel('预测类别')
    axes[1, 0].set_ylabel('真实类别')
    
    # 4. 预测时间对比
    predict_times = [results[name]['predict_time'] for name in names]
    axes[1, 1].bar(names, predict_times, color=colors, alpha=0.8)
    axes[1, 1].set_title('预测时间对比')
    axes[1, 1].set_ylabel('时间 (秒)')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('classifier_comparison_results.png', dpi=300, bbox_inches='tight')
    print("对比结果图表已保存为: classifier_comparison_results.png")
    
    return fig

def generate_performance_report(results):
    """生成性能报告"""
    print("\n" + "="*60)
    print("性能报告总结")
    print("="*60)
    
    # 找出最佳分类器
    best_classifier = max(results.keys(), key=lambda x: results[x]['accuracy'])
    best_accuracy = results[best_classifier]['accuracy']
    
    print(f"🏆 最佳分类器: {best_classifier}")
    print(f"🎯 最高准确率: {best_accuracy:.4f}")
    
    # 速度对比
    fastest_train = min(results.keys(), key=lambda x: results[x]['train_time'])
    fastest_predict = min(results.keys(), key=lambda x: results[x]['predict_time'])
    
    print(f"⚡ 训练最快: {fastest_train} ({results[fastest_train]['train_time']:.2f}秒)")
    print(f"🚀 预测最快: {fastest_predict} ({results[fastest_predict]['predict_time']:.4f}秒)")
    
    # 详细性能表
    print(f"\n详细性能对比:")
    print(f"{'分类器':<10} {'准确率':<10} {'训练时间(秒)':<12} {'预测时间(秒)':<12}")
    print("-" * 50)
    for name in results.keys():
        acc = results[name]['accuracy']
        train_t = results[name]['train_time']
        pred_t = results[name]['predict_time']
        print(f"{name:<10} {acc:<10.4f} {train_t:<12.2f} {pred_t:<12.4f}")

def main():
    """主函数"""
    print("叶片内腔缺陷数据集使用示例")
    print("="*50)
    
    # 1. 加载数据集
    df = load_dataset('blade_cavity_defects_30k.csv')
    
    # 2. 探索数据集
    class_counts = explore_dataset(df)
    
    # 3. 准备数据
    X, y, feature_names = prepare_data(df)
    
    # 4. 比较分类器
    results, X_test, y_test = compare_classifiers(X, y, feature_names)
    
    # 5. 可视化结果
    visualize_results(results, X_test, y_test)
    
    # 6. 生成性能报告
    generate_performance_report(results)
    
    print(f"\n✅ 数据集使用示例完成！")
    print(f"生成的文件:")
    print(f"  - classifier_comparison_results.png (分类器对比结果)")
    
    return df, results

if __name__ == "__main__":
    df, results = main()
