import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_visualize_dataset(filename='frequency_domain_defects_10000.csv'):
    """加载并可视化频域特征数据集"""
    
    # 读取数据
    print(f"正在加载数据集: {filename}")
    df = pd.read_csv(filename, encoding='utf-8-sig')
    
    print(f"数据集基本信息:")
    print(f"  样本数量: {len(df):,}")
    print(f"  特征数量: {len(df.columns) - 2}")  # 减去缺陷类型和编码列
    print(f"  缺陷类型: {df['缺陷类型'].unique()}")
    
    # 定义颜色映射
    colors = {
        '纤维褶皱': '#FF6B6B',
        '裂纹': '#4ECDC4',
        '分层': '#45B7D1',
        '鼓包': '#96CEB4'
    }
    
    # 获取特征列
    feature_cols = [col for col in df.columns if col not in ['缺陷类型', '缺陷编码']]
    
    # 创建大型可视化图表
    fig = plt.figure(figsize=(20, 24))
    
    # 1. 主要统计特征对比 (目标值 vs 实际值)
    ax1 = plt.subplot(4, 3, 1)
    target_stats = {
        '平均值': 0.004535467952873973,
        '方差': 0.13361739428910718,
        '均方值': 0.017853608056610733,
        '均方根值': 0.13361739428910718,
        '最大值': 1.9846451308495212,
        '最小值': -1.7174139110666464,
        '峰值': 1.9846451308495212,
        '峰峰值': 3.7020590419161676,
        '平均绝对幅值': 0.09177927629408723,
        '方根幅值': 0.07371838887961654
    }
    
    main_features = list(target_stats.keys())
    target_values = [target_stats[f] for f in main_features]
    actual_values = [df[f].mean() for f in main_features]
    
    x_pos = np.arange(len(main_features))
    width = 0.35
    
    ax1.bar(x_pos - width/2, target_values, width, label='目标值', alpha=0.8, color='skyblue')
    ax1.bar(x_pos + width/2, actual_values, width, label='实际均值', alpha=0.8, color='orange')
    
    ax1.set_xlabel('统计特征')
    ax1.set_ylabel('数值')
    ax1.set_title('目标统计特征 vs 实际统计特征对比')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(main_features, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 各缺陷类型样本分布饼图
    ax2 = plt.subplot(4, 3, 2)
    defect_counts = df['缺陷类型'].value_counts()
    colors_list = [colors[defect] for defect in defect_counts.index]
    ax2.pie(defect_counts.values, labels=defect_counts.index, 
            colors=colors_list, autopct='%1.1f%%', startangle=90)
    ax2.set_title('各缺陷类型样本分布')
    
    # 3. 主要特征的箱线图
    ax3 = plt.subplot(4, 3, 3)
    main_features_subset = ['平均值', '方差', '均方值', '均方根值']
    df_melted = df.melt(id_vars=['缺陷类型'], 
                       value_vars=main_features_subset,
                       var_name='特征', value_name='数值')
    sns.boxplot(data=df_melted, x='特征', y='数值', hue='缺陷类型', ax=ax3)
    ax3.set_title('主要统计特征分布对比')
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. 峰值相关特征箱线图
    ax4 = plt.subplot(4, 3, 4)
    peak_features = ['最大值', '最小值', '峰值', '峰峰值']
    df_melted2 = df.melt(id_vars=['缺陷类型'], 
                        value_vars=peak_features,
                        var_name='特征', value_name='数值')
    sns.boxplot(data=df_melted2, x='特征', y='数值', hue='缺陷类型', ax=ax4)
    ax4.set_title('峰值相关特征分布对比')
    ax4.tick_params(axis='x', rotation=45)
    
    # 5. 幅值相关特征箱线图
    ax5 = plt.subplot(4, 3, 5)
    amplitude_features = ['平均绝对幅值', '方根幅值']
    df_melted3 = df.melt(id_vars=['缺陷类型'], 
                        value_vars=amplitude_features,
                        var_name='特征', value_name='数值')
    sns.boxplot(data=df_melted3, x='特征', y='数值', hue='缺陷类型', ax=ax5)
    ax5.set_title('幅值相关特征分布对比')
    ax5.tick_params(axis='x', rotation=45)
    
    # 6. 高阶统计量箱线图
    ax6 = plt.subplot(4, 3, 6)
    higher_order_features = ['偏度', '峭度']
    df_melted4 = df.melt(id_vars=['缺陷类型'], 
                        value_vars=higher_order_features,
                        var_name='特征', value_name='数值')
    sns.boxplot(data=df_melted4, x='特征', y='数值', hue='缺陷类型', ax=ax6)
    ax6.set_title('高阶统计量分布对比')
    ax6.tick_params(axis='x', rotation=45)
    
    # 7. 形状指标箱线图
    ax7 = plt.subplot(4, 3, 7)
    shape_features = ['波形指标', '峰值指标', '脉冲指标']
    df_melted5 = df.melt(id_vars=['缺陷类型'], 
                        value_vars=shape_features,
                        var_name='特征', value_name='数值')
    sns.boxplot(data=df_melted5, x='特征', y='数值', hue='缺陷类型', ax=ax7)
    ax7.set_title('形状指标分布对比')
    ax7.tick_params(axis='x', rotation=45)
    
    # 8. 特征相关性热力图
    ax8 = plt.subplot(4, 3, 8)
    # 选择主要特征进行相关性分析
    main_features_for_corr = main_features_subset + ['偏度', '峭度', '波形指标', '峰值指标']
    corr_matrix = df[main_features_for_corr].corr()
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
               fmt='.2f', ax=ax8, cbar_kws={'shrink': 0.8})
    ax8.set_title('主要特征相关性矩阵')
    
    # 9. 平均值 vs 方差散点图
    ax9 = plt.subplot(4, 3, 9)
    for defect_type in df['缺陷类型'].unique():
        mask = df['缺陷类型'] == defect_type
        ax9.scatter(df.loc[mask, '平均值'], df.loc[mask, '方差'], 
                   c=colors[defect_type], label=defect_type, alpha=0.6, s=30)
    ax9.set_xlabel('平均值')
    ax9.set_ylabel('方差')
    ax9.set_title('平均值 vs 方差分布')
    ax9.legend()
    ax9.grid(True, alpha=0.3)
    
    # 10. 峰值 vs 峰峰值散点图
    ax10 = plt.subplot(4, 3, 10)
    for defect_type in df['缺陷类型'].unique():
        mask = df['缺陷类型'] == defect_type
        ax10.scatter(df.loc[mask, '峰值'], df.loc[mask, '峰峰值'], 
                    c=colors[defect_type], label=defect_type, alpha=0.6, s=30)
    ax10.set_xlabel('峰值')
    ax10.set_ylabel('峰峰值')
    ax10.set_title('峰值 vs 峰峰值分布')
    ax10.legend()
    ax10.grid(True, alpha=0.3)
    
    # 11. 各缺陷类型特征均值雷达图数据
    ax11 = plt.subplot(4, 3, 11)
    defect_means = df.groupby('缺陷类型')[main_features_subset].mean()
    defect_means_norm = (defect_means - defect_means.min()) / (defect_means.max() - defect_means.min())
    
    x_pos = np.arange(len(main_features_subset))
    width = 0.2
    for i, defect_type in enumerate(defect_means_norm.index):
        ax11.bar(x_pos + i*width, defect_means_norm.loc[defect_type], 
               width, label=defect_type, color=colors[defect_type], alpha=0.7)
    
    ax11.set_xlabel('特征')
    ax11.set_ylabel('标准化均值')
    ax11.set_title('各缺陷类型主要特征对比')
    ax11.set_xticks(x_pos + width * 1.5)
    ax11.set_xticklabels(main_features_subset, rotation=45)
    ax11.legend()
    ax11.grid(True, alpha=0.3)
    
    # 12. 统计特征分布直方图
    ax12 = plt.subplot(4, 3, 12)
    for defect_type in df['缺陷类型'].unique():
        data = df[df['缺陷类型'] == defect_type]['平均值']
        ax12.hist(data, alpha=0.6, label=defect_type, bins=30, color=colors[defect_type])
    ax12.set_xlabel('平均值')
    ax12.set_ylabel('频次')
    ax12.set_title('平均值分布直方图')
    ax12.legend()
    ax12.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('frequency_domain_dataset_visualization.png', dpi=300, bbox_inches='tight')
    print("数据集可视化图表已保存为: frequency_domain_dataset_visualization.png")
    
    # 生成详细统计报告
    print("\n" + "="*80)
    print("频域特征数据集详细统计报告")
    print("="*80)
    
    # 整体统计
    print(f"\n数据集基本信息:")
    print(f"  总样本数: {len(df):,}")
    print(f"  特征数量: {len(feature_cols)}")
    print(f"  缺陷类型数: {len(df['缺陷类型'].unique())}")
    
    # 各类别样本分布
    print(f"\n各缺陷类型样本分布:")
    for defect_type, count in df['缺陷类型'].value_counts().items():
        print(f"  {defect_type}: {count:,} 个样本 ({count/len(df)*100:.1f}%)")
    
    # 主要特征统计对比
    print(f"\n主要统计特征对比 (目标值 vs 实际均值):")
    print(f"{'特征名称':<15} {'目标值':<15} {'实际均值':<15} {'相对误差(%)':<15}")
    print("-" * 65)
    
    for feature in main_features:
        target_val = target_stats[feature]
        actual_val = df[feature].mean()
        relative_error = abs(actual_val - target_val) / abs(target_val) * 100
        print(f"{feature:<15} {target_val:<15.6f} {actual_val:<15.6f} {relative_error:<15.2f}")
    
    # 各缺陷类型特征统计
    print(f"\n各缺陷类型主要特征均值:")
    defect_stats = df.groupby('缺陷类型')[main_features_subset].mean()
    print(defect_stats.round(6))
    
    return df, fig

def main():
    """主函数"""
    print("频域特征数据集可视化分析")
    print("="*50)
    
    # 加载并可视化数据集
    df, fig = load_and_visualize_dataset('frequency_domain_defects_10000.csv')
    
    print(f"\n可视化完成！生成的文件:")
    print(f"  - frequency_domain_dataset_visualization.png (数据集可视化图表)")
    
    return df

if __name__ == "__main__":
    df = main()
