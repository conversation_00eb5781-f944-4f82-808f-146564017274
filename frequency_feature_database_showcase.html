<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频域特征提取与数据库构建可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            max-width: 900px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2.2em;
            color: #4a5568;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            font-size: 1.2em;
            margin-right: 15px;
        }

        .section-description {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.8;
        }

        .image-container {
            text-align: center;
            margin: 25px 0;
        }

        .process-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .process-image:hover {
            transform: scale(1.02);
        }

        .image-caption {
            font-style: italic;
            color: #666;
            margin-top: 15px;
            font-size: 1em;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .feature-card p {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 10px;
        }

        .feature-card ul {
            color: #666;
            font-size: 0.9em;
            padding-left: 20px;
        }

        .feature-card li {
            margin-bottom: 5px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .process-flow {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .process-flow h3 {
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
        }

        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-step {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            min-width: 150px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .flow-step:hover {
            transform: translateY(-3px);
        }

        .flow-step .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 10px;
        }

        .flow-step h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .flow-step p {
            color: #666;
            font-size: 0.9em;
        }

        .arrow {
            font-size: 2em;
            color: #667eea;
            margin: 0 10px;
        }

        .database-schema {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px solid #e9ecef;
        }

        .database-schema h4 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .schema-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .schema-header {
            background: #667eea;
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .schema-row {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
        }

        .schema-row:last-child {
            border-bottom: none;
        }

        .schema-row:nth-child(even) {
            background: #f8f9fa;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 50px;
            padding: 30px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .section-title {
                font-size: 1.8em;
            }
            
            .container {
                padding: 15px;
            }
            
            .section {
                padding: 20px;
            }
            
            .flow-steps {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 频域特征提取与数据库构建</h1>
            <p>基于FFT变换的缺陷信号频域特征提取、分析与数据库构建的完整可视化流程</p>
        </div>

        <!-- 概述 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🎯</span>
                项目概述
            </h2>
            <p class="section-description">
                本项目展示了如何从缺陷信号中提取频域特征，并构建结构化数据库的完整过程。
                通过FFT变换将时域信号转换为频域，提取18维频域特征，最终构建包含200个样本的特征数据库。
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">200</div>
                    <div class="stat-label">信号样本</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">4</div>
                    <div class="stat-label">缺陷类型</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">18</div>
                    <div class="stat-label">频域特征</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">1000Hz</div>
                    <div class="stat-label">采样频率</div>
                </div>
            </div>
        </div>

        <!-- 特征提取过程 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🔍</span>
                频域特征提取过程
            </h2>
            <p class="section-description">
                展示四种缺陷类型（裂纹、纤维褶皱、分层、鼓包）的时域信号、频域变换、能量分布和特征提取的完整过程。
                每种缺陷都具有独特的频域特征，为智能识别提供了可靠的特征基础。
            </p>
            
            <div class="image-container">
                <img src="frequency_feature_extraction_process.png" alt="频域特征提取过程" class="process-image">
                <p class="image-caption">四种缺陷类型的频域特征提取完整过程</p>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔥 裂纹缺陷特征</h3>
                    <p><strong>频域特征：</strong>高频成分丰富，主频率在200-400Hz</p>
                    <ul>
                        <li>高频能量比显著增加</li>
                        <li>频谱重心偏向高频</li>
                        <li>频谱平坦度较低</li>
                        <li>有效带宽较宽</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🌊 纤维褶皱特征</h3>
                    <p><strong>频域特征：</strong>中频周期性成分，主频率在80-120Hz</p>
                    <ul>
                        <li>中频能量比最高</li>
                        <li>频谱重心在中频段</li>
                        <li>频谱方差适中</li>
                        <li>调制特征明显</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📈 分层缺陷特征</h3>
                    <p><strong>频域特征：</strong>低频基础 + 中频脉冲，主频率在20-40Hz</p>
                    <ul>
                        <li>低频能量比较高</li>
                        <li>间歇性中频成分</li>
                        <li>频谱重心偏低</li>
                        <li>双峰频谱特征</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🎈 鼓包缺陷特征</h3>
                    <p><strong>频域特征：</strong>低频大幅度振荡，主频率在10-25Hz</p>
                    <ul>
                        <li>低频能量比最高</li>
                        <li>频谱重心最低</li>
                        <li>频域幅值较大</li>
                        <li>包络调制特征</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 数据库构建流程 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🗄️</span>
                数据库构建流程
            </h2>
            <p class="section-description">
                从原始缺陷信号到结构化数据库的完整构建流程，包括信号预处理、FFT变换、特征提取、
                标准化处理和数据库存储等关键步骤。
            </p>
            
            <div class="image-container">
                <img src="frequency_database_construction_flow.png" alt="数据库构建流程" class="process-image">
                <p class="image-caption">频域特征数据库构建完整流程</p>
            </div>

            <div class="process-flow">
                <h3>详细处理流程</h3>
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <h4>信号预处理</h4>
                        <p>去直流、滤波、归一化</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <h4>FFT变换</h4>
                        <p>计算频域幅值谱</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <h4>特征提取</h4>
                        <p>18维频域特征</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <h4>特征标准化</h4>
                        <p>归一化处理</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <h4>数据库存储</h4>
                        <p>结构化存储</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库结构 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🏗️</span>
                数据库结构设计
            </h2>
            <p class="section-description">
                频域特征数据库采用关系型数据库设计，包含信号标识、缺陷类型、采样参数和18维频域特征等字段，
                支持高效的查询和分析操作。
            </p>

            <div class="database-schema">
                <h4>📋 frequency_features 表结构</h4>
                <div class="schema-table">
                    <div class="schema-header">
                        字段名称 | 数据类型 | 描述
                    </div>
                    <div class="schema-row">
                        <span>signal_id</span>
                        <span>TEXT (主键)</span>
                        <span>信号唯一标识符</span>
                    </div>
                    <div class="schema-row">
                        <span>defect_type</span>
                        <span>TEXT</span>
                        <span>缺陷类型（裂纹/纤维褶皱/分层/鼓包）</span>
                    </div>
                    <div class="schema-row">
                        <span>sampling_rate</span>
                        <span>REAL</span>
                        <span>采样频率 (Hz)</span>
                    </div>
                    <div class="schema-row">
                        <span>频域均值/标准差/最大值</span>
                        <span>REAL</span>
                        <span>基础统计特征</span>
                    </div>
                    <div class="schema-row">
                        <span>主频率/频谱重心</span>
                        <span>REAL</span>
                        <span>频率特征</span>
                    </div>
                    <div class="schema-row">
                        <span>低/中/高频能量比</span>
                        <span>REAL</span>
                        <span>能量分布特征</span>
                    </div>
                    <div class="schema-row">
                        <span>频谱平坦度/滚降点</span>
                        <span>REAL</span>
                        <span>形状特征</span>
                    </div>
                    <div class="schema-row">
                        <span>created_time</span>
                        <span>TEXT</span>
                        <span>创建时间戳</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库可视化分析 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📈</span>
                数据库可视化分析
            </h2>
            <p class="section-description">
                对构建的频域特征数据库进行全面的可视化分析，包括缺陷类型分布、特征分布、
                相关性分析等，验证数据库的质量和特征的有效性。
            </p>
            
            <div class="image-container">
                <img src="frequency_database_visualization.png" alt="数据库可视化分析" class="process-image">
                <p class="image-caption">频域特征数据库的多维度可视化分析</p>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📊 数据分布分析</h3>
                    <p>各缺陷类型样本均匀分布，每类50个样本</p>
                    <ul>
                        <li>样本平衡性良好</li>
                        <li>特征分布合理</li>
                        <li>无明显异常值</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🎯 特征区分度</h3>
                    <p>不同缺陷类型在频域特征上具有良好的区分度</p>
                    <ul>
                        <li>主频率差异显著</li>
                        <li>能量分布特征明显</li>
                        <li>频谱重心区分度高</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔗 特征相关性</h3>
                    <p>特征间相关性分析揭示了频域特征的内在关系</p>
                    <ul>
                        <li>能量特征间强相关</li>
                        <li>频率特征相对独立</li>
                        <li>统计特征互补性好</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ 查询性能</h3>
                    <p>数据库设计优化，支持高效的特征查询和分析</p>
                    <ul>
                        <li>索引优化完善</li>
                        <li>查询响应快速</li>
                        <li>支持复杂分析</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🔬 频域特征提取与数据库构建可视化系统</p>
            <p>💡 基于FFT变换的智能缺陷识别特征工程解决方案</p>
        </div>
    </div>
</body>
</html>
