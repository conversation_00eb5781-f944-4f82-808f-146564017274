import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DefectSignalAnalyzer:
    def __init__(self, fs=1000, duration=2.0):
        """
        初始化缺陷信号分析器
        fs: 采样频率 (Hz)
        duration: 信号持续时间 (s)
        """
        self.fs = fs
        self.duration = duration
        self.t = np.linspace(0, duration, int(fs * duration), endpoint=False)
        self.defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
        
    def generate_defect_signals(self, num_samples=100):
        """生成四种缺陷的时域信号"""
        signals = []
        labels = []
        
        for defect_idx, defect_type in enumerate(self.defect_types):
            for i in range(num_samples):
                if defect_type == '裂纹':
                    # 裂纹：高频振动 + 冲击响应
                    signal = (np.sin(2 * np.pi * 150 * self.t) * 
                             np.exp(-5 * self.t) + 
                             0.5 * np.sin(2 * np.pi * 300 * self.t) +
                             0.3 * np.random.randn(len(self.t)))
                    
                elif defect_type == '纤维褶皱':
                    # 纤维褶皱：中频调制信号
                    signal = (np.sin(2 * np.pi * 80 * self.t) * 
                             (1 + 0.5 * np.sin(2 * np.pi * 10 * self.t)) +
                             0.2 * np.sin(2 * np.pi * 200 * self.t) +
                             0.2 * np.random.randn(len(self.t)))
                    
                elif defect_type == '分层':
                    # 分层：低频振动 + 谐波
                    signal = (np.sin(2 * np.pi * 50 * self.t) +
                             0.6 * np.sin(2 * np.pi * 100 * self.t) +
                             0.4 * np.sin(2 * np.pi * 150 * self.t) +
                             0.25 * np.random.randn(len(self.t)))
                    
                elif defect_type == '鼓包':
                    # 鼓包：脉冲信号 + 低频成分
                    pulse_times = np.random.choice(self.t[::100], 3, replace=False)
                    signal = np.zeros_like(self.t)
                    for pulse_time in pulse_times:
                        pulse_idx = np.argmin(np.abs(self.t - pulse_time))
                        signal += 2 * np.exp(-20 * (self.t - pulse_time)**2)
                    signal += (0.3 * np.sin(2 * np.pi * 30 * self.t) +
                              0.2 * np.random.randn(len(self.t)))
                
                signals.append(signal)
                labels.append(defect_idx)
        
        return np.array(signals), np.array(labels)
    
    def plot_time_domain_signals(self, signals, labels):
        """绘制时域信号"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        colors = ['red', 'blue', 'green', 'orange']
        
        for i, defect_type in enumerate(self.defect_types):
            # 选择该类型的第一个信号作为示例
            signal_idx = np.where(labels == i)[0][0]
            signal = signals[signal_idx]
            
            axes[i].plot(self.t, signal, color=colors[i], linewidth=1.5)
            axes[i].set_title(f'{defect_type}缺陷时域信号', fontsize=14, fontweight='bold')
            axes[i].set_xlabel('时间 (s)', fontsize=12)
            axes[i].set_ylabel('幅值', fontsize=12)
            axes[i].grid(True, alpha=0.3)
            axes[i].set_xlim(0, 1)  # 只显示前1秒
        
        plt.tight_layout()
        plt.savefig('defect_time_domain_signals.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def compute_fft(self, signals):
        """计算FFT变换"""
        fft_signals = []
        freqs = np.fft.fftfreq(len(self.t), 1/self.fs)[:len(self.t)//2]

        for signal in signals:
            fft_signal = np.abs(np.fft.fft(signal))[:len(self.t)//2]
            fft_signals.append(fft_signal)

        return np.array(fft_signals), freqs
    
    def plot_frequency_domain_signals(self, fft_signals, freqs, labels):
        """绘制频域信号（四个信号在同一坐标轴）"""
        plt.figure(figsize=(15, 8))
        
        colors = ['red', 'blue', 'green', 'orange']
        
        for i, defect_type in enumerate(self.defect_types):
            # 选择该类型的第一个信号作为示例
            signal_idx = np.where(labels == i)[0][0]
            fft_signal = fft_signals[signal_idx]
            
            plt.plot(freqs, fft_signal, color=colors[i], linewidth=2, 
                    label=f'{defect_type}缺陷', alpha=0.8)
        
        plt.title('四种缺陷频域信号对比', fontsize=16, fontweight='bold')
        plt.xlabel('频率 (Hz)', fontsize=14)
        plt.ylabel('幅值', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 400)  # 只显示0-400Hz
        plt.tight_layout()
        plt.savefig('defect_frequency_domain_signals.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def extract_features(self, signals, fft_signals, freqs):
        """提取时域和频域特征"""
        features = []
        
        for i, signal in enumerate(signals):
            fft_signal = fft_signals[i]
            
            # 时域特征
            mean_val = np.mean(signal)
            std_val = np.std(signal)
            rms_val = np.sqrt(np.mean(signal**2))
            peak_val = np.max(np.abs(signal))
            kurtosis = np.mean(((signal - mean_val) / std_val)**4) - 3
            skewness = np.mean(((signal - mean_val) / std_val)**3)
            
            # 频域特征
            dominant_freq = freqs[np.argmax(fft_signal)]
            spectral_centroid = np.sum(freqs * fft_signal) / np.sum(fft_signal)
            spectral_rolloff = freqs[np.where(np.cumsum(fft_signal) >= 0.85 * np.sum(fft_signal))[0][0]]
            spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid)**2) * fft_signal) / np.sum(fft_signal))
            
            feature_vector = [mean_val, std_val, rms_val, peak_val, kurtosis, skewness,
                            dominant_freq, spectral_centroid, spectral_rolloff, spectral_bandwidth]
            features.append(feature_vector)
        
        return np.array(features)
    
    def train_knn_classifier(self, features, labels, k=5):
        """训练KNN分类器"""
        # 数据标准化
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, labels, test_size=0.3, random_state=42, stratify=labels)
        
        # 训练KNN分类器
        knn = KNeighborsClassifier(n_neighbors=k)
        knn.fit(X_train, y_train)
        
        # 预测
        y_pred = knn.predict(X_test)
        
        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        
        return knn, scaler, X_test, y_test, y_pred, accuracy
    
    def plot_classification_results(self, y_test, y_pred, accuracy):
        """绘制分类结果"""
        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        
        plt.figure(figsize=(15, 5))
        
        # 混淆矩阵热力图
        plt.subplot(1, 3, 1)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.defect_types, yticklabels=self.defect_types)
        plt.title('混淆矩阵', fontsize=14, fontweight='bold')
        plt.xlabel('预测标签', fontsize=12)
        plt.ylabel('真实标签', fontsize=12)
        
        # 分类准确率柱状图
        plt.subplot(1, 3, 2)
        class_accuracy = cm.diagonal() / cm.sum(axis=1)
        colors = ['red', 'blue', 'green', 'orange']
        bars = plt.bar(self.defect_types, class_accuracy, color=colors, alpha=0.7)
        plt.title(f'各类缺陷分类准确率\n总体准确率: {accuracy:.3f}', fontsize=14, fontweight='bold')
        plt.ylabel('准确率', fontsize=12)
        plt.ylim(0, 1)
        
        # 在柱状图上添加数值标签
        for bar, acc in zip(bars, class_accuracy):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.3f}', ha='center', va='bottom', fontsize=10)
        
        # 分类报告可视化
        plt.subplot(1, 3, 3)
        report = classification_report(y_test, y_pred, target_names=self.defect_types, output_dict=True)
        metrics = ['precision', 'recall', 'f1-score']
        x = np.arange(len(self.defect_types))
        width = 0.25
        
        for i, metric in enumerate(metrics):
            values = [report[defect][metric] for defect in self.defect_types]
            plt.bar(x + i*width, values, width, label=metric, alpha=0.8)
        
        plt.title('分类性能指标', fontsize=14, fontweight='bold')
        plt.xlabel('缺陷类型', fontsize=12)
        plt.ylabel('分数', fontsize=12)
        plt.xticks(x + width, self.defect_types)
        plt.legend()
        plt.ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig('knn_classification_results.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    print("开始缺陷信号分析...")
    
    # 创建分析器
    analyzer = DefectSignalAnalyzer(fs=1000, duration=2.0)
    
    # 生成缺陷信号
    print("生成缺陷信号...")
    signals, labels = analyzer.generate_defect_signals(num_samples=100)
    
    # 绘制时域信号
    print("绘制时域信号...")
    analyzer.plot_time_domain_signals(signals, labels)
    
    # 计算FFT
    print("计算FFT变换...")
    fft_signals, freqs = analyzer.compute_fft(signals)
    
    # 绘制频域信号
    print("绘制频域信号...")
    analyzer.plot_frequency_domain_signals(fft_signals, freqs, labels)
    
    # 提取特征
    print("提取特征...")
    features = analyzer.extract_features(signals, fft_signals, freqs)
    
    # 训练KNN分类器
    print("训练KNN分类器...")
    knn, scaler, X_test, y_test, y_pred, accuracy = analyzer.train_knn_classifier(features, labels, k=5)
    
    # 绘制分类结果
    print("绘制分类结果...")
    analyzer.plot_classification_results(y_test, y_pred, accuracy)
    
    print(f"\n分析完成！")
    print(f"总体分类准确率: {accuracy:.3f}")
    print(f"生成的图像文件:")
    print("- defect_time_domain_signals.png (时域信号)")
    print("- defect_frequency_domain_signals.png (频域信号)")
    print("- knn_classification_results.png (分类结果)")

if __name__ == "__main__":
    main()
