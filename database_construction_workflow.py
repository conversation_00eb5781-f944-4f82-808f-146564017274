import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_database_workflow_diagram():
    """创建数据库建立流程图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'collection': '#3498DB',    # 蓝色 - 数据采集
        'processing': '#E74C3C',    # 红色 - 数据处理
        'annotation': '#F39C12',    # 橙色 - 数据标注
        'database': '#2ECC71',      # 绿色 - 数据库
        'validation': '#9B59B6',    # 紫色 - 验证
        'deployment': '#1ABC9C'     # 青色 - 部署
    }
    
    # 标题
    ax.text(5, 11.5, '缺陷数据库建立完整流程', fontsize=24, fontweight='bold', 
            ha='center', va='center')
    
    # 阶段1：数据采集
    stage1_box = FancyBboxPatch((0.5, 9.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['collection'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage1_box)
    ax.text(1.5, 10.7, '阶段1', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(1.5, 10.3, '数据采集', fontsize=12, ha='center', color='white')
    ax.text(1.5, 9.9, '• 超声波检测\n• 声发射监测\n• 振动信号', fontsize=10, ha='center', color='white')
    
    # 阶段2：数据预处理
    stage2_box = FancyBboxPatch((3.5, 9.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['processing'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage2_box)
    ax.text(4.5, 10.7, '阶段2', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(4.5, 10.3, '数据预处理', fontsize=12, ha='center', color='white')
    ax.text(4.5, 9.9, '• 滤波去噪\n• 信号增强\n• 质量检查', fontsize=10, ha='center', color='white')
    
    # 阶段3：特征提取
    stage3_box = FancyBboxPatch((6.5, 9.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['annotation'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage3_box)
    ax.text(7.5, 10.7, '阶段3', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(7.5, 10.3, '特征提取', fontsize=12, ha='center', color='white')
    ax.text(7.5, 9.9, '• 时域特征\n• 频域特征\n• 时频特征', fontsize=10, ha='center', color='white')
    
    # 阶段4：数据标注
    stage4_box = FancyBboxPatch((0.5, 7.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['validation'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage4_box)
    ax.text(1.5, 8.7, '阶段4', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(1.5, 8.3, '数据标注', fontsize=12, ha='center', color='white')
    ax.text(1.5, 7.9, '• 专家标注\n• 质量验证\n• 一致性检查', fontsize=10, ha='center', color='white')
    
    # 阶段5：数据库构建
    stage5_box = FancyBboxPatch((3.5, 7.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['database'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage5_box)
    ax.text(4.5, 8.7, '阶段5', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(4.5, 8.3, '数据库构建', fontsize=12, ha='center', color='white')
    ax.text(4.5, 7.9, '• 数据存储\n• 索引优化\n• 接口开发', fontsize=10, ha='center', color='white')
    
    # 阶段6：验证部署
    stage6_box = FancyBboxPatch((6.5, 7.5), 2, 1.5, boxstyle="round,pad=0.1", 
                               facecolor=colors['deployment'], alpha=0.8, edgecolor='black')
    ax.add_patch(stage6_box)
    ax.text(7.5, 8.7, '阶段6', fontsize=14, fontweight='bold', ha='center', color='white')
    ax.text(7.5, 8.3, '验证部署', fontsize=12, ha='center', color='white')
    ax.text(7.5, 7.9, '• 模型训练\n• 系统测试\n• 正式部署', fontsize=10, ha='center', color='white')
    
    # 绘制箭头连接
    arrow_props = dict(arrowstyle='->', lw=2, color='black')
    
    # 水平箭头
    ax.annotate('', xy=(3.4, 10.25), xytext=(2.6, 10.25), arrowprops=arrow_props)
    ax.annotate('', xy=(6.4, 10.25), xytext=(5.6, 10.25), arrowprops=arrow_props)
    
    # 垂直箭头
    ax.annotate('', xy=(1.5, 9.4), xytext=(1.5, 8.8), arrowprops=arrow_props)
    ax.annotate('', xy=(4.5, 9.4), xytext=(4.5, 8.8), arrowprops=arrow_props)
    ax.annotate('', xy=(7.5, 9.4), xytext=(7.5, 8.8), arrowprops=arrow_props)
    
    # 水平箭头（下层）
    ax.annotate('', xy=(3.4, 8.25), xytext=(2.6, 8.25), arrowprops=arrow_props)
    ax.annotate('', xy=(6.4, 8.25), xytext=(5.6, 8.25), arrowprops=arrow_props)
    
    # 详细步骤说明
    detail_y = 6
    ax.text(5, detail_y, '详细实施步骤', fontsize=18, fontweight='bold', ha='center')
    
    # 步骤详情
    steps = [
        "1. 设备配置：选择合适的检测设备和传感器",
        "2. 采样策略：制定空间和时间采样方案",
        "3. 信号预处理：滤波、去噪、归一化处理",
        "4. 特征工程：提取时域、频域、时频特征",
        "5. 专家标注：建立标注体系和质量控制",
        "6. 数据库设计：设计高效的存储和查询结构",
        "7. 质量验证：多层次质量检查和验证",
        "8. 系统部署：集成测试和正式上线"
    ]
    
    for i, step in enumerate(steps):
        y_pos = detail_y - 0.5 - i * 0.4
        ax.text(0.5, y_pos, step, fontsize=11, ha='left', va='center')
    
    # 关键指标框
    metrics_box = FancyBboxPatch((0.2, 0.5), 9.6, 2, boxstyle="round,pad=0.2", 
                                facecolor='lightgray', alpha=0.3, edgecolor='black')
    ax.add_patch(metrics_box)
    
    ax.text(5, 2.2, '关键质量指标', fontsize=16, fontweight='bold', ha='center')
    
    metrics_text = """
    📊 数据规模：100,000+ 信号样本    🎯 标注准确率：≥95%    🔍 数据完整率：≥95%
    
    🏷️ 缺陷类型：12+ 种类型          📈 特征维度：50+ 维度    ⚡ 处理速度：实时响应
    
    🤝 专家一致性：≥85%             🔒 数据安全：多重备份    🌐 系统可用性：≥99.5%
    """
    
    ax.text(5, 1.3, metrics_text, fontsize=12, ha='center', va='center')
    
    plt.tight_layout()
    plt.savefig('defect_database_construction_workflow.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 数据库建立流程图已生成: defect_database_construction_workflow.png")

def create_data_flow_diagram():
    """创建数据流程图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(18, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 标题
    ax.text(5, 9.5, '缺陷数据库数据流程图', fontsize=22, fontweight='bold', ha='center')
    
    # 数据源
    sources = ['超声波检测', '声发射监测', '振动分析', '热成像检测']
    source_colors = ['#3498DB', '#E74C3C', '#F39C12', '#2ECC71']
    
    for i, (source, color) in enumerate(zip(sources, source_colors)):
        x = 1 + i * 2
        y = 8
        circle = plt.Circle((x, y), 0.4, facecolor=color, alpha=0.8, edgecolor='black')
        ax.add_patch(circle)
        ax.text(x, y, source, fontsize=10, ha='center', va='center', color='white', fontweight='bold')
    
    # 数据汇聚
    convergence_box = FancyBboxPatch((3.5, 6.5), 3, 0.8, boxstyle="round,pad=0.1", 
                                    facecolor='lightblue', alpha=0.8, edgecolor='black')
    ax.add_patch(convergence_box)
    ax.text(5, 6.9, '多源数据融合', fontsize=14, fontweight='bold', ha='center')
    
    # 处理流程
    processes = [
        ('数据预处理', 2, 5.5, '#E74C3C'),
        ('特征提取', 5, 5.5, '#F39C12'),
        ('质量控制', 8, 5.5, '#9B59B6')
    ]
    
    for process, x, y, color in processes:
        process_box = FancyBboxPatch((x-0.8, y-0.3), 1.6, 0.6, boxstyle="round,pad=0.1", 
                                    facecolor=color, alpha=0.8, edgecolor='black')
        ax.add_patch(process_box)
        ax.text(x, y, process, fontsize=12, fontweight='bold', ha='center', color='white')
    
    # 数据库存储
    db_box = FancyBboxPatch((3.5, 3.5), 3, 1.2, boxstyle="round,pad=0.1", 
                           facecolor='#2ECC71', alpha=0.8, edgecolor='black')
    ax.add_patch(db_box)
    ax.text(5, 4.5, '缺陷数据库', fontsize=16, fontweight='bold', ha='center', color='white')
    ax.text(5, 4, '• 原始信号存储\n• 特征数据管理\n• 标注信息维护', 
            fontsize=11, ha='center', color='white')
    
    # 应用层
    applications = [
        ('智能诊断', 1.5, 2, '#1ABC9C'),
        ('预测维护', 3.5, 2, '#34495E'),
        ('质量评估', 5.5, 2, '#E67E22'),
        ('标准制定', 7.5, 2, '#8E44AD')
    ]
    
    for app, x, y, color in applications:
        app_box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6, boxstyle="round,pad=0.1", 
                                facecolor=color, alpha=0.8, edgecolor='black')
        ax.add_patch(app_box)
        ax.text(x, y, app, fontsize=11, fontweight='bold', ha='center', color='white')
    
    # 绘制连接箭头
    arrow_props = dict(arrowstyle='->', lw=2, color='black')
    
    # 从数据源到汇聚
    for i in range(4):
        x_start = 1 + i * 2
        ax.annotate('', xy=(4.5, 7.2), xytext=(x_start, 7.6), arrowprops=arrow_props)
    
    # 从汇聚到处理
    ax.annotate('', xy=(2, 5.8), xytext=(4, 6.5), arrowprops=arrow_props)
    ax.annotate('', xy=(5, 5.8), xytext=(5, 6.5), arrowprops=arrow_props)
    ax.annotate('', xy=(8, 5.8), xytext=(6, 6.5), arrowprops=arrow_props)
    
    # 从处理到数据库
    ax.annotate('', xy=(4, 4.6), xytext=(2, 5.2), arrowprops=arrow_props)
    ax.annotate('', xy=(5, 4.6), xytext=(5, 5.2), arrowprops=arrow_props)
    ax.annotate('', xy=(6, 4.6), xytext=(8, 5.2), arrowprops=arrow_props)
    
    # 从数据库到应用
    for i, (_, x, _, _) in enumerate(applications):
        ax.annotate('', xy=(x, 2.3), xytext=(4.5, 3.5), arrowprops=arrow_props)
    
    # 添加时间线
    timeline_y = 0.8
    ax.text(5, timeline_y + 0.3, '实施时间线', fontsize=14, fontweight='bold', ha='center')
    
    timeline_items = [
        ('准备规划', 1, '2-3周'),
        ('数据采集', 2.5, '8-12周'),
        ('数据处理', 4, '6-8周'),
        ('标注验证', 5.5, '8-10周'),
        ('数据库构建', 7, '4-6周'),
        ('部署上线', 8.5, '3-4周')
    ]
    
    for item, x, duration in timeline_items:
        ax.text(x, timeline_y, f'{item}\n({duration})', fontsize=10, ha='center', va='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('defect_database_data_flow.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 数据流程图已生成: defect_database_data_flow.png")

def create_feature_extraction_diagram():
    """创建特征提取流程图"""

    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 8)
    ax.axis('off')

    # 箭头样式
    arrow_props = dict(arrowstyle='->', lw=2, color='black')

    # 标题
    ax.text(5, 7.5, '缺陷信号特征提取流程', fontsize=20, fontweight='bold', ha='center')
    
    # 原始信号
    signal_box = FancyBboxPatch((0.5, 6), 2, 0.8, boxstyle="round,pad=0.1", 
                               facecolor='#3498DB', alpha=0.8, edgecolor='black')
    ax.add_patch(signal_box)
    ax.text(1.5, 6.4, '原始缺陷信号', fontsize=12, fontweight='bold', ha='center', color='white')
    
    # 三个特征提取分支
    branches = [
        ('时域特征', 1.5, 4.5, '#E74C3C', ['均值', '标准差', '峰值', '偏度', '峭度']),
        ('频域特征', 5, 4.5, '#F39C12', ['主频率', '频谱重心', '能量分布', '带宽', '谐波']),
        ('时频特征', 8.5, 4.5, '#2ECC71', ['小波系数', '瞬时频率', '时频能量', '奇异值', '熵'])
    ]
    
    for branch_name, x, y, color, features in branches:
        # 主分支框
        branch_box = FancyBboxPatch((x-0.8, y-0.4), 1.6, 0.8, boxstyle="round,pad=0.1", 
                                   facecolor=color, alpha=0.8, edgecolor='black')
        ax.add_patch(branch_box)
        ax.text(x, y, branch_name, fontsize=12, fontweight='bold', ha='center', color='white')
        
        # 特征详情
        feature_text = '\n'.join([f'• {f}' for f in features])
        ax.text(x, y-1.5, feature_text, fontsize=10, ha='center', va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # 连接箭头
        ax.annotate('', xy=(x, y+0.4), xytext=(1.5, 5.6), arrowprops=arrow_props)
    
    # 特征融合
    fusion_box = FancyBboxPatch((3.5, 1.5), 3, 0.8, boxstyle="round,pad=0.1", 
                               facecolor='#9B59B6', alpha=0.8, edgecolor='black')
    ax.add_patch(fusion_box)
    ax.text(5, 1.9, '多维特征融合', fontsize=14, fontweight='bold', ha='center', color='white')
    
    # 从各分支到融合的箭头
    for _, x, y, _, _ in branches:
        ax.annotate('', xy=(5, 2.3), xytext=(x, y-2), arrowprops=arrow_props)
    
    # 最终输出
    output_box = FancyBboxPatch((3.5, 0.2), 3, 0.6, boxstyle="round,pad=0.1", 
                               facecolor='#1ABC9C', alpha=0.8, edgecolor='black')
    ax.add_patch(output_box)
    ax.text(5, 0.5, '高维特征向量 (50+维)', fontsize=12, fontweight='bold', ha='center', color='white')
    
    # 最后的箭头
    ax.annotate('', xy=(5, 0.8), xytext=(5, 1.5), arrowprops=arrow_props)
    
    plt.tight_layout()
    plt.savefig('feature_extraction_workflow.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 特征提取流程图已生成: feature_extraction_workflow.png")

if __name__ == "__main__":
    print("🎨 正在生成数据库建立流程可视化图表...")
    create_database_workflow_diagram()
    create_data_flow_diagram()
    create_feature_extraction_diagram()
    print("🎉 所有流程图生成完成！")
