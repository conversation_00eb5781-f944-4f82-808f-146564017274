import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
from matplotlib.font_manager import FontProperties
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DefectSignalAnalyzer:
    def __init__(self, fs=1000, duration=2.0):
        """
        初始化缺陷信号分析器
        fs: 采样频率 (Hz)
        duration: 信号持续时间 (s)
        """
        self.fs = fs
        self.duration = duration
        self.t = np.linspace(0, duration, int(fs * duration), endpoint=False)
        self.defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
        
    def generate_defect_signals(self, num_samples_per_class=50):
        """生成四种缺陷类型的时域信号"""
        signals = []
        labels = []
        
        for defect_idx, defect_type in enumerate(self.defect_types):
            for i in range(num_samples_per_class):
                if defect_type == '裂纹':
                    # 裂纹：高频冲击信号 + 噪声
                    signal = self._generate_crack_signal()
                elif defect_type == '纤维褶皱':
                    # 纤维褶皱：中频周期性信号
                    signal = self._generate_fiber_wrinkle_signal()
                elif defect_type == '分层':
                    # 分层：低频振荡 + 间歇性脉冲
                    signal = self._generate_delamination_signal()
                else:  # 鼓包
                    # 鼓包：低频大幅度振荡
                    signal = self._generate_bulge_signal()
                
                signals.append(signal)
                labels.append(defect_idx)
        
        return np.array(signals), np.array(labels)
    
    def _generate_crack_signal(self):
        """生成裂纹信号：高频冲击特征"""
        # 基础信号
        signal = 0.1 * np.sin(2 * np.pi * 50 * self.t)
        
        # 添加随机冲击
        num_impacts = np.random.randint(3, 8)
        for _ in range(num_impacts):
            impact_time = np.random.uniform(0.2, 1.8)
            impact_idx = int(impact_time * self.fs)
            # 高频冲击
            impact_freq = np.random.uniform(200, 400)
            impact_duration = 0.05
            impact_samples = int(impact_duration * self.fs)
            
            if impact_idx + impact_samples < len(signal):
                t_impact = np.linspace(0, impact_duration, impact_samples)
                impact = 2.0 * np.exp(-t_impact * 20) * np.sin(2 * np.pi * impact_freq * t_impact)
                signal[impact_idx:impact_idx + impact_samples] += impact
        
        # 添加噪声
        signal += 0.05 * np.random.randn(len(signal))
        return signal
    
    def _generate_fiber_wrinkle_signal(self):
        """生成纤维褶皱信号：中频周期性特征"""
        # 主频率成分
        freq1 = np.random.uniform(80, 120)
        freq2 = np.random.uniform(150, 200)
        
        signal = (0.8 * np.sin(2 * np.pi * freq1 * self.t) + 
                 0.4 * np.sin(2 * np.pi * freq2 * self.t))
        
        # 添加调制
        mod_freq = np.random.uniform(5, 15)
        signal *= (1 + 0.3 * np.sin(2 * np.pi * mod_freq * self.t))
        
        # 添加噪声
        signal += 0.08 * np.random.randn(len(signal))
        return signal
    
    def _generate_delamination_signal(self):
        """生成分层信号：低频振荡 + 间歇性脉冲"""
        # 低频基础振荡
        freq_base = np.random.uniform(20, 40)
        signal = 1.2 * np.sin(2 * np.pi * freq_base * self.t)
        
        # 添加间歇性脉冲
        num_pulses = np.random.randint(2, 5)
        for _ in range(num_pulses):
            pulse_time = np.random.uniform(0.3, 1.7)
            pulse_idx = int(pulse_time * self.fs)
            pulse_width = int(0.1 * self.fs)
            
            if pulse_idx + pulse_width < len(signal):
                pulse_freq = np.random.uniform(60, 100)
                t_pulse = np.linspace(0, 0.1, pulse_width)
                pulse = 1.5 * np.exp(-t_pulse * 10) * np.sin(2 * np.pi * pulse_freq * t_pulse)
                signal[pulse_idx:pulse_idx + pulse_width] += pulse
        
        # 添加噪声
        signal += 0.06 * np.random.randn(len(signal))
        return signal
    
    def _generate_bulge_signal(self):
        """生成鼓包信号：低频大幅度振荡"""
        # 低频大幅度振荡
        freq1 = np.random.uniform(10, 25)
        freq2 = np.random.uniform(30, 50)
        
        signal = (1.5 * np.sin(2 * np.pi * freq1 * self.t) + 
                 0.8 * np.sin(2 * np.pi * freq2 * self.t))
        
        # 添加缓慢变化的包络
        envelope_freq = np.random.uniform(1, 3)
        signal *= (1 + 0.4 * np.sin(2 * np.pi * envelope_freq * self.t))
        
        # 添加噪声
        signal += 0.04 * np.random.randn(len(signal))
        return signal
    
    def extract_time_domain_features(self, signal):
        """提取时域特征"""
        features = []
        
        # 统计特征
        features.append(np.mean(signal))           # 均值
        features.append(np.std(signal))            # 标准差
        features.append(np.max(signal))            # 最大值
        features.append(np.min(signal))            # 最小值
        features.append(np.max(signal) - np.min(signal))  # 峰峰值
        
        # 高阶统计特征
        features.append(np.mean(signal**2))        # 均方值
        features.append(np.sqrt(np.mean(signal**2)))  # 均方根值
        
        # 波形因子
        rms = np.sqrt(np.mean(signal**2))
        if np.mean(np.abs(signal)) != 0:
            features.append(rms / np.mean(np.abs(signal)))  # 波形因子
        else:
            features.append(0)
        
        # 峰值因子
        if rms != 0:
            features.append(np.max(np.abs(signal)) / rms)  # 峰值因子
        else:
            features.append(0)
        
        # 偏度和峰度
        features.append(np.mean((signal - np.mean(signal))**3) / (np.std(signal)**3))  # 偏度
        features.append(np.mean((signal - np.mean(signal))**4) / (np.std(signal)**4))  # 峰度
        
        return np.array(features)
    
    def extract_frequency_domain_features(self, signal):
        """提取频域特征"""
        # FFT变换
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/self.fs)
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        
        features = []
        
        # 频域统计特征
        features.append(np.mean(magnitude))        # 频域均值
        features.append(np.std(magnitude))         # 频域标准差
        features.append(np.max(magnitude))         # 频域最大值
        
        # 主频率
        dominant_freq_idx = np.argmax(magnitude)
        features.append(positive_freqs[dominant_freq_idx])  # 主频率
        
        # 频率重心
        if np.sum(magnitude) != 0:
            freq_centroid = np.sum(positive_freqs * magnitude) / np.sum(magnitude)
            features.append(freq_centroid)
        else:
            features.append(0)
        
        # 频带能量比
        low_freq_energy = np.sum(magnitude[positive_freqs <= 50])
        mid_freq_energy = np.sum(magnitude[(positive_freqs > 50) & (positive_freqs <= 200)])
        high_freq_energy = np.sum(magnitude[positive_freqs > 200])
        total_energy = np.sum(magnitude)
        
        if total_energy != 0:
            features.append(low_freq_energy / total_energy)   # 低频能量比
            features.append(mid_freq_energy / total_energy)   # 中频能量比
            features.append(high_freq_energy / total_energy)  # 高频能量比
        else:
            features.extend([0, 0, 0])
        
        return np.array(features), magnitude, positive_freqs

    def visualize_time_domain_signals(self, signals, labels, num_examples=4):
        """可视化时域信号"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()

        # 定义四种颜色：红、黄、蓝、绿
        colors = ['red', 'gold', 'blue', 'green']

        for i, defect_type in enumerate(self.defect_types):
            # 找到该类型的第一个样本
            defect_indices = np.where(labels == i)[0]
            if len(defect_indices) > 0:
                signal = signals[defect_indices[0]]

                axes[i].plot(self.t, signal, color=colors[i], linewidth=2.0)
                axes[i].set_title(f'{defect_type}缺陷时域信号', fontsize=14, fontweight='bold')
                axes[i].set_xlabel('时间 (s)', fontsize=12)
                axes[i].set_ylabel('幅值', fontsize=12)
                axes[i].grid(True, alpha=0.3)
                axes[i].set_xlim(0, self.duration)

                # 设置背景色为浅灰色，突出信号颜色
                axes[i].set_facecolor('#f8f9fa')

        plt.tight_layout()
        plt.savefig('defect_time_domain_signals.png', dpi=300, bbox_inches='tight')
        plt.close()

    def visualize_frequency_domain_signals(self, signals, labels):
        """可视化频域信号 - 四个信号在同一坐标轴"""
        fig, ax = plt.subplots(1, 1, figsize=(15, 8))

        # 定义四种颜色：红、黄、蓝、绿
        colors = ['red', 'gold', 'blue', 'green']

        for i, defect_type in enumerate(self.defect_types):
            # 找到该类型的第一个样本
            defect_indices = np.where(labels == i)[0]
            if len(defect_indices) > 0:
                signal = signals[defect_indices[0]]

                # FFT变换
                fft_signal = np.fft.fft(signal)
                freqs = np.fft.fftfreq(len(signal), 1/self.fs)
                positive_freqs = freqs[:len(freqs)//2]
                magnitude = np.abs(fft_signal[:len(fft_signal)//2])

                # 归一化处理便于比较
                magnitude_normalized = magnitude / np.max(magnitude)

                ax.plot(positive_freqs, magnitude_normalized,
                       color=colors[i], linewidth=2.5, label=f'{defect_type}缺陷', alpha=0.9)

        ax.set_title('四种缺陷频域信号对比', fontsize=16, fontweight='bold')
        ax.set_xlabel('频率 (Hz)', fontsize=14)
        ax.set_ylabel('归一化幅值', fontsize=14)
        ax.legend(fontsize=12, loc='upper right')
        ax.grid(True, alpha=0.3)
        ax.set_xlim(0, 250)  # 只显示0-250Hz范围
        ax.set_facecolor('#f8f9fa')  # 设置背景色

        plt.tight_layout()
        plt.savefig('fft_four_defects_same_axis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def train_knn_classifier(self, signals, labels, test_size=0.3, k=5):
        """训练KNN分类器"""
        # 提取特征
        print("正在提取特征...")
        all_features = []

        for signal in signals:
            # 时域特征
            time_features = self.extract_time_domain_features(signal)
            # 频域特征
            freq_features, _, _ = self.extract_frequency_domain_features(signal)
            # 合并特征
            combined_features = np.concatenate([time_features, freq_features])
            all_features.append(combined_features)

        X = np.array(all_features)
        y = labels

        # 数据标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=test_size, random_state=42, stratify=y
        )

        # 训练KNN分类器
        knn = KNeighborsClassifier(n_neighbors=k)
        knn.fit(X_train, y_train)

        # 预测
        y_pred = knn.predict(X_test)

        return knn, scaler, X_test, y_test, y_pred

    def visualize_classification_results(self, y_test, y_pred):
        """可视化分类结果"""
        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 混淆矩阵热图
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.defect_types, yticklabels=self.defect_types, ax=ax1)
        ax1.set_title('KNN分类混淆矩阵', fontsize=14, fontweight='bold')
        ax1.set_xlabel('预测类别', fontsize=12)
        ax1.set_ylabel('真实类别', fontsize=12)

        # 分类准确率柱状图
        accuracy_per_class = cm.diagonal() / cm.sum(axis=1)
        # 定义四种颜色：红、黄、蓝、绿
        colors = ['red', 'gold', 'blue', 'green']
        bars = ax2.bar(self.defect_types, accuracy_per_class,
                      color=colors, alpha=0.8)
        ax2.set_title('各类缺陷分类准确率', fontsize=14, fontweight='bold')
        ax2.set_xlabel('缺陷类型', fontsize=12)
        ax2.set_ylabel('准确率', fontsize=12)
        ax2.set_ylim(0, 1)

        # 在柱状图上添加数值标签
        for bar, acc in zip(bars, accuracy_per_class):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontsize=11)

        plt.tight_layout()
        plt.savefig('knn_classification_results.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 打印详细分类报告
        print("\n=== KNN分类详细报告 ===")
        print(classification_report(y_test, y_pred, target_names=self.defect_types))

    def run_complete_analysis(self, num_samples_per_class=50, k=5):
        """运行完整的缺陷信号分析流程"""
        print("=== 缺陷信号综合分析系统 ===")
        print(f"生成每类缺陷样本数: {num_samples_per_class}")
        print(f"KNN分类器K值: {k}")
        print()

        # 1. 生成缺陷信号
        print("1. 生成四种类型的缺陷信号...")
        signals, labels = self.generate_defect_signals(num_samples_per_class)
        print(f"   总共生成 {len(signals)} 个信号样本")

        # 2. 可视化时域信号
        print("2. 生成时域信号图...")
        self.visualize_time_domain_signals(signals, labels)

        # 3. 可视化频域信号
        print("3. 进行FFT变换并生成频域对比图...")
        self.visualize_frequency_domain_signals(signals, labels)

        # 4. 训练KNN分类器
        print("4. 训练KNN分类器...")
        knn, scaler, X_test, y_test, y_pred = self.train_knn_classifier(signals, labels, k=k)

        # 5. 可视化分类结果
        print("5. 生成分类结果可视化...")
        self.visualize_classification_results(y_test, y_pred)

        print("\n=== 分析完成 ===")
        print("生成的图片文件:")
        print("- defect_time_domain_signals.png: 时域信号图")
        print("- fft_four_defects_same_axis.png: 频域信号对比图")
        print("- knn_classification_results.png: KNN分类结果图")

        return knn, scaler


def main():
    """主函数"""
    # 创建分析器
    analyzer = DefectSignalAnalyzer(fs=1000, duration=2.0)

    # 运行完整分析
    knn_model, scaler = analyzer.run_complete_analysis(num_samples_per_class=50, k=5)

    print("\n程序运行完成！请查看生成的图片文件。")


if __name__ == "__main__":
    main()
