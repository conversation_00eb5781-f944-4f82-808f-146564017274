"""
简单的剪贴板功能模块
替代 pyperclip 的基本功能
"""
import subprocess
import sys
import os

def copy(text):
    """复制文本到剪贴板"""
    try:
        if sys.platform == 'win32':
            # Windows 系统
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            root.clipboard_clear()
            root.clipboard_append(text)
            root.update()
            root.destroy()
            return True
        elif sys.platform == 'darwin':
            # macOS 系统
            subprocess.run(['pbcopy'], input=text.encode('utf-8'), check=True)
            return True
        else:
            # Linux 系统
            try:
                subprocess.run(['xclip', '-selection', 'clipboard'], 
                             input=text.encode('utf-8'), check=True)
                return True
            except FileNotFoundError:
                try:
                    subprocess.run(['xsel', '--clipboard', '--input'], 
                                 input=text.encode('utf-8'), check=True)
                    return True
                except FileNotFoundError:
                    return False
    except Exception as e:
        print(f"复制到剪贴板失败: {e}")
        return False

def paste():
    """从剪贴板粘贴文本"""
    try:
        if sys.platform == 'win32':
            # Windows 系统
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            text = root.clipboard_get()
            root.destroy()
            return text
        elif sys.platform == 'darwin':
            # macOS 系统
            result = subprocess.run(['pbpaste'], capture_output=True, text=True, check=True)
            return result.stdout
        else:
            # Linux 系统
            try:
                result = subprocess.run(['xclip', '-selection', 'clipboard', '-o'], 
                                      capture_output=True, text=True, check=True)
                return result.stdout
            except FileNotFoundError:
                try:
                    result = subprocess.run(['xsel', '--clipboard', '--output'], 
                                          capture_output=True, text=True, check=True)
                    return result.stdout
                except FileNotFoundError:
                    return ""
    except Exception as e:
        print(f"从剪贴板粘贴失败: {e}")
        return ""

# 测试函数
if __name__ == "__main__":
    test_text = "Hello, Clipboard!"
    print(f"测试复制文本: {test_text}")
    
    if copy(test_text):
        print("复制成功!")
        pasted = paste()
        print(f"粘贴结果: {pasted}")
        if pasted.strip() == test_text:
            print("✓ 剪贴板功能正常!")
        else:
            print("✗ 剪贴板功能异常")
    else:
        print("✗ 复制失败")
