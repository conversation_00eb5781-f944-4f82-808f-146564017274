import numpy as np
import matplotlib.pyplot as plt
import pywt
from scipy import signal
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)

def generate_defect_signals(n_samples=1000, fs=1000):
    """
    生成三个通道的缺陷信号数据
    
    参数:
    n_samples: 每个信号的采样点数
    fs: 采样频率
    
    返回:
    signals: 三个通道的信号数据 (3, n_samples, 4种缺陷类型)
    labels: 缺陷类型标签
    """
    t = np.linspace(0, n_samples/fs, n_samples)
    
    # 定义四种缺陷类型的信号特征
    defect_types = ['纤维褶皱', '裂纹', '分层', '鼓包']
    n_defects = len(defect_types)
    
    # 初始化信号数组 (通道数, 样本数, 缺陷类型数)
    signals = np.zeros((3, n_samples, n_defects))
    
    # 通道1 (红色) - 主要反映低频特征
    for i, defect in enumerate(defect_types):
        if defect == '纤维褶皱':
            # 低频正弦波 + 调制
            base_freq = 5 + np.random.normal(0, 1)
            signals[0, :, i] = (2 + 0.5*np.sin(2*np.pi*1*t)) * np.sin(2*np.pi*base_freq*t)
        elif defect == '裂纹':
            # 高频脉冲信号
            base_freq = 50 + np.random.normal(0, 5)
            pulse_train = signal.square(2*np.pi*base_freq*t) * np.exp(-t*2)
            signals[0, :, i] = pulse_train + 0.5*np.sin(2*np.pi*10*t)
        elif defect == '分层':
            # 中频衰减振荡
            base_freq = 20 + np.random.normal(0, 3)
            signals[0, :, i] = 1.5 * np.exp(-t*1.5) * np.sin(2*np.pi*base_freq*t)
        elif defect == '鼓包':
            # 低频大幅度波动
            base_freq = 8 + np.random.normal(0, 2)
            signals[0, :, i] = 3 * np.sin(2*np.pi*base_freq*t) * (1 + 0.3*np.sin(2*np.pi*2*t))
    
    # 通道2 (蓝色) - 主要反映中频特征
    for i, defect in enumerate(defect_types):
        if defect == '纤维褶皱':
            # 中频调制信号
            base_freq = 15 + np.random.normal(0, 2)
            signals[1, :, i] = 1.5 * np.sin(2*np.pi*base_freq*t) * (1 + 0.4*np.cos(2*np.pi*3*t))
        elif defect == '裂纹':
            # 高频突发信号
            base_freq = 60 + np.random.normal(0, 8)
            burst = np.sin(2*np.pi*base_freq*t) * np.exp(-((t-0.5)**2)/0.1)
            signals[1, :, i] = burst + 0.8*np.sin(2*np.pi*25*t)
        elif defect == '分层':
            # 中频持续振荡
            base_freq = 30 + np.random.normal(0, 4)
            signals[1, :, i] = 2 * np.sin(2*np.pi*base_freq*t) + 0.5*np.sin(2*np.pi*45*t)
        elif defect == '鼓包':
            # 低中频混合
            signals[1, :, i] = 2.5*np.sin(2*np.pi*12*t) + 1*np.sin(2*np.pi*35*t)
    
    # 通道3 (绿色) - 主要反映高频特征
    for i, defect in enumerate(defect_types):
        if defect == '纤维褶皱':
            # 高频微弱信号
            base_freq = 80 + np.random.normal(0, 10)
            signals[2, :, i] = 0.8 * np.sin(2*np.pi*base_freq*t) + 0.3*np.sin(2*np.pi*120*t)
        elif defect == '裂纹':
            # 超高频尖峰
            base_freq = 100 + np.random.normal(0, 15)
            spikes = signal.square(2*np.pi*base_freq*t) * (1 + np.sin(2*np.pi*5*t))
            signals[2, :, i] = 2 * spikes
        elif defect == '分层':
            # 高频衰减
            base_freq = 70 + np.random.normal(0, 8)
            signals[2, :, i] = 1.2 * np.exp(-t*0.8) * np.sin(2*np.pi*base_freq*t)
        elif defect == '鼓包':
            # 高频调制
            base_freq = 90 + np.random.normal(0, 12)
            signals[2, :, i] = 1.8 * np.sin(2*np.pi*base_freq*t) * np.cos(2*np.pi*8*t)
    
    return signals, defect_types, t

def add_noise(signals, snr_db=10):
    """
    向信号添加高斯白噪声
    
    参数:
    signals: 原始信号
    snr_db: 信噪比(dB)
    
    返回:
    noisy_signals: 含噪声的信号
    """
    noisy_signals = np.zeros_like(signals)
    
    for ch in range(signals.shape[0]):
        for defect in range(signals.shape[2]):
            signal_power = np.mean(signals[ch, :, defect]**2)
            noise_power = signal_power / (10**(snr_db/10))
            noise = np.random.normal(0, np.sqrt(noise_power), signals.shape[1])
            noisy_signals[ch, :, defect] = signals[ch, :, defect] + noise
    
    return noisy_signals

def wavelet_denoise(signal_data, wavelet='db4', threshold_mode='soft'):
    """
    使用小波变换进行信号降噪
    
    参数:
    signal_data: 输入信号
    wavelet: 小波基函数
    threshold_mode: 阈值处理模式
    
    返回:
    denoised_signal: 降噪后的信号
    """
    # 小波分解
    coeffs = pywt.wavedec(signal_data, wavelet, level=6)
    
    # 估计噪声标准差（使用最高频细节系数）
    sigma = np.median(np.abs(coeffs[-1])) / 0.6745
    
    # 计算阈值
    threshold = sigma * np.sqrt(2 * np.log(len(signal_data)))
    
    # 阈值处理
    coeffs_thresh = list(coeffs)
    coeffs_thresh[1:] = [pywt.threshold(detail, threshold, threshold_mode) 
                        for detail in coeffs_thresh[1:]]
    
    # 小波重构
    denoised_signal = pywt.waverec(coeffs_thresh, wavelet)
    
    return denoised_signal

def extract_features(signals):
    """
    从信号中提取特征用于分类
    
    参数:
    signals: 信号数据 (通道数, 样本数, 缺陷类型数)
    
    返回:
    features: 特征矩阵
    """
    n_channels, n_samples, n_defects = signals.shape
    features = []
    
    for defect in range(n_defects):
        defect_features = []
        for ch in range(n_channels):
            signal_ch = signals[ch, :, defect]
            
            # 时域特征
            mean_val = np.mean(signal_ch)
            std_val = np.std(signal_ch)
            rms_val = np.sqrt(np.mean(signal_ch**2))
            peak_val = np.max(np.abs(signal_ch))
            
            # 频域特征
            fft_vals = np.fft.fft(signal_ch)
            power_spectrum = np.abs(fft_vals)**2
            dominant_freq = np.argmax(power_spectrum[:len(power_spectrum)//2])
            spectral_centroid = np.sum(np.arange(len(power_spectrum)) * power_spectrum) / np.sum(power_spectrum)
            
            defect_features.extend([mean_val, std_val, rms_val, peak_val, dominant_freq, spectral_centroid])
        
        features.append(defect_features)
    
    return np.array(features)

# 生成信号数据
print("正在生成三通道缺陷信号数据...")
signals_clean, defect_labels, time_axis = generate_defect_signals(n_samples=1000, fs=1000)

# 添加噪声
print("添加噪声...")
signals_noisy = add_noise(signals_clean, snr_db=5)

# 小波降噪
print("进行小波降噪...")
signals_denoised = np.zeros_like(signals_noisy)
for ch in range(3):
    for defect in range(4):
        signals_denoised[ch, :, defect] = wavelet_denoise(signals_noisy[ch, :, defect])

# 创建可视化
print("生成可视化图表...")
fig = plt.figure(figsize=(20, 15))

# 定义颜色
channel_colors = ['red', 'blue', 'green']
channel_names = ['通道1 (红色)', '通道2 (蓝色)', '通道3 (绿色)']

# 选择显示的时间范围（前200个采样点，便于观察）
time_range = slice(0, 200)
t_display = time_axis[time_range]

# 为每种缺陷类型创建子图
for defect_idx, defect_name in enumerate(defect_labels):
    # 原始信号
    ax1 = plt.subplot(4, 3, defect_idx*3 + 1)
    for ch in range(3):
        plt.plot(t_display, signals_clean[ch, time_range, defect_idx], 
                color=channel_colors[ch], label=channel_names[ch], linewidth=1.5, alpha=0.8)
    plt.title(f'{defect_name} - 原始信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 含噪声信号
    ax2 = plt.subplot(4, 3, defect_idx*3 + 2)
    for ch in range(3):
        plt.plot(t_display, signals_noisy[ch, time_range, defect_idx], 
                color=channel_colors[ch], label=channel_names[ch], linewidth=1.5, alpha=0.8)
    plt.title(f'{defect_name} - 含噪声信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 降噪后信号
    ax3 = plt.subplot(4, 3, defect_idx*3 + 3)
    for ch in range(3):
        plt.plot(t_display, signals_denoised[ch, time_range, defect_idx], 
                color=channel_colors[ch], label=channel_names[ch], linewidth=1.5, alpha=0.8)
    plt.title(f'{defect_name} - 小波降噪后')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.legend()
    plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('three_channel_wavelet_denoising.png', dpi=300, bbox_inches='tight')
print("三通道小波降噪图表已保存为 three_channel_wavelet_denoising.png")

# 特征提取和分类性能比较
print("\n进行特征提取和分类性能比较...")

# 提取特征
features_clean = extract_features(signals_clean)
features_noisy = extract_features(signals_noisy)
features_denoised = extract_features(signals_denoised)

# 创建标签
labels = np.arange(4)

# 标准化特征
scaler_clean = StandardScaler()
scaler_noisy = StandardScaler()
scaler_denoised = StandardScaler()

features_clean_scaled = scaler_clean.fit_transform(features_clean)
features_noisy_scaled = scaler_noisy.fit_transform(features_noisy)
features_denoised_scaled = scaler_denoised.fit_transform(features_denoised)

# 生成更多样本用于训练和测试
print("生成更多样本用于分类测试...")
n_samples_per_class = 50
all_features_clean = []
all_features_noisy = []
all_features_denoised = []
all_labels = []

for _ in range(n_samples_per_class):
    # 生成新的信号样本
    temp_signals_clean, _, _ = generate_defect_signals(n_samples=1000, fs=1000)
    temp_signals_noisy = add_noise(temp_signals_clean, snr_db=5)
    temp_signals_denoised = np.zeros_like(temp_signals_noisy)
    
    for ch in range(3):
        for defect in range(4):
            temp_signals_denoised[ch, :, defect] = wavelet_denoise(temp_signals_noisy[ch, :, defect])
    
    # 提取特征
    temp_features_clean = extract_features(temp_signals_clean)
    temp_features_noisy = extract_features(temp_signals_noisy)
    temp_features_denoised = extract_features(temp_signals_denoised)
    
    all_features_clean.extend(temp_features_clean)
    all_features_noisy.extend(temp_features_noisy)
    all_features_denoised.extend(temp_features_denoised)
    all_labels.extend(labels)

# 转换为numpy数组
all_features_clean = np.array(all_features_clean)
all_features_noisy = np.array(all_features_noisy)
all_features_denoised = np.array(all_features_denoised)
all_labels = np.array(all_labels)

# 训练和测试KNN分类器
def evaluate_classifier(features, labels, title):
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels, test_size=0.3, random_state=42, stratify=labels
    )
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    knn = KNeighborsClassifier(n_neighbors=5)
    knn.fit(X_train_scaled, y_train)
    y_pred = knn.predict(X_test_scaled)
    
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"\n{title}:")
    print(f"分类准确率: {accuracy:.3f}")
    print("详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=defect_labels))
    
    return accuracy

# 评估三种情况下的分类性能
acc_clean = evaluate_classifier(all_features_clean, all_labels, "原始信号分类性能")
acc_noisy = evaluate_classifier(all_features_noisy, all_labels, "含噪声信号分类性能")
acc_denoised = evaluate_classifier(all_features_denoised, all_labels, "小波降噪后信号分类性能")

# 创建性能对比图
fig2 = plt.figure(figsize=(10, 6))
categories = ['原始信号', '含噪声信号', '小波降噪后']
accuracies = [acc_clean, acc_noisy, acc_denoised]
colors = ['green', 'red', 'blue']

bars = plt.bar(categories, accuracies, color=colors, alpha=0.7)
plt.ylim(0, 1)
plt.ylabel('分类准确率')
plt.title('三通道信号小波降噪前后分类性能对比')
plt.grid(True, alpha=0.3)

# 在柱状图上添加数值
for bar, acc in zip(bars, accuracies):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.savefig('classification_performance_comparison.png', dpi=300, bbox_inches='tight')
print("\n分类性能对比图已保存为 classification_performance_comparison.png")

print(f"\n总结:")
print(f"原始信号分类准确率: {acc_clean:.3f}")
print(f"含噪声信号分类准确率: {acc_noisy:.3f}")
print(f"小波降噪后分类准确率: {acc_denoised:.3f}")
print(f"降噪改善效果: {acc_denoised - acc_noisy:.3f}")
