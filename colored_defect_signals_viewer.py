import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from defect_signal_comprehensive_analysis import DefectSignalAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_colored_signal_comparison():
    """创建彩色缺陷信号对比图"""
    
    # 创建分析器
    analyzer = DefectSignalAnalyzer(fs=1000, duration=2.0)
    
    # 生成信号
    signals, labels = analyzer.generate_defect_signals(num_samples_per_class=1)
    
    # 定义颜色和缺陷类型
    colors = ['red', 'gold', 'blue', 'green']
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    color_names = ['红色', '黄色', '蓝色', '绿色']
    
    # 创建大图显示所有信号
    fig = plt.figure(figsize=(20, 12))
    
    # 主标题
    fig.suptitle('四种缺陷信号彩色对比分析', fontsize=24, fontweight='bold', y=0.95)
    
    # 1. 时域信号对比 (2x2子图)
    for i in range(4):
        ax = plt.subplot(3, 4, i+1)
        signal = signals[i]
        
        ax.plot(analyzer.t, signal, color=colors[i], linewidth=2.5, alpha=0.8)
        ax.set_title(f'{defect_types[i]}缺陷\n({color_names[i]})', 
                    fontsize=14, fontweight='bold', color=colors[i])
        ax.set_xlabel('时间 (s)', fontsize=11)
        ax.set_ylabel('幅值', fontsize=11)
        ax.grid(True, alpha=0.3)
        ax.set_xlim(0, analyzer.duration)
        ax.set_facecolor('#f8f9fa')
    
    # 2. 频域信号对比 (单个大图)
    ax_freq = plt.subplot(3, 1, 2)
    
    for i in range(4):
        signal = signals[i]
        
        # FFT变换
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/analyzer.fs)
        positive_freqs = freqs[:len(freqs)//2]
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        
        # 归一化处理
        magnitude_normalized = magnitude / np.max(magnitude)
        
        ax_freq.plot(positive_freqs, magnitude_normalized, 
                    color=colors[i], linewidth=3, 
                    label=f'{defect_types[i]}缺陷 ({color_names[i]})', alpha=0.9)
    
    ax_freq.set_title('四种缺陷频域信号对比', fontsize=16, fontweight='bold')
    ax_freq.set_xlabel('频率 (Hz)', fontsize=14)
    ax_freq.set_ylabel('归一化幅值', fontsize=14)
    ax_freq.legend(fontsize=12, loc='upper right')
    ax_freq.grid(True, alpha=0.3)
    ax_freq.set_xlim(0, 250)
    ax_freq.set_facecolor('#f8f9fa')
    
    # 3. 颜色说明图例
    ax_legend = plt.subplot(3, 1, 3)
    ax_legend.axis('off')
    
    # 创建颜色说明
    legend_text = "颜色对应关系说明:\n\n"
    for i, (defect, color, color_name) in enumerate(zip(defect_types, colors, color_names)):
        legend_text += f"● {defect}缺陷 → {color_name}\n"
    
    ax_legend.text(0.1, 0.8, legend_text, fontsize=16, fontweight='bold',
                  verticalalignment='top', transform=ax_legend.transAxes)
    
    # 添加特征说明
    feature_text = """
    信号特征说明:
    
    🔥 裂纹缺陷 (红色): 高频冲击信号，突发性脉冲特征
    🌊 纤维褶皱 (黄色): 中频周期性振荡，规律性波动模式  
    📈 分层缺陷 (蓝色): 低频振荡伴随间歇性脉冲
    🎈 鼓包缺陷 (绿色): 低频大幅度振荡，缓慢变化包络
    """
    
    ax_legend.text(0.5, 0.8, feature_text, fontsize=14,
                  verticalalignment='top', transform=ax_legend.transAxes)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92, hspace=0.4)
    plt.savefig('colored_defect_signals_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 彩色缺陷信号对比图已生成: colored_defect_signals_comparison.png")

def create_single_axis_comparison():
    """创建单轴对比图"""
    analyzer = DefectSignalAnalyzer(fs=1000, duration=2.0)
    signals, labels = analyzer.generate_defect_signals(num_samples_per_class=1)
    
    colors = ['red', 'gold', 'blue', 'green']
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10))
    
    # 时域信号在同一轴
    for i in range(4):
        signal = signals[i]
        # 为了便于比较，对信号进行偏移
        offset = i * 3
        ax1.plot(analyzer.t, signal + offset, color=colors[i], 
                linewidth=2.5, label=f'{defect_types[i]}缺陷', alpha=0.9)
    
    ax1.set_title('四种缺陷时域信号对比 (同一坐标轴)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('时间 (s)', fontsize=14)
    ax1.set_ylabel('幅值 (带偏移)', fontsize=14)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_facecolor('#f8f9fa')
    
    # 频域信号在同一轴
    for i in range(4):
        signal = signals[i]
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/analyzer.fs)
        positive_freqs = freqs[:len(freqs)//2]
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        magnitude_normalized = magnitude / np.max(magnitude)
        
        ax2.plot(positive_freqs, magnitude_normalized, 
                color=colors[i], linewidth=2.5, 
                label=f'{defect_types[i]}缺陷', alpha=0.9)
    
    ax2.set_title('四种缺陷频域信号对比 (同一坐标轴)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('频率 (Hz)', fontsize=14)
    ax2.set_ylabel('归一化幅值', fontsize=14)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, 250)
    ax2.set_facecolor('#f8f9fa')
    
    plt.tight_layout()
    plt.savefig('single_axis_colored_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 单轴彩色对比图已生成: single_axis_colored_comparison.png")

if __name__ == "__main__":
    print("🎨 正在生成彩色缺陷信号对比图...")
    create_colored_signal_comparison()
    create_single_axis_comparison()
    print("🎉 所有彩色图表生成完成！")
