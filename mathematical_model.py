"""
2024年高教社杯全国大学生数学建模竞赛B题：生产过程中的决策问题
数学模型建立与求解
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from scipy.stats import binom
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ProductionDecisionModel:
    """生产过程决策模型"""
    
    def __init__(self):
        # 基础参数（这些需要根据具体题目数据调整）
        self.defect_rate_1 = 0.10  # 零配件1次品率
        self.defect_rate_2 = 0.10  # 零配件2次品率
        self.cost_component_1 = 4   # 零配件1购买成本
        self.cost_component_2 = 18  # 零配件2购买成本
        self.cost_assembly = 6      # 装配成本
        self.cost_inspection_1 = 2  # 零配件1检测成本
        self.cost_inspection_2 = 3  # 零配件2检测成本
        self.cost_disassembly = 5   # 拆解成本
        self.market_price = 56      # 成品市场售价
        self.replacement_cost = 6   # 调换损失
        
    def calculate_product_defect_rate(self, p1, p2):
        """计算成品次品率"""
        return p1 + p2 - p1 * p2
    
    def inspection_scheme_analysis(self, claimed_rate, confidence_level=0.95):
        """
        问题1：抽样检测方案设计
        
        参数:
        claimed_rate: 供应商声称的次品率
        confidence_level: 置信水平
        """
        print(f"=== 问题1：抽样检测方案设计 ===")
        print(f"供应商声称次品率不超过: {claimed_rate*100}%")
        print(f"置信水平: {confidence_level*100}%")
        
        # 设计抽样方案
        sample_sizes = [50, 100, 200, 500]
        results = []
        
        for n in sample_sizes:
            # 在声称次品率下，样本中次品数的期望和方差
            expected_defects = n * claimed_rate
            variance = n * claimed_rate * (1 - claimed_rate)
            
            # 使用二项分布计算临界值
            # 如果观察到的次品数超过临界值，则拒绝供应商声称
            alpha = 1 - confidence_level
            critical_value = binom.ppf(confidence_level, n, claimed_rate)
            
            # 计算检测成本
            if claimed_rate == 0.10:  # 假设是零配件1
                inspection_cost = n * self.cost_inspection_1
            else:
                inspection_cost = n * self.cost_inspection_2
            
            results.append({
                '样本量': n,
                '期望次品数': expected_defects,
                '临界值': critical_value,
                '检测成本': inspection_cost,
                '拒绝概率': 1 - binom.cdf(critical_value, n, claimed_rate)
            })
        
        df_results = pd.DataFrame(results)
        print(df_results)
        
        return df_results
    
    def production_decision_analysis(self):
        """
        问题2：生产过程各阶段决策分析
        """
        print(f"\n=== 问题2：生产过程决策分析 ===")
        
        # 情况分析
        scenarios = [
            {"name": "情况1", "inspect_1": True, "inspect_2": False, "disassemble": True},
            {"name": "情况2", "inspect_1": False, "inspect_2": True, "disassemble": False},
            {"name": "情况3", "inspect_1": True, "inspect_2": True, "disassemble": True},
            {"name": "情况4", "inspect_1": False, "inspect_2": False, "disassemble": False}
        ]
        
        results = []
        
        for scenario in scenarios:
            profit = self.calculate_expected_profit(
                scenario["inspect_1"], 
                scenario["inspect_2"], 
                scenario["disassemble"]
            )
            results.append({
                "决策方案": scenario["name"],
                "检测零配件1": scenario["inspect_1"],
                "检测零配件2": scenario["inspect_2"],
                "拆解不合格品": scenario["disassemble"],
                "期望利润": profit
            })
        
        df_results = pd.DataFrame(results)
        print(df_results)
        
        # 找出最优决策
        best_scenario = df_results.loc[df_results['期望利润'].idxmax()]
        print(f"\n最优决策方案: {best_scenario['决策方案']}")
        print(f"最大期望利润: {best_scenario['期望利润']:.2f}")
        
        return df_results
    
    def calculate_expected_profit(self, inspect_1, inspect_2, disassemble):
        """计算期望利润"""
        
        # 有效零配件率（经过检测后）
        if inspect_1:
            effective_rate_1 = 1 - self.defect_rate_1  # 检测后剔除次品
            cost_1 = self.cost_component_1 + self.cost_inspection_1
        else:
            effective_rate_1 = 1 - self.defect_rate_1  # 未检测，仍有次品
            cost_1 = self.cost_component_1
            
        if inspect_2:
            effective_rate_2 = 1 - self.defect_rate_2
            cost_2 = self.cost_component_2 + self.cost_inspection_2
        else:
            effective_rate_2 = 1 - self.defect_rate_2
            cost_2 = self.cost_component_2
        
        # 成品合格率
        if inspect_1 and inspect_2:
            # 两个零配件都检测，成品合格率为1
            product_quality_rate = 1.0
        elif inspect_1 and not inspect_2:
            # 只检测零配件1
            product_quality_rate = 1 - self.defect_rate_2
        elif not inspect_1 and inspect_2:
            # 只检测零配件2
            product_quality_rate = 1 - self.defect_rate_1
        else:
            # 都不检测
            product_quality_rate = (1 - self.defect_rate_1) * (1 - self.defect_rate_2)
        
        # 计算各种成本和收益
        total_component_cost = cost_1 + cost_2
        assembly_cost = self.cost_assembly
        
        # 合格品收益
        qualified_revenue = product_quality_rate * self.market_price
        
        # 不合格品处理
        defect_rate = 1 - product_quality_rate
        if disassemble and defect_rate > 0:
            # 拆解：需要支付拆解费用，但可能回收部分零配件
            disassemble_cost = defect_rate * self.cost_disassembly
            # 简化假设：拆解后可回收50%的零配件价值
            recovery_value = defect_rate * 0.5 * (self.cost_component_1 + self.cost_component_2)
            defect_handling_cost = disassemble_cost - recovery_value
        else:
            # 报废：直接损失
            defect_handling_cost = defect_rate * (total_component_cost + assembly_cost)
        
        # 市场调换损失
        market_loss = defect_rate * self.replacement_cost
        
        # 期望利润
        expected_profit = (qualified_revenue - total_component_cost - assembly_cost 
                          - defect_handling_cost - market_loss)
        
        return expected_profit
    
    def sensitivity_analysis(self):
        """敏感性分析"""
        print(f"\n=== 敏感性分析 ===")
        
        # 分析次品率变化对决策的影响
        defect_rates = np.arange(0.05, 0.20, 0.01)
        profits_no_inspect = []
        profits_inspect_all = []
        
        original_rate_1 = self.defect_rate_1
        original_rate_2 = self.defect_rate_2
        
        for rate in defect_rates:
            self.defect_rate_1 = rate
            self.defect_rate_2 = rate
            
            # 不检测的利润
            profit_no = self.calculate_expected_profit(False, False, False)
            profits_no_inspect.append(profit_no)
            
            # 全检测的利润
            profit_all = self.calculate_expected_profit(True, True, True)
            profits_inspect_all.append(profit_all)
        
        # 恢复原始值
        self.defect_rate_1 = original_rate_1
        self.defect_rate_2 = original_rate_2
        
        # 绘图
        plt.figure(figsize=(10, 6))
        plt.plot(defect_rates, profits_no_inspect, label='不检测策略', marker='o')
        plt.plot(defect_rates, profits_inspect_all, label='全检测策略', marker='s')
        plt.xlabel('次品率')
        plt.ylabel('期望利润')
        plt.title('次品率对不同策略期望利润的影响')
        plt.legend()
        plt.grid(True)
        plt.savefig('sensitivity_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return defect_rates, profits_no_inspect, profits_inspect_all

def main():
    """主函数"""
    model = ProductionDecisionModel()
    
    # 问题1：抽样检测方案
    print("开始求解问题1...")
    inspection_results = model.inspection_scheme_analysis(0.10)
    
    # 问题2：生产决策分析
    print("\n开始求解问题2...")
    decision_results = model.production_decision_analysis()
    
    # 敏感性分析
    print("\n进行敏感性分析...")
    model.sensitivity_analysis()
    
    return model, inspection_results, decision_results

if __name__ == "__main__":
    model, inspection_results, decision_results = main()
