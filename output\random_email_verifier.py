#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机邮箱验证码获取工具
基于Miko邮箱系统API
设计风格参考Augment
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import random
import string
import threading
import time
import pyperclip
from datetime import datetime

class RandomEmailVerifier:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        
        # API配置
        self.base_url = "http://***************:8080"
        self.username = "kimi"
        self.password = "tgx123456"
        self.session = requests.Session()
        self.token = None
        self.current_email = None
        self.mailbox_id = None
        self.monitoring_active = False  # 监听状态标志

        # 创建界面
        self.create_widgets()
        
        # 自动登录
        self.auto_login()
        
    def setup_window(self):
        """设置窗口"""
        self.root.title("随机邮箱验证码获取工具")
        self.root.geometry("1000x800")
        self.root.configure(bg='#1a1a2e')
        self.root.resizable(False, False)

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
            
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', 
                       background='#1a1a2e', 
                       foreground='#ffffff', 
                       font=('Microsoft YaHei UI', 24, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background='#1a1a2e', 
                       foreground='#8b8b8b', 
                       font=('Microsoft YaHei UI', 12))
        
        style.configure('Info.TLabel', 
                       background='#1a1a2e', 
                       foreground='#ffffff', 
                       font=('Microsoft YaHei UI', 11))
        
        style.configure('Purple.TButton',
                       background='#6c5ce7',
                       foreground='white',
                       font=('Microsoft YaHei UI', 11, 'bold'),
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Purple.TButton',
                 background=[('active', '#5f4fcf'),
                           ('pressed', '#4834d4')])
        
        style.configure('Green.TButton',
                       background='#00b894',
                       foreground='white',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Green.TButton',
                 background=[('active', '#00a085'),
                           ('pressed', '#008f76')])
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = tk.Frame(self.root, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True, padx=40, pady=40)
        
        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#1a1a2e')
        title_frame.pack(fill='x', pady=(0, 30))
        
        # 图标和标题
        icon_label = tk.Label(title_frame, text="📧", font=('Arial', 40), bg='#1a1a2e', fg='#6c5ce7')
        icon_label.pack()
        
        title_label = ttk.Label(title_frame, text="随机邮箱验证码", style='Title.TLabel')
        title_label.pack(pady=(10, 5))
        
        subtitle_label = ttk.Label(title_frame, text="基于Miko邮箱系统的临时邮箱服务", style='Subtitle.TLabel')
        subtitle_label.pack()
        
        # 状态区域
        status_frame = tk.Frame(main_frame, bg='#16213e', relief='solid', bd=1)
        status_frame.pack(fill='x', pady=(0, 20), ipady=15)
        
        self.status_label = ttk.Label(status_frame, text="正在初始化...", style='Info.TLabel')
        self.status_label.pack()
        
        # 邮箱信息区域
        email_frame = tk.Frame(main_frame, bg='#16213e', relief='solid', bd=1)
        email_frame.pack(fill='x', pady=(0, 20), ipady=20)
        
        email_title = ttk.Label(email_frame, text="当前邮箱", style='Info.TLabel')
        email_title.pack()
        
        # 邮箱显示容器
        email_container = tk.Frame(email_frame, bg='#16213e')
        email_container.pack(pady=(5, 0))

        self.email_var = tk.StringVar(value="暂无邮箱")
        self.email_label = tk.Label(email_container, textvariable=self.email_var,
                                   font=('Consolas', 14, 'bold'),
                                   bg='#16213e', fg='#6c5ce7',
                                   cursor='hand2')
        self.email_label.pack(side='left', padx=(0, 10))

        # 邮箱复制按钮
        self.email_copy_btn = ttk.Button(email_container, text="复制邮箱",
                                        style='Green.TButton',
                                        command=self.copy_email,
                                        state='disabled')
        self.email_copy_btn.pack(side='left')

        # 绑定邮箱点击事件
        self.email_label.bind("<Button-1>", lambda e: self.copy_email())
        
        # 按钮区域
        button_frame = tk.Frame(main_frame, bg='#1a1a2e')
        button_frame.pack(fill='x', pady=(0, 20))
        
        self.generate_btn = ttk.Button(button_frame, text="生成随机邮箱", 
                                      style='Purple.TButton', command=self.generate_random_email)
        self.generate_btn.pack(pady=5, ipadx=20, ipady=10)
        
        self.refresh_btn = ttk.Button(button_frame, text="刷新验证码", 
                                     style='Green.TButton', command=self.refresh_codes)
        self.refresh_btn.pack(pady=5, ipadx=20, ipady=8)
        
        # 验证码显示区域
        codes_frame = tk.Frame(main_frame, bg='#16213e', relief='solid', bd=1)
        codes_frame.pack(fill='both', expand=True, pady=(0, 20), ipady=20)

        codes_title = ttk.Label(codes_frame, text="验证码列表", style='Info.TLabel')
        codes_title.pack(pady=(10, 5))

        # 创建滚动区域
        canvas_frame = tk.Frame(codes_frame, bg='#16213e')
        canvas_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(canvas_frame, bg='#2d3748', highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#2d3748')

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        self.bind_mousewheel()

        # 初始化显示
        self.display_no_codes()

    def display_no_codes(self):
        """显示无验证码状态"""
        # 清空现有内容
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 显示等待消息
        no_codes_label = tk.Label(
            self.scrollable_frame,
            text="等待接收验证码...",
            bg='#2d3748',
            fg='#a0aec0',
            font=('Microsoft YaHei', 12),
            pady=50
        )
        no_codes_label.pack(fill='x', padx=20)
        
    def bind_mousewheel(self):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        self.canvas.bind("<MouseWheel>", _on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", _on_mousewheel)
        
    def auto_login(self):
        """自动登录"""
        def login_thread():
            try:
                self.update_status("正在登录...")

                # 登录请求 - 使用正确的API端点
                login_data = {
                    "username": self.username,
                    "password": self.password
                }

                response = self.session.post(f"{self.base_url}/api/login", json=login_data)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        # 登录成功，使用Session Cookie认证
                        self.update_status("登录成功！")
                        self.root.after(1000, lambda: self.update_status("请生成随机邮箱"))
                    else:
                        self.update_status(f"登录失败: {result.get('message', '未知错误')}")
                else:
                    self.update_status(f"登录失败: HTTP {response.status_code}")

            except Exception as e:
                self.update_status(f"登录异常: {str(e)}")

        threading.Thread(target=login_thread, daemon=True).start()
    
    def delete_current_mailbox(self):
        """删除当前邮箱"""
        if self.mailbox_id:
            try:
                response = self.session.delete(f"{self.base_url}/api/mailboxes/{self.mailbox_id}")
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"邮箱 {self.current_email} 已删除")
                    else:
                        print(f"删除邮箱失败: {result.get('message', '未知错误')}")
                else:
                    print(f"删除邮箱失败: HTTP {response.status_code}")
            except Exception as e:
                print(f"删除邮箱异常: {str(e)}")

    def generate_random_email(self):
        """生成随机邮箱"""
        def generate_thread():
            try:
                # 如果已有邮箱，先停止监听并删除旧邮箱
                if self.current_email and self.mailbox_id:
                    self.update_status("正在删除旧邮箱...")
                    self.stop_monitoring()  # 停止旧的监听
                    self.delete_current_mailbox()
                    # 清空验证码列表
                    self.root.after(0, lambda: self.display_codes([]))

                self.update_status("正在生成随机邮箱...")

                # 首先获取可用域名
                domains_response = self.session.get(f"{self.base_url}/api/domains/available")
                if domains_response.status_code != 200:
                    self.update_status("获取可用域名失败")
                    return

                domains_result = domains_response.json()
                if not domains_result.get('success') or not domains_result.get('data'):
                    self.update_status("没有可用域名")
                    return

                # 选择第一个可用域名
                domain = domains_result['data'][0]
                domain_id = domain.get('id', 1)
                domain_name = domain.get('name', 'youddns.site')

                # 生成随机邮箱前缀
                random_prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                email_address = f"{random_prefix}@{domain_name}"

                # 创建邮箱 - 使用正确的API格式
                mailbox_data = {
                    "prefix": random_prefix,
                    "domain_id": domain_id,
                    "password": "123456"  # 固定密码
                }

                response = self.session.post(f"{self.base_url}/api/mailboxes", json=mailbox_data)

                if response.status_code == 200 or response.status_code == 201:
                    result = response.json()
                    if result.get('success'):
                        self.current_email = email_address
                        self.mailbox_id = result.get('data', {}).get('id')
                        self.email_var.set(email_address)

                        # 启用复制按钮
                        self.root.after(0, lambda: self.email_copy_btn.config(state='normal'))

                        self.update_status("邮箱生成成功！等待接收验证码...")

                        # 开始监听验证码
                        self.start_monitoring()
                    else:
                        self.update_status(f"创建邮箱失败: {result.get('message', '未知错误')}")
                else:
                    self.update_status(f"创建邮箱失败: HTTP {response.status_code}")

            except Exception as e:
                self.update_status(f"生成邮箱异常: {str(e)}")

        threading.Thread(target=generate_thread, daemon=True).start()
    
    def start_monitoring(self):
        """开始监听验证码"""
        self.monitoring_active = True

        def monitor_thread():
            while self.current_email and self.monitoring_active:
                try:
                    self.fetch_codes_silently()
                    time.sleep(5)  # 每5秒检查一次
                except Exception as e:
                    print(f"监听异常: {e}")
                    time.sleep(10)

        threading.Thread(target=monitor_thread, daemon=True).start()

    def fetch_codes_silently(self):
        """静默获取验证码（用于监听）"""
        if not self.current_email:
            return

        try:
            # 获取验证码
            params = {
                "mailbox": self.current_email,
                "limit": 10
            }

            response = self.session.get(f"{self.base_url}/api/emails/verification-code", params=params)
            print(f"API响应状态: {response.status_code}")
            print(f"API响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    codes_data = result.get('data', [])
                    if codes_data is None:
                        codes_data = []
                    print(f"获取到验证码数据: {codes_data}")
                    self.root.after(0, lambda: self.display_codes(codes_data))
                else:
                    print(f"获取验证码失败: {result.get('message', '未知错误')}")
            else:
                print(f"获取验证码失败: HTTP {response.status_code}")

        except Exception as e:
            print(f"获取验证码异常: {str(e)}")

    def stop_monitoring(self):
        """停止监听验证码"""
        self.monitoring_active = False
    
    def refresh_codes(self):
        """刷新验证码"""
        if not self.current_email:
            self.update_status("请先生成邮箱")
            return

        def refresh_thread():
            try:
                self.update_status("正在刷新验证码...")

                # 获取验证码
                params = {
                    "mailbox": self.current_email,
                    "limit": 10
                }

                response = self.session.get(f"{self.base_url}/api/emails/verification-code", params=params)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        codes_data = result.get('data', [])
                        if codes_data is None:
                            codes_data = []
                        self.root.after(0, lambda: self.display_codes(codes_data))
                        self.update_status(f"已刷新，找到 {len(codes_data)} 个验证码")
                    else:
                        self.update_status(f"获取验证码失败: {result.get('message', '未知错误')}")
                        print(f"获取验证码失败: {result.get('message', '未知错误')}")
                else:
                    self.update_status(f"获取验证码失败: HTTP {response.status_code}")
                    print(f"获取验证码失败: HTTP {response.status_code}")
                    print(f"响应内容: {response.text}")

            except Exception as e:
                self.update_status(f"刷新验证码异常: {str(e)}")
                print(f"刷新验证码异常: {str(e)}")

        threading.Thread(target=refresh_thread, daemon=True).start()
    
    def display_codes(self, codes_data):
        """显示验证码"""
        print(f"display_codes 被调用，数据: {codes_data}")

        if not codes_data:
            print("没有验证码数据，显示暂无验证码")
            self.display_no_codes()
            return

        print(f"开始显示 {len(codes_data)} 个验证码项")

        # 清空现有内容
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 为每个邮件创建验证码项
        for i, code_info in enumerate(codes_data):
            codes = code_info.get('codes', [])
            if codes:  # 只显示有验证码的邮件
                for code in codes:
                    self.create_code_item(code, code_info)

    def create_code_item(self, code, email_info):
        """创建单个验证码项目"""
        # 主容器
        item_frame = tk.Frame(self.scrollable_frame, bg='#2d3748', pady=5)
        item_frame.pack(fill='x', padx=20, pady=2)

        # 验证码容器（深色背景，类似图片中的样式）
        code_container = tk.Frame(item_frame, bg='#1a202c', relief='solid', bd=1)
        code_container.pack(fill='x', pady=2)

        # 内部框架
        inner_frame = tk.Frame(code_container, bg='#1a202c')
        inner_frame.pack(fill='x', padx=15, pady=10)

        # 验证码文本（左侧）
        code_label = tk.Label(
            inner_frame,
            text=code,
            bg='#1a202c',
            fg='#e2e8f0',
            font=('Consolas', 14, 'bold'),
            anchor='w'
        )
        code_label.pack(side='left', fill='x', expand=True)

        # 复制按钮（右侧，类似图片中的样式）
        copy_btn = tk.Button(
            inner_frame,
            text="复制",
            bg='#4a5568',
            fg='#e2e8f0',
            font=('Microsoft YaHei', 10),
            relief='flat',
            padx=15,
            pady=5,
            cursor='hand2',
            command=lambda c=code: self.copy_code(c)
        )
        copy_btn.pack(side='right')

        # 鼠标悬停效果
        def on_enter(e):
            copy_btn.config(bg='#2d3748')

        def on_leave(e):
            copy_btn.config(bg='#4a5568')

        copy_btn.bind("<Enter>", on_enter)
        copy_btn.bind("<Leave>", on_leave)

    def copy_code(self, code):
        """复制验证码到剪贴板"""
        try:
            # 使用 tkinter 的剪贴板功能
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            self.root.update()  # 确保剪贴板更新

            # 显示复制成功提示
            self.show_copy_success()
            self.update_status(f"已复制验证码: {code}")
            print(f"已复制验证码: {code}")
        except Exception as e:
            self.update_status(f"复制失败: {str(e)}")
            print(f"复制失败: {e}")

    def show_copy_success(self):
        """显示复制成功提示"""
        # 创建临时提示标签
        success_label = tk.Label(
            self.root,
            text="✓ 已复制",
            bg='#48bb78',
            fg='white',
            font=('Microsoft YaHei', 10),
            padx=10,
            pady=5
        )
        success_label.place(relx=0.5, rely=0.1, anchor='center')

        # 1秒后自动消失
        self.root.after(1000, success_label.destroy)

    def copy_email(self):
        """复制邮箱到剪贴板"""
        if not self.current_email:
            self.update_status("暂无邮箱可复制")
            messagebox.showwarning("提示", "暂无邮箱可复制")
            return

        try:
            pyperclip.copy(self.current_email)
            self.update_status(f"已复制邮箱: {self.current_email}")
            messagebox.showinfo("成功", f"邮箱 {self.current_email} 已复制到剪贴板！")
        except Exception as e:
            self.update_status(f"复制邮箱失败: {str(e)}")
            messagebox.showerror("错误", f"复制邮箱失败: {str(e)}")
    
    def update_status(self, message):
        """更新状态"""
        self.root.after(0, lambda: self.status_label.config(text=message))

    def on_closing(self):
        """窗口关闭事件处理"""
        def cleanup_thread():
            try:
                # 停止监听
                self.stop_monitoring()

                if self.current_email and self.mailbox_id:
                    print(f"正在清理邮箱: {self.current_email}")
                    self.delete_current_mailbox()
                    print("邮箱清理完成")
            except Exception as e:
                print(f"清理邮箱时出错: {str(e)}")
            finally:
                # 确保窗口关闭
                self.root.after(0, self.root.destroy)

        # 在后台线程中执行清理操作
        threading.Thread(target=cleanup_thread, daemon=True).start()

        # 给清理操作一些时间
        self.root.after(2000, self.root.destroy)  # 2秒后强制关闭

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = RandomEmailVerifier()
    app.run()
