import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_annotation_process_visualization():
    """创建标注过程可视化"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    fig.suptitle('缺陷严重程度标注过程可视化', fontsize=20, fontweight='bold')
    
    # 严重程度颜色定义
    severity_colors = {
        1: '#2ECC71',  # 绿色 - 轻微
        2: '#F39C12',  # 橙色 - 中等
        3: '#E67E22',  # 深橙色 - 严重
        4: '#E74C3C'   # 红色 - 危险
    }
    
    # 1. 原始信号展示 (左上)
    ax1 = axes[0, 0]
    t = np.linspace(0, 2, 1000)
    
    # 生成不同严重程度的裂纹信号
    for severity in range(1, 5):
        # 基础信号
        signal = 0.1 * np.sin(2 * np.pi * 50 * t)
        
        # 根据严重程度添加不同强度的冲击
        num_impacts = severity
        impact_amplitude = severity * 0.8
        
        for _ in range(num_impacts):
            impact_time = np.random.uniform(0.2, 1.8)
            impact_idx = int(impact_time * 500)
            if impact_idx < len(signal) - 25:
                impact = impact_amplitude * np.exp(-np.arange(25) * 0.2) * np.sin(2 * np.pi * 300 * np.arange(25) / 500)
                signal[impact_idx:impact_idx+25] += impact
        
        # 添加噪声
        signal += 0.02 * severity * np.random.randn(len(signal))
        
        # 绘制信号
        ax1.plot(t, signal + (severity-1) * 4, color=severity_colors[severity], 
                linewidth=2, label=f'等级{severity}', alpha=0.8)
    
    ax1.set_title('不同严重程度的裂纹信号', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('幅值 (带偏移)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 特征提取展示 (中上)
    ax2 = axes[0, 1]
    
    # 模拟特征值随严重程度变化
    severities = range(1, 5)
    signal_amplitude = [1.2, 2.8, 5.2, 8.5]
    peak_frequency = [180, 220, 280, 350]
    energy_ratio = [0.2, 0.4, 0.7, 0.9]
    
    ax2_twin1 = ax2.twinx()
    ax2_twin2 = ax2.twinx()
    ax2_twin2.spines['right'].set_position(('outward', 60))
    
    line1 = ax2.plot(severities, signal_amplitude, 'o-', color='red', linewidth=3, 
                     markersize=8, label='信号幅值')
    line2 = ax2_twin1.plot(severities, peak_frequency, 's-', color='blue', linewidth=3, 
                          markersize=8, label='主频率')
    line3 = ax2_twin2.plot(severities, energy_ratio, '^-', color='green', linewidth=3, 
                          markersize=8, label='高频能量比')
    
    ax2.set_title('特征值随严重程度变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('严重程度等级')
    ax2.set_ylabel('信号幅值', color='red')
    ax2_twin1.set_ylabel('主频率 (Hz)', color='blue')
    ax2_twin2.set_ylabel('高频能量比', color='green')
    
    # 设置颜色
    ax2.tick_params(axis='y', labelcolor='red')
    ax2_twin1.tick_params(axis='y', labelcolor='blue')
    ax2_twin2.tick_params(axis='y', labelcolor='green')
    
    ax2.set_xticks(severities)
    ax2.grid(True, alpha=0.3)
    
    # 合并图例
    lines = line1 + line2 + line3
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper left')
    
    # 3. 标注界面模拟 (右上)
    ax3 = axes[0, 2]
    ax3.axis('off')
    
    # 模拟标注界面
    interface_box = FancyBboxPatch((0.1, 0.1), 0.8, 0.8, boxstyle="round,pad=0.05", 
                                  facecolor='lightgray', edgecolor='black', linewidth=2)
    ax3.add_patch(interface_box)
    
    ax3.text(0.5, 0.85, '缺陷标注界面', fontsize=14, fontweight='bold', ha='center', transform=ax3.transAxes)
    
    # 缺陷类型选择
    ax3.text(0.15, 0.75, '缺陷类型:', fontsize=11, fontweight='bold', transform=ax3.transAxes)
    ax3.text(0.35, 0.75, '☑ 裂纹', fontsize=10, transform=ax3.transAxes, color='red')
    
    # 严重程度选择
    ax3.text(0.15, 0.65, '严重程度:', fontsize=11, fontweight='bold', transform=ax3.transAxes)
    for i, (severity, color) in enumerate(severity_colors.items()):
        y_pos = 0.55 - i * 0.08
        circle = Circle((0.2, y_pos), 0.02, facecolor=color, transform=ax3.transAxes)
        ax3.add_patch(circle)
        selected = '●' if severity == 3 else '○'
        ax3.text(0.25, y_pos, f'{selected} 等级{severity}', fontsize=10, transform=ax3.transAxes)
    
    # 特征值显示
    ax3.text(0.15, 0.25, '特征值:', fontsize=11, fontweight='bold', transform=ax3.transAxes)
    ax3.text(0.15, 0.20, '长度: 22.5mm', fontsize=10, transform=ax3.transAxes)
    ax3.text(0.15, 0.15, '深度: 5.8mm', fontsize=10, transform=ax3.transAxes)
    ax3.text(0.15, 0.10, '信号幅值: 5.2', fontsize=10, transform=ax3.transAxes)
    
    # 4. 严重程度分布饼图 (左下)
    ax4 = axes[1, 0]
    
    severity_counts = [60, 60, 60, 60]
    colors = list(severity_colors.values())
    labels = [f'等级{i}\n({["轻微", "中等", "严重", "危险"][i-1]})' for i in range(1, 5)]
    
    wedges, texts, autotexts = ax4.pie(severity_counts, labels=labels, autopct='%1.1f%%',
                                      colors=colors, startangle=90, textprops={'fontsize': 10})
    ax4.set_title('严重程度标注分布', fontsize=14, fontweight='bold')
    
    # 5. 风险评分分布 (中下)
    ax5 = axes[1, 1]
    
    # 生成风险评分数据
    np.random.seed(42)
    for severity in range(1, 5):
        base_score = {1: 15, 2: 35, 3: 65, 4: 90}[severity]
        scores = np.random.normal(base_score, 8, 50)
        scores = np.clip(scores, 0, 100)
        
        ax5.hist(scores, bins=15, alpha=0.7, color=severity_colors[severity], 
                label=f'等级{severity}', density=True)
    
    ax5.set_title('风险评分分布', fontsize=14, fontweight='bold')
    ax5.set_xlabel('风险评分')
    ax5.set_ylabel('密度')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 维修策略统计 (右下)
    ax6 = axes[1, 2]
    
    strategies = ['继续监测', '加强监测', '计划维修', '立即停机']
    strategy_counts = [60, 60, 60, 60]
    
    bars = ax6.bar(strategies, strategy_counts, color=list(severity_colors.values()), alpha=0.8)
    ax6.set_title('维修策略分布', fontsize=14, fontweight='bold')
    ax6.set_ylabel('样本数量')
    
    # 旋转x轴标签
    plt.setp(ax6.get_xticklabels(), rotation=45, ha='right')
    
    # 添加数值标签
    for bar, count in zip(bars, strategy_counts):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{count}', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('severity_annotation_process.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 标注过程可视化已生成: severity_annotation_process.png")

def create_severity_comparison_chart():
    """创建严重程度对比图表"""
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    
    # 缺陷类型和严重程度数据
    defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    severity_colors = ['#2ECC71', '#F39C12', '#E67E22', '#E74C3C']
    
    # 创建模拟数据：每种缺陷在不同严重程度下的特征
    np.random.seed(42)
    
    x_pos = np.arange(len(defect_types))
    width = 0.2
    
    # 四个严重程度的数据
    severity_data = {
        1: [15, 12, 18, 10],  # 轻微
        2: [35, 28, 38, 25],  # 中等
        3: [65, 55, 70, 50],  # 严重
        4: [90, 85, 95, 80]   # 危险
    }
    
    # 绘制分组柱状图
    for i, (severity, data) in enumerate(severity_data.items()):
        offset = (i - 1.5) * width
        bars = ax.bar(x_pos + offset, data, width, 
                     label=f'等级{severity}', color=severity_colors[i], alpha=0.8)
        
        # 添加数值标签
        for bar, value in zip(bars, data):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{value}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax.set_title('四种缺陷类型的严重程度风险评分对比', fontsize=16, fontweight='bold')
    ax.set_xlabel('缺陷类型', fontsize=14)
    ax.set_ylabel('风险评分', fontsize=14)
    ax.set_xticks(x_pos)
    ax.set_xticklabels(defect_types)
    ax.legend(title='严重程度等级', fontsize=12)
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim(0, 105)
    
    # 添加风险区域背景
    ax.axhspan(0, 25, alpha=0.1, color='green', label='低风险区')
    ax.axhspan(25, 50, alpha=0.1, color='orange', label='中风险区')
    ax.axhspan(50, 80, alpha=0.1, color='red', label='高风险区')
    ax.axhspan(80, 100, alpha=0.1, color='darkred', label='极高风险区')
    
    # 添加风险区域标签
    ax.text(3.5, 12.5, '低风险区', fontsize=10, ha='center', color='green', fontweight='bold')
    ax.text(3.5, 37.5, '中风险区', fontsize=10, ha='center', color='orange', fontweight='bold')
    ax.text(3.5, 65, '高风险区', fontsize=10, ha='center', color='red', fontweight='bold')
    ax.text(3.5, 90, '极高风险区', fontsize=10, ha='center', color='darkred', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('severity_comparison_chart.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 严重程度对比图表已生成: severity_comparison_chart.png")

def create_severity_signal_examples():
    """创建不同严重程度的信号示例"""
    
    fig, axes = plt.subplots(4, 2, figsize=(16, 16))
    
    fig.suptitle('四个严重程度等级的缺陷信号示例', fontsize=20, fontweight='bold')
    
    severity_colors = ['#2ECC71', '#F39C12', '#E67E22', '#E74C3C']
    severity_names = ['轻微', '中等', '严重', '危险']
    
    t = np.linspace(0, 2, 1000)
    np.random.seed(42)
    
    for severity in range(1, 5):
        # 时域信号 (左列)
        ax_time = axes[severity-1, 0]
        
        # 生成该严重程度的信号
        signal = 0.1 * np.sin(2 * np.pi * 50 * t)
        
        # 根据严重程度调整特征
        impact_amplitude = severity * 0.6
        impact_frequency = 200 + severity * 50
        num_impacts = severity
        
        for _ in range(num_impacts):
            impact_time = np.random.uniform(0.2, 1.8)
            impact_idx = int(impact_time * 500)
            if impact_idx < len(signal) - 30:
                impact = impact_amplitude * np.exp(-np.arange(30) * 0.15) * np.sin(2 * np.pi * impact_frequency * np.arange(30) / 500)
                signal[impact_idx:impact_idx+30] += impact
        
        signal += 0.02 * severity * np.random.randn(len(signal))
        
        ax_time.plot(t, signal, color=severity_colors[severity-1], linewidth=2)
        ax_time.set_title(f'等级{severity} ({severity_names[severity-1]}) - 时域信号', 
                         fontsize=12, fontweight='bold', color=severity_colors[severity-1])
        ax_time.set_xlabel('时间 (s)')
        ax_time.set_ylabel('幅值')
        ax_time.grid(True, alpha=0.3)
        ax_time.set_xlim(0, 2)
        
        # 添加严重程度标识
        ax_time.text(0.02, 0.95, f'风险评分: {15 + (severity-1)*25}', 
                    transform=ax_time.transAxes, fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # 频域信号 (右列)
        ax_freq = axes[severity-1, 1]
        
        # FFT变换
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/500)
        positive_freqs = freqs[:len(freqs)//2]
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        
        ax_freq.plot(positive_freqs, magnitude, color=severity_colors[severity-1], linewidth=2)
        ax_freq.set_title(f'等级{severity} ({severity_names[severity-1]}) - 频域信号', 
                         fontsize=12, fontweight='bold', color=severity_colors[severity-1])
        ax_freq.set_xlabel('频率 (Hz)')
        ax_freq.set_ylabel('幅值')
        ax_freq.grid(True, alpha=0.3)
        ax_freq.set_xlim(0, 250)
        
        # 添加主频率标识
        main_freq_idx = np.argmax(magnitude)
        main_freq = positive_freqs[main_freq_idx]
        ax_freq.axvline(x=main_freq, color='red', linestyle='--', alpha=0.7)
        ax_freq.text(main_freq + 10, np.max(magnitude) * 0.8, f'主频: {main_freq:.1f}Hz', 
                    fontsize=9, color='red', fontweight='bold')
        
        # 添加维修策略
        strategies = ['继续监测', '加强监测', '计划维修', '立即停机']
        ax_freq.text(0.02, 0.95, f'维修策略: {strategies[severity-1]}', 
                    transform=ax_freq.transAxes, fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=severity_colors[severity-1], 
                             alpha=0.8, edgecolor='black'), color='white')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('severity_signal_examples.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 严重程度信号示例已生成: severity_signal_examples.png")

def main():
    """主函数"""
    print("🎨 开始创建缺陷严重程度标注可视化...")
    
    # 1. 创建标注过程可视化
    print("1. 生成标注过程可视化...")
    create_annotation_process_visualization()
    
    # 2. 创建严重程度对比图表
    print("2. 生成严重程度对比图表...")
    create_severity_comparison_chart()
    
    # 3. 创建不同严重程度的信号示例
    print("3. 生成严重程度信号示例...")
    create_severity_signal_examples()
    
    print("\n🎉 所有严重程度标注可视化图表生成完成！")
    print("生成的文件:")
    print("• severity_annotation_process.png - 标注过程可视化")
    print("• severity_comparison_chart.png - 严重程度对比图表")
    print("• severity_signal_examples.png - 严重程度信号示例")


if __name__ == "__main__":
    main()
