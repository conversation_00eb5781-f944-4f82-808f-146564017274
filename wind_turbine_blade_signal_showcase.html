<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风机叶片内腔微小声信号处理与VI级小波降噪系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
            color: white;
            border-radius: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .step-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #FF6B6B;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin: 0 auto 15px auto;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .step-description {
            font-size: 0.9em;
            color: #666;
        }
        
        .image-container {
            text-align: center;
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .image-title {
            font-size: 1.4em;
            font-weight: bold;
            margin: 15px 0 10px 0;
            color: #333;
        }
        .image-description {
            color: #666;
            font-size: 1em;
            line-height: 1.4;
        }
        
        .section-title {
            font-size: 2em;
            color: #333;
            margin: 40px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 3px solid #4ECDC4;
            text-align: center;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
        }
        
        .tech-specs {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .results-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        .results-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        .results-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .improvement-highlight {
            background-color: #e8f5e8;
            color: #2e7d32;
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .header h1 {
                font-size: 1.8em;
            }
            .stats-grid, .process-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌪️ 风机叶片内腔微小声信号处理与VI级小波降噪系统</h1>
            <p>专业的微小声信号检测、放大与降噪处理解决方案</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">0.047 mV</div>
                <div class="stat-label">原始微小信号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">40 dB</div>
                <div class="stat-label">信号放大增益</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">VI级</div>
                <div class="stat-label">小波分解级数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">7.67 dB</div>
                <div class="stat-label">SNR改善</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">58.6%</div>
                <div class="stat-label">RMSE降低</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">98.8%</div>
                <div class="stat-label">相关性提升</div>
            </div>
        </div>

        <div class="highlight-box">
            <h3>🎯 系统特点</h3>
            <p>本系统专门针对风机叶片内腔的微小声信号进行处理，模拟真实的工业环境中叶片振动、气流湍流、结构共振、轴承噪声等多种信号源。通过40dB高增益放大和VI级小波降噪技术，实现了对微弱信号的有效提取和噪声抑制，为风机状态监测提供了可靠的技术支撑。</p>
        </div>

        <h2 class="section-title">🔄 信号处理流程</h2>
        <div class="process-steps">
            <div class="step-card">
                <div class="step-number">1</div>
                <div class="step-title">微小声信号生成</div>
                <div class="step-description">模拟风机叶片内腔的真实声学环境，包含叶片振动、湍流噪声、结构共振等成分</div>
            </div>
            <div class="step-card">
                <div class="step-number">2</div>
                <div class="step-title">环境噪声添加</div>
                <div class="step-description">加入白噪声、工频干扰、低频漂移等真实环境中的噪声成分</div>
            </div>
            <div class="step-card">
                <div class="step-number">3</div>
                <div class="step-title">信号放大处理</div>
                <div class="step-description">采用40dB高增益放大，将微小信号提升到可处理的幅度范围</div>
            </div>
            <div class="step-card">
                <div class="step-number">4</div>
                <div class="step-title">VI级小波降噪</div>
                <div class="step-description">使用6级小波分解，自适应阈值处理，有效去除噪声成分</div>
            </div>
        </div>

        <h2 class="section-title">📊 主要处理流程可视化</h2>
        <div class="image-container">
            <img src="wind_turbine_blade_signal_processing.png" alt="风机叶片信号处理流程">
            <div class="image-title">风机叶片内腔微小声信号处理完整流程</div>
            <div class="image-description">
                展示了从原始微小声信号生成、噪声添加、信号放大到VI级小波降噪的完整处理流程。
                包含时域分析、频域分析、信号对比和小波分解阈值等关键信息。
            </div>
        </div>

        <h2 class="section-title">🔬 VI级小波分解详细分析</h2>
        <div class="image-container">
            <img src="wavelet_decomposition_vi_levels.png" alt="VI级小波分解分析">
            <div class="image-title">VI级小波分解系数与阈值处理</div>
            <div class="image-description">
                详细展示了6级小波分解的各层系数变化，包括近似系数A6和细节系数D1-D6。
                左侧为原始系数，右侧为阈值处理后的系数，红色虚线表示各级阈值。
            </div>
        </div>

        <h2 class="section-title">📈 性能指标分析</h2>
        <div class="image-container">
            <img src="signal_performance_metrics.png" alt="信号性能指标分析">
            <div class="image-title">信号处理性能综合评估</div>
            <div class="image-description">
                包含SNR改善分析、信号幅度对比、各频段能量分析和相关性分析四个维度的性能评估。
                全面展示了VI级小波降噪在各个方面的处理效果。
            </div>
        </div>

        <h2 class="section-title">📋 详细性能统计</h2>
        <table class="results-table">
            <thead>
                <tr>
                    <th>性能指标</th>
                    <th>处理前</th>
                    <th>处理后</th>
                    <th>改善效果</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>信噪比 (SNR)</strong></td>
                    <td>-10.02 dB</td>
                    <td>-2.36 dB</td>
                    <td><span class="improvement-highlight">+7.67 dB</span></td>
                </tr>
                <tr>
                    <td><strong>均方根误差 (RMSE)</strong></td>
                    <td>5.9884</td>
                    <td>2.4775</td>
                    <td><span class="improvement-highlight">-58.6%</span></td>
                </tr>
                <tr>
                    <td><strong>相关系数</strong></td>
                    <td>0.3136</td>
                    <td>0.6233</td>
                    <td><span class="improvement-highlight">+98.8%</span></td>
                </tr>
                <tr>
                    <td><strong>信号最大幅度</strong></td>
                    <td>26.994 V</td>
                    <td>保持稳定</td>
                    <td><span class="improvement-highlight">噪声显著降低</span></td>
                </tr>
            </tbody>
        </table>

        <h2 class="section-title">🔧 技术参数详情</h2>
        <div class="tech-specs">
            <h3>信号特征参数</h3>
            <ul>
                <li><strong>原始微小信号幅度</strong>: 0.047205 V (47.2 mV)</li>
                <li><strong>噪声水平</strong>: 0.056701 V (56.7 mV)</li>
                <li><strong>采样频率</strong>: 2000 Hz</li>
                <li><strong>信号持续时间</strong>: 5秒</li>
                <li><strong>数据点数</strong>: 10,000个采样点</li>
            </ul>
            
            <h3>信号放大参数</h3>
            <ul>
                <li><strong>放大增益</strong>: 40.0 dB (100倍线性增益)</li>
                <li><strong>放大后最大幅度</strong>: 26.994 V</li>
                <li><strong>放大器类型</strong>: 理想线性放大</li>
            </ul>
            
            <h3>VI级小波降噪参数</h3>
            <ul>
                <li><strong>小波基函数</strong>: Daubechies 8 (db8)</li>
                <li><strong>分解级数</strong>: VI级 (6级分解)</li>
                <li><strong>阈值模式</strong>: 软阈值 (soft thresholding)</li>
                <li><strong>阈值策略</strong>: 自适应分级阈值</li>
                <li><strong>各级阈值</strong>: D1(26.543) → D2(31.851) → D3(38.221) → D4(45.866) → D5(55.039) → D6(66.047)</li>
            </ul>
        </div>

        <h2 class="section-title">📋 结论与应用</h2>
        <div class="highlight-box">
            <h4>🎯 处理效果总结</h4>
            <ul>
                <li><strong>显著的SNR改善</strong>: 从-10.02 dB提升到-2.36 dB，改善7.67 dB</li>
                <li><strong>大幅降低误差</strong>: RMSE从5.9884降低到2.4775，降低58.6%</li>
                <li><strong>相关性大幅提升</strong>: 从0.3136提升到0.6233，提升98.8%</li>
                <li><strong>有效保持信号特征</strong>: 在去除噪声的同时很好地保留了原始信号的主要特征</li>
            </ul>
            
            <h4>🔬 技术优势</h4>
            <ul>
                <li><strong>VI级分解</strong>: 6级小波分解提供了精细的频率分辨率</li>
                <li><strong>自适应阈值</strong>: 不同级别采用不同阈值策略，优化降噪效果</li>
                <li><strong>软阈值处理</strong>: 保持信号连续性，避免硬阈值的突变效应</li>
                <li><strong>多维度评估</strong>: 时域、频域、统计指标全面评估处理效果</li>
            </ul>
            
            <h4>🏭 工业应用价值</h4>
            <ul>
                <li><strong>风机状态监测</strong>: 实时监测叶片内腔的微小声学变化</li>
                <li><strong>故障预警</strong>: 通过信号特征变化预测潜在故障</li>
                <li><strong>维护优化</strong>: 为预测性维护提供可靠的数据支撑</li>
                <li><strong>质量控制</strong>: 在制造过程中检测叶片内部缺陷</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <p style="color: #666; margin: 0;">
                <strong>系统版本:</strong> v1.0 | 
                <strong>处理时间:</strong> 2025-08-19 | 
                <strong>技术方案:</strong> VI级小波降噪 | 
                <strong>SNR改善:</strong> 7.67 dB
            </p>
        </div>
    </div>
</body>
</html>
