<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缺陷数据库建立完整指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2.2em;
            color: #4a5568;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            font-size: 1.2em;
            margin-right: 15px;
        }

        .section-description {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.8;
        }

        .image-container {
            text-align: center;
            margin: 25px 0;
        }

        .workflow-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .workflow-image:hover {
            transform: scale(1.02);
        }

        .image-caption {
            font-style: italic;
            color: #666;
            margin-top: 15px;
            font-size: 1em;
        }

        .process-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .process-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .process-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .process-card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .process-card p {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 10px;
        }

        .process-card ul {
            color: #666;
            font-size: 0.9em;
            padding-left: 20px;
        }

        .process-card li {
            margin-bottom: 5px;
        }

        .timeline {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .timeline h3 {
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .timeline-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .timeline-content h4 {
            color: #4a5568;
            margin-bottom: 5px;
        }

        .timeline-content p {
            color: #666;
            font-size: 0.9em;
        }

        .timeline-duration {
            background: #e9ecef;
            color: #495057;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            margin-left: auto;
            flex-shrink: 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-3px);
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .tech-stack {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
        }

        .tech-stack h4 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tech-tag {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .tech-tag:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 50px;
            padding: 30px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .section-title {
                font-size: 1.8em;
            }
            
            .container {
                padding: 15px;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 缺陷数据库建立完整指南</h1>
            <p>从数据采集到智能分析的全流程解决方案 | 构建高质量、可扩展的缺陷识别数据库</p>
        </div>

        <!-- 概述 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🎯</span>
                项目概述
            </h2>
            <p class="section-description">
                缺陷数据库建立是一个系统性工程，涉及多源数据采集、智能预处理、特征工程、专家标注、
                数据库设计等多个环节。本指南提供了完整的技术路线和实施方案。
            </p>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">100K+</div>
                    <div class="metric-label">信号样本</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">12+</div>
                    <div class="metric-label">缺陷类型</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">50+</div>
                    <div class="metric-label">特征维度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">95%+</div>
                    <div class="metric-label">标注准确率</div>
                </div>
            </div>
        </div>

        <!-- 建立流程 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🔄</span>
                数据库建立流程
            </h2>
            <p class="section-description">
                完整的数据库建立流程包含六个主要阶段，每个阶段都有明确的目标、方法和质量标准。
            </p>
            
            <div class="image-container">
                <img src="defect_database_construction_workflow.png" alt="数据库建立流程图" class="workflow-image">
                <p class="image-caption">缺陷数据库建立完整流程图</p>
            </div>
        </div>

        <!-- 数据流程 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🌊</span>
                数据流程架构
            </h2>
            <p class="section-description">
                从多源数据采集到最终应用的完整数据流程，确保数据的高质量和可追溯性。
            </p>
            
            <div class="image-container">
                <img src="defect_database_data_flow.png" alt="数据流程图" class="workflow-image">
                <p class="image-caption">多源数据融合与处理流程</p>
            </div>
        </div>

        <!-- 特征提取 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🧠</span>
                特征提取工程
            </h2>
            <p class="section-description">
                采用多维特征提取策略，从时域、频域、时频域三个维度全面刻画缺陷信号特征。
            </p>
            
            <div class="image-container">
                <img src="feature_extraction_workflow.png" alt="特征提取流程图" class="workflow-image">
                <p class="image-caption">多维特征提取与融合流程</p>
            </div>
        </div>

        <!-- 实施步骤 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📋</span>
                详细实施步骤
            </h2>
            
            <div class="process-grid">
                <div class="process-card">
                    <h3>🔧 阶段1：数据采集</h3>
                    <p><strong>目标：</strong>获取高质量的多源缺陷信号</p>
                    <ul>
                        <li>超声波检测：2-10MHz，50MHz采样</li>
                        <li>声发射监测：100kHz-1MHz，2MHz采样</li>
                        <li>振动分析：0-20kHz，50kHz采样</li>
                        <li>网格化空间采样策略</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h3>🔍 阶段2：数据预处理</h3>
                    <p><strong>目标：</strong>提升信号质量，去除噪声干扰</p>
                    <ul>
                        <li>滤波去噪：带通、高通、低通滤波</li>
                        <li>信号增强：小波去噪、自适应滤波</li>
                        <li>质量检查：SNR、饱和度、完整性</li>
                        <li>数据标准化与归一化</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h3>⚙️ 阶段3：特征提取</h3>
                    <p><strong>目标：</strong>提取多维度缺陷特征</p>
                    <ul>
                        <li>时域特征：统计量、形状因子</li>
                        <li>频域特征：频谱分析、能量分布</li>
                        <li>时频特征：小波变换、短时傅里叶</li>
                        <li>特征选择与降维优化</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h3>🏷️ 阶段4：数据标注</h3>
                    <p><strong>目标：</strong>建立高质量标注数据集</p>
                    <ul>
                        <li>标注体系设计：类型、严重程度</li>
                        <li>专家标注培训与质量控制</li>
                        <li>多专家一致性验证</li>
                        <li>标注工具开发与优化</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h3>🗃️ 阶段5：数据库构建</h3>
                    <p><strong>目标：</strong>设计高效的数据存储系统</p>
                    <ul>
                        <li>数据库架构设计</li>
                        <li>索引优化与查询性能</li>
                        <li>数据压缩与分层存储</li>
                        <li>API接口开发</li>
                    </ul>
                </div>
                
                <div class="process-card">
                    <h3>🚀 阶段6：验证部署</h3>
                    <p><strong>目标：</strong>系统验证与正式部署</p>
                    <ul>
                        <li>模型训练与验证</li>
                        <li>系统集成测试</li>
                        <li>性能优化与调优</li>
                        <li>用户培训与文档</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 实施时间表 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📅</span>
                实施时间表
            </h2>
            
            <div class="timeline">
                <h3>项目实施时间线</h3>
                
                <div class="timeline-item">
                    <div class="timeline-number">1</div>
                    <div class="timeline-content">
                        <h4>准备与规划</h4>
                        <p>需求分析、技术调研、设备选型、人员培训</p>
                    </div>
                    <div class="timeline-duration">2-3周</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-number">2</div>
                    <div class="timeline-content">
                        <h4>数据采集</h4>
                        <p>设备部署、信号采集、数据质量监控</p>
                    </div>
                    <div class="timeline-duration">8-12周</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-number">3</div>
                    <div class="timeline-content">
                        <h4>数据处理</h4>
                        <p>预处理算法、特征提取、质量控制</p>
                    </div>
                    <div class="timeline-duration">6-8周</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-number">4</div>
                    <div class="timeline-content">
                        <h4>标注与验证</h4>
                        <p>专家标注、一致性检查、质量验证</p>
                    </div>
                    <div class="timeline-duration">8-10周</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-number">5</div>
                    <div class="timeline-content">
                        <h4>数据库构建</h4>
                        <p>系统设计、数据导入、接口开发</p>
                    </div>
                    <div class="timeline-duration">4-6周</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-number">6</div>
                    <div class="timeline-content">
                        <h4>验证部署</h4>
                        <p>系统测试、性能优化、正式上线</p>
                    </div>
                    <div class="timeline-duration">3-4周</div>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">💻</span>
                技术栈与工具
            </h2>
            
            <div class="tech-stack">
                <h4>🔧 数据采集与处理</h4>
                <div class="tech-tags">
                    <span class="tech-tag">Python</span>
                    <span class="tech-tag">NumPy</span>
                    <span class="tech-tag">SciPy</span>
                    <span class="tech-tag">PyWavelets</span>
                    <span class="tech-tag">Matplotlib</span>
                </div>
            </div>
            
            <div class="tech-stack">
                <h4>🤖 机器学习与分析</h4>
                <div class="tech-tags">
                    <span class="tech-tag">scikit-learn</span>
                    <span class="tech-tag">TensorFlow</span>
                    <span class="tech-tag">PyTorch</span>
                    <span class="tech-tag">Pandas</span>
                    <span class="tech-tag">Seaborn</span>
                </div>
            </div>
            
            <div class="tech-stack">
                <h4>🗄️ 数据库与存储</h4>
                <div class="tech-tags">
                    <span class="tech-tag">SQLite</span>
                    <span class="tech-tag">PostgreSQL</span>
                    <span class="tech-tag">MongoDB</span>
                    <span class="tech-tag">Redis</span>
                    <span class="tech-tag">HDF5</span>
                </div>
            </div>
            
            <div class="tech-stack">
                <h4>🌐 Web开发与部署</h4>
                <div class="tech-tags">
                    <span class="tech-tag">Flask</span>
                    <span class="tech-tag">FastAPI</span>
                    <span class="tech-tag">Docker</span>
                    <span class="tech-tag">Kubernetes</span>
                    <span class="tech-tag">Nginx</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🔬 缺陷数据库建立完整指南 | 基于多源信号融合的智能缺陷识别解决方案</p>
            <p>💡 从数据采集到智能分析的全流程技术实现 | 构建高质量、可扩展的工业级数据库</p>
        </div>
    </div>
</body>
</html>
