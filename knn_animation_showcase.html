
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KNN算法原理动画演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .animation-container {
            text-align: center;
            margin: 30px 0;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .animation-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        .card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            position: relative;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: -12px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .step h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-left: 20px;
        }
        .step p {
            margin-left: 20px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 KNN算法原理动画演示</h1>
        
        <div class="highlight">
            <h3>🎯 动画展示内容</h3>
            <p><strong>实时演示KNN算法的工作过程</strong> - 观察k值变化如何影响分类结果，理解"近朱者赤，近墨者黑"的算法思想</p>
        </div>

        <div class="animation-container">
            <h2>🎥 KNN算法动态演示</h2>
            <img src="knn_animation.gif" alt="KNN算法动画" style="border: 3px solid #3498db;">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                左图：KNN分类过程可视化 | 右图：实时投票结果统计
            </p>
        </div>

        <div class="animation-container">
            <h2>📊 不同k值效果对比</h2>
            <img src="knn_k_comparison.png" alt="KNN不同k值对比">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                展示k=1,3,5,7,11,15时的分类效果和置信度变化
            </p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎬 动画特色</h3>
                <ul>
                    <li><strong>实时k值变化</strong>：从1到15循环变化</li>
                    <li><strong>动态连线显示</strong>：实时显示到最近邻的距离</li>
                    <li><strong>决策圆圈</strong>：可视化k邻域范围</li>
                    <li><strong>投票过程</strong>：实时统计各类别票数</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>📊 可视化元素</h3>
                <ul>
                    <li><strong>训练数据点</strong>：三种缺陷类型用不同颜色和形状</li>
                    <li><strong>测试点</strong>：红色五角星标记</li>
                    <li><strong>最近邻高亮</strong>：红色边框突出显示</li>
                    <li><strong>距离连线</strong>：灰色虚线连接</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎯 算法演示</h3>
                <ul>
                    <li><strong>邻居选择</strong>：动态选择k个最近的训练样本</li>
                    <li><strong>距离计算</strong>：欧几里得距离可视化</li>
                    <li><strong>投票决策</strong>：多数投票确定分类结果</li>
                    <li><strong>置信度计算</strong>：显示预测的可信程度</li>
                </ul>
            </div>
        </div>

        <div class="steps">
            <h2>🔄 KNN算法执行步骤</h2>
            <div class="step">
                <h4>计算距离</h4>
                <p>计算测试点到所有训练样本的欧几里得距离</p>
            </div>
            <div class="step">
                <h4>选择邻居</h4>
                <p>按距离排序，选择k个最近的邻居样本</p>
            </div>
            <div class="step">
                <h4>投票决策</h4>
                <p>统计k个邻居的类别标签，进行多数投票</p>
            </div>
            <div class="step">
                <h4>输出结果</h4>
                <p>输出得票最多的类别作为预测结果</p>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🔍 观察要点</h3>
                <ul>
                    <li><strong>k值影响</strong>：观察k值变化对预测结果的影响</li>
                    <li><strong>决策边界</strong>：注意决策圆圈的大小变化</li>
                    <li><strong>置信度变化</strong>：关注预测置信度的波动</li>
                    <li><strong>类别分布</strong>：理解邻域内类别的分布特点</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>💡 算法洞察</h3>
                <ul>
                    <li><strong>k=1时</strong>：容易受噪声影响，决策边界复杂</li>
                    <li><strong>k较大时</strong>：决策更稳定，但可能过于平滑</li>
                    <li><strong>奇数k值</strong>：避免投票平局的情况</li>
                    <li><strong>局部性原理</strong>：相似样本倾向于同类</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎨 技术实现</h3>
                <ul>
                    <li><strong>matplotlib动画</strong>：使用FuncAnimation创建</li>
                    <li><strong>GIF格式</strong>：便于网页展示和分享</li>
                    <li><strong>实时计算</strong>：每帧重新计算邻居和投票</li>
                    <li><strong>交互式可视化</strong>：直观理解算法原理</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <h3>🚀 学习价值</h3>
            <p>通过动画演示，您可以直观地理解KNN算法的工作原理，观察k值选择对分类结果的影响，这对于理解机器学习中的"偏差-方差权衡"概念非常有帮助。</p>
        </div>
    </div>
</body>
</html>