import numpy as np
import pandas as pd
import sqlite3
import json
import pickle
import zlib
from datetime import datetime
import hashlib
import os

class DefectDatabaseManager:
    """缺陷数据库管理器"""
    
    def __init__(self, db_path='defect_database.db'):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """初始化数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建设备信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment_info (
                equipment_id TEXT PRIMARY KEY,
                model TEXT,
                manufacturer TEXT,
                install_date DATE,
                location TEXT,
                specifications JSON
            )
        ''')
        
        # 创建检测记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inspection_records (
                inspection_id TEXT PRIMARY KEY,
                equipment_id TEXT,
                inspection_date DATETIME,
                method TEXT,
                operator TEXT,
                weather_condition TEXT,
                FOREIGN KEY (equipment_id) REFERENCES equipment_info(equipment_id)
            )
        ''')
        
        # 创建原始信号表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS raw_signals (
                signal_id TEXT PRIMARY KEY,
                inspection_id TEXT,
                channel_id INTEGER,
                sampling_rate REAL,
                signal_length INTEGER,
                signal_data BLOB,
                metadata JSON,
                created_date DATETIME,
                FOREIGN KEY (inspection_id) REFERENCES inspection_records(inspection_id)
            )
        ''')
        
        # 创建特征数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feature_data (
                feature_id TEXT PRIMARY KEY,
                signal_id TEXT,
                feature_vector JSON,
                feature_names JSON,
                extraction_method TEXT,
                extraction_date DATETIME,
                FOREIGN KEY (signal_id) REFERENCES raw_signals(signal_id)
            )
        ''')
        
        # 创建缺陷标注表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS defect_annotations (
                annotation_id TEXT PRIMARY KEY,
                signal_id TEXT,
                defect_type TEXT,
                severity_level INTEGER,
                confidence_score REAL,
                annotator TEXT,
                annotation_date DATETIME,
                verified BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (signal_id) REFERENCES raw_signals(signal_id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ 数据库结构初始化完成")
    
    def add_equipment(self, equipment_id, model, manufacturer, install_date, location, specifications=None):
        """添加设备信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO equipment_info 
            (equipment_id, model, manufacturer, install_date, location, specifications)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (equipment_id, model, manufacturer, install_date, location, 
              json.dumps(specifications) if specifications else None))
        
        conn.commit()
        conn.close()
        print(f"✅ 设备 {equipment_id} 信息已添加")
    
    def add_inspection_record(self, inspection_id, equipment_id, method, operator, weather_condition=None):
        """添加检测记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inspection_date = datetime.now().isoformat()
        
        cursor.execute('''
            INSERT INTO inspection_records 
            (inspection_id, equipment_id, inspection_date, method, operator, weather_condition)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (inspection_id, equipment_id, inspection_date, method, operator, weather_condition))
        
        conn.commit()
        conn.close()
        print(f"✅ 检测记录 {inspection_id} 已添加")
    
    def store_signal(self, signal_id, inspection_id, channel_id, signal_data, sampling_rate, metadata=None):
        """存储原始信号数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 压缩信号数据
        compressed_signal = zlib.compress(pickle.dumps(signal_data))
        
        cursor.execute('''
            INSERT INTO raw_signals 
            (signal_id, inspection_id, channel_id, sampling_rate, signal_length, 
             signal_data, metadata, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (signal_id, inspection_id, channel_id, sampling_rate, len(signal_data),
              compressed_signal, json.dumps(metadata) if metadata else None, 
              datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
        print(f"✅ 信号 {signal_id} 已存储")
    
    def store_features(self, feature_id, signal_id, feature_vector, feature_names, extraction_method):
        """存储特征数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO feature_data 
            (feature_id, signal_id, feature_vector, feature_names, extraction_method, extraction_date)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (feature_id, signal_id, json.dumps(feature_vector.tolist()), 
              json.dumps(feature_names), extraction_method, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
        print(f"✅ 特征 {feature_id} 已存储")
    
    def add_annotation(self, annotation_id, signal_id, defect_type, severity_level, 
                      confidence_score, annotator):
        """添加缺陷标注"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO defect_annotations 
            (annotation_id, signal_id, defect_type, severity_level, confidence_score, 
             annotator, annotation_date)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (annotation_id, signal_id, defect_type, severity_level, confidence_score,
              annotator, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
        print(f"✅ 标注 {annotation_id} 已添加")
    
    def get_dataset_for_training(self, defect_types=None, verified_only=True):
        """获取用于训练的数据集"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT f.feature_vector, f.feature_names, a.defect_type, a.severity_level
            FROM feature_data f
            JOIN defect_annotations a ON f.signal_id = a.signal_id
        '''
        
        if verified_only:
            query += ' WHERE a.verified = TRUE'
        
        if defect_types:
            defect_list = "', '".join(defect_types)
            query += f" AND a.defect_type IN ('{defect_list}')"
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        # 解析特征向量
        features = []
        labels = []
        
        for _, row in df.iterrows():
            feature_vector = json.loads(row['feature_vector'])
            features.append(feature_vector)
            labels.append(row['defect_type'])
        
        return np.array(features), np.array(labels), json.loads(df.iloc[0]['feature_names'])
    
    def get_database_statistics(self):
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_path)
        
        stats = {}
        
        # 设备数量
        cursor = conn.execute('SELECT COUNT(*) FROM equipment_info')
        stats['equipment_count'] = cursor.fetchone()[0]
        
        # 检测记录数量
        cursor = conn.execute('SELECT COUNT(*) FROM inspection_records')
        stats['inspection_count'] = cursor.fetchone()[0]
        
        # 信号数量
        cursor = conn.execute('SELECT COUNT(*) FROM raw_signals')
        stats['signal_count'] = cursor.fetchone()[0]
        
        # 特征数量
        cursor = conn.execute('SELECT COUNT(*) FROM feature_data')
        stats['feature_count'] = cursor.fetchone()[0]
        
        # 标注数量
        cursor = conn.execute('SELECT COUNT(*) FROM defect_annotations')
        stats['annotation_count'] = cursor.fetchone()[0]
        
        # 缺陷类型分布
        cursor = conn.execute('''
            SELECT defect_type, COUNT(*) as count 
            FROM defect_annotations 
            GROUP BY defect_type
        ''')
        stats['defect_distribution'] = dict(cursor.fetchall())
        
        conn.close()
        return stats


class DefectDataCollector:
    """缺陷数据采集器"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
    
    def simulate_data_collection(self, num_samples=200):
        """模拟数据采集过程"""
        print("🔄 开始模拟数据采集过程...")
        
        # 添加设备信息
        self.db_manager.add_equipment(
            equipment_id='BLADE_001',
            model='GE_1.5MW',
            manufacturer='General Electric',
            install_date='2020-01-15',
            location='风场A区1号机组',
            specifications={'length': '37m', 'material': 'GFRP'}
        )
        
        # 创建检测记录
        inspection_id = f'INSP_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        self.db_manager.add_inspection_record(
            inspection_id=inspection_id,
            equipment_id='BLADE_001',
            method='超声波检测',
            operator='张工程师',
            weather_condition='晴朗，风速3m/s'
        )
        
        # 生成并存储信号数据
        for i in range(num_samples):
            # 生成模拟信号
            defect_type_idx = i % 4
            defect_type = self.defect_types[defect_type_idx]
            
            signal = self._generate_defect_signal(defect_type)
            
            # 生成唯一ID
            signal_id = f'SIG_{inspection_id}_{i:04d}'
            
            # 存储信号
            self.db_manager.store_signal(
                signal_id=signal_id,
                inspection_id=inspection_id,
                channel_id=1,
                signal_data=signal,
                sampling_rate=1000,
                metadata={'defect_type': defect_type, 'position': f'point_{i}'}
            )
            
            # 提取并存储特征
            features, feature_names = self._extract_comprehensive_features(signal)
            feature_id = f'FEAT_{signal_id}'
            
            self.db_manager.store_features(
                feature_id=feature_id,
                signal_id=signal_id,
                feature_vector=features,
                feature_names=feature_names,
                extraction_method='comprehensive_v1.0'
            )
            
            # 添加标注
            annotation_id = f'ANN_{signal_id}'
            self.db_manager.add_annotation(
                annotation_id=annotation_id,
                signal_id=signal_id,
                defect_type=defect_type,
                severity_level=np.random.randint(1, 5),
                confidence_score=np.random.uniform(0.8, 1.0),
                annotator='专家A'
            )
            
            if (i + 1) % 50 == 0:
                print(f"   已处理 {i + 1}/{num_samples} 个样本")
        
        print(f"✅ 数据采集模拟完成，共处理 {num_samples} 个样本")
    
    def _generate_defect_signal(self, defect_type):
        """生成缺陷信号（简化版）"""
        t = np.linspace(0, 2, 2000)
        
        if defect_type == '裂纹':
            signal = 0.1 * np.sin(2 * np.pi * 50 * t)
            # 添加冲击
            for _ in range(3):
                impact_time = np.random.uniform(0.2, 1.8)
                impact_idx = int(impact_time * 1000)
                if impact_idx < len(signal) - 50:
                    signal[impact_idx:impact_idx+50] += 2 * np.exp(-np.arange(50) * 0.1) * np.sin(2 * np.pi * 300 * np.arange(50) / 1000)
        elif defect_type == '纤维褶皱':
            signal = 0.8 * np.sin(2 * np.pi * 100 * t) + 0.4 * np.sin(2 * np.pi * 180 * t)
        elif defect_type == '分层':
            signal = 1.2 * np.sin(2 * np.pi * 30 * t)
            # 添加脉冲
            for _ in range(2):
                pulse_time = np.random.uniform(0.3, 1.7)
                pulse_idx = int(pulse_time * 1000)
                if pulse_idx < len(signal) - 100:
                    signal[pulse_idx:pulse_idx+100] += 1.5 * np.exp(-np.arange(100) * 0.05) * np.sin(2 * np.pi * 80 * np.arange(100) / 1000)
        else:  # 鼓包
            signal = 1.5 * np.sin(2 * np.pi * 15 * t) + 0.8 * np.sin(2 * np.pi * 40 * t)
        
        # 添加噪声
        signal += 0.05 * np.random.randn(len(signal))
        return signal
    
    def _extract_comprehensive_features(self, signal):
        """提取综合特征"""
        features = []
        feature_names = []
        
        # 时域特征
        time_features = [
            np.mean(signal), np.std(signal), np.max(signal), np.min(signal),
            np.max(signal) - np.min(signal), np.sqrt(np.mean(signal**2)),
            np.mean(np.abs(signal))
        ]
        time_names = ['均值', '标准差', '最大值', '最小值', '峰峰值', '有效值', '平均绝对值']
        
        features.extend(time_features)
        feature_names.extend(time_names)
        
        # 频域特征
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/1000)
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        positive_freqs = freqs[:len(freqs)//2]
        
        freq_features = [
            positive_freqs[np.argmax(magnitude)],  # 主频率
            np.sum(positive_freqs * magnitude) / np.sum(magnitude),  # 频谱重心
            np.max(magnitude),  # 最大幅值
            np.sum(magnitude[positive_freqs <= 50]) / np.sum(magnitude),  # 低频能量比
            np.sum(magnitude[(positive_freqs > 50) & (positive_freqs <= 200)]) / np.sum(magnitude),  # 中频能量比
            np.sum(magnitude[positive_freqs > 200]) / np.sum(magnitude)  # 高频能量比
        ]
        freq_names = ['主频率', '频谱重心', '频域最大值', '低频能量比', '中频能量比', '高频能量比']
        
        features.extend(freq_features)
        feature_names.extend(freq_names)
        
        return np.array(features), feature_names


def demonstrate_database_construction():
    """演示数据库建立过程"""
    print("🗄️ 缺陷数据库建立演示")
    print("=" * 50)
    
    # 创建数据库管理器
    db_manager = DefectDatabaseManager('demo_defect_database.db')
    
    # 创建数据采集器
    collector = DefectDataCollector(db_manager)
    
    # 模拟数据采集过程
    collector.simulate_data_collection(num_samples=200)
    
    # 获取数据库统计信息
    stats = db_manager.get_database_statistics()
    
    print("\n📊 数据库统计信息:")
    print(f"设备数量: {stats['equipment_count']}")
    print(f"检测记录: {stats['inspection_count']}")
    print(f"信号数量: {stats['signal_count']}")
    print(f"特征数量: {stats['feature_count']}")
    print(f"标注数量: {stats['annotation_count']}")
    
    print("\n🏷️ 缺陷类型分布:")
    for defect_type, count in stats['defect_distribution'].items():
        print(f"  {defect_type}: {count} 个样本")
    
    # 获取训练数据集
    print("\n🤖 准备训练数据集...")
    X, y, feature_names = db_manager.get_dataset_for_training(verified_only=False)
    
    print(f"特征矩阵形状: {X.shape}")
    print(f"标签数量: {len(y)}")
    print(f"特征名称: {len(feature_names)} 个特征")
    
    print("\n✅ 数据库建立演示完成！")
    print(f"数据库文件: {db_manager.db_path}")
    
    return db_manager


if __name__ == "__main__":
    # 运行演示
    db_manager = demonstrate_database_construction()
    
    print("\n💡 数据库建立关键步骤总结:")
    print("1. 🏗️  数据库结构设计与初始化")
    print("2. 📡 多源信号数据采集")
    print("3. 🔧 信号预处理与质量控制")
    print("4. 🧠 多维特征提取与存储")
    print("5. 🏷️  专家标注与验证")
    print("6. 📊 数据集准备与模型训练")
    print("7. ⚡ 系统部署与持续优化")
