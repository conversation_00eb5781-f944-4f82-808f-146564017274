"""
Excel文件读写演示
使用项目中的叶片缺陷数据演示Excel文件的读取和写入
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def csv_to_excel_demo():
    """将CSV文件转换为Excel文件的演示"""
    print("=" * 60)
    print("CSV转Excel演示")
    print("=" * 60)
    
    # 读取现有的CSV文件
    csv_file = "blade_cavity_defects.csv"
    
    if not os.path.exists(csv_file):
        print(f"CSV文件不存在: {csv_file}")
        return None
    
    # 读取CSV数据
    df = pd.read_csv(csv_file)
    print(f"从CSV读取数据: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 创建Excel文件名
    excel_file = "blade_cavity_defects.xlsx"
    
    # 将数据写入Excel文件
    try:
        # 方法1: 简单写入
        df.to_excel(excel_file, index=False, sheet_name='缺陷数据')
        print(f"成功创建Excel文件: {excel_file}")
        
        # 方法2: 写入多个工作表
        excel_file_multi = "blade_cavity_defects_multi.xlsx"
        
        # 按缺陷类型分组
        defect_types = df['缺陷类型'].unique()
        
        with pd.ExcelWriter(excel_file_multi, engine='openpyxl') as writer:
            # 写入完整数据
            df.to_excel(writer, sheet_name='全部数据', index=False)
            
            # 按缺陷类型分别写入
            for defect_type in defect_types:
                subset = df[df['缺陷类型'] == defect_type]
                sheet_name = f'{defect_type}数据'
                subset.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 写入统计摘要
            summary = df.describe()
            summary.to_excel(writer, sheet_name='统计摘要')
        
        print(f"成功创建多工作表Excel文件: {excel_file_multi}")
        
        return excel_file, excel_file_multi
        
    except Exception as e:
        print(f"创建Excel文件时出错: {str(e)}")
        return None, None

def read_excel_demo(excel_file):
    """读取Excel文件的演示"""
    print("\n" + "=" * 60)
    print("Excel文件读取演示")
    print("=" * 60)
    
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        return
    
    try:
        # 方法1: 读取第一个工作表
        print("1. 读取第一个工作表:")
        df1 = pd.read_excel(excel_file)
        print(f"   数据形状: {df1.shape}")
        print(f"   前3行数据:")
        print(df1.head(3))
        
        # 方法2: 获取所有工作表信息
        print("\n2. 获取工作表信息:")
        excel_file_obj = pd.ExcelFile(excel_file)
        sheet_names = excel_file_obj.sheet_names
        print(f"   工作表名称: {sheet_names}")
        
        # 方法3: 读取指定工作表
        if len(sheet_names) > 0:
            print(f"\n3. 读取指定工作表 '{sheet_names[0]}':")
            df2 = pd.read_excel(excel_file, sheet_name=sheet_names[0])
            print(f"   数据形状: {df2.shape}")
        
        # 方法4: 读取所有工作表
        print("\n4. 读取所有工作表:")
        all_sheets = pd.read_excel(excel_file, sheet_name=None)
        for sheet_name, df in all_sheets.items():
            print(f"   工作表 '{sheet_name}': {df.shape}")
        
        # 方法5: 读取指定列
        print("\n5. 读取指定列:")
        columns_to_read = ['频率特征_Hz', '幅度特征_dB', '缺陷类型']
        df3 = pd.read_excel(excel_file, usecols=columns_to_read)
        print(f"   选择的列: {list(df3.columns)}")
        print(f"   数据形状: {df3.shape}")
        
        # 方法6: 条件读取
        print("\n6. 数据分析示例:")
        df_full = pd.read_excel(excel_file)
        
        # 统计各缺陷类型的数量
        defect_counts = df_full['缺陷类型'].value_counts()
        print("   缺陷类型统计:")
        for defect_type, count in defect_counts.items():
            print(f"     {defect_type}: {count} 个")
        
        # 数值列的基本统计
        numeric_columns = df_full.select_dtypes(include=[np.number]).columns
        print(f"\n   数值列统计 (共{len(numeric_columns)}列):")
        print(df_full[numeric_columns].describe().round(2))
        
        return df_full
        
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return None

def advanced_excel_operations(excel_file):
    """高级Excel操作演示"""
    print("\n" + "=" * 60)
    print("高级Excel操作演示")
    print("=" * 60)
    
    try:
        # 读取数据
        df = pd.read_excel(excel_file)
        
        # 1. 数据筛选
        print("1. 数据筛选示例:")
        high_freq_data = df[df['频率特征_Hz'] > 2500]
        print(f"   频率 > 2500Hz 的数据: {len(high_freq_data)} 条")
        
        # 2. 数据分组统计
        print("\n2. 按缺陷类型分组统计:")
        group_stats = df.groupby('缺陷类型').agg({
            '频率特征_Hz': ['mean', 'std'],
            '幅度特征_dB': ['mean', 'std'],
            '缺陷面积比_%': ['mean', 'max']
        }).round(2)
        print(group_stats)
        
        # 3. 创建数据透视表
        print("\n3. 创建数据透视表:")
        # 先创建频率区间
        df['频率区间'] = pd.cut(df['频率特征_Hz'], bins=3, labels=['低频', '中频', '高频'])
        
        pivot_table = pd.pivot_table(df, 
                                   values='缺陷面积比_%', 
                                   index='缺陷类型', 
                                   columns='频率区间', 
                                   aggfunc='mean').round(2)
        print(pivot_table)
        
        # 4. 保存处理后的数据
        output_file = "blade_defects_processed.xlsx"
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 原始数据
            df.to_excel(writer, sheet_name='原始数据', index=False)
            
            # 筛选后的数据
            high_freq_data.to_excel(writer, sheet_name='高频数据', index=False)
            
            # 分组统计
            group_stats.to_excel(writer, sheet_name='分组统计')
            
            # 数据透视表
            pivot_table.to_excel(writer, sheet_name='数据透视表')
        
        print(f"\n处理后的数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"高级操作时出错: {str(e)}")

def main():
    """主函数"""
    print("Excel文件读写完整演示")
    print("=" * 60)
    
    # 1. 将CSV转换为Excel
    excel_file, excel_file_multi = csv_to_excel_demo()
    
    if excel_file:
        # 2. 读取Excel文件
        df = read_excel_demo(excel_file)
        
        if df is not None:
            # 3. 高级Excel操作
            advanced_excel_operations(excel_file)
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("生成的文件:")
    files_created = [
        "blade_cavity_defects.xlsx",
        "blade_cavity_defects_multi.xlsx", 
        "blade_defects_processed.xlsx"
    ]
    
    for file in files_created:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (未创建)")

if __name__ == "__main__":
    main()
