import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, Ellipse
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TDOALocalizationAnimation:
    def __init__(self):
        # 风机叶片参数
        self.blade_length = 10  # 叶片长度
        self.blade_width = 2    # 叶片宽度
        
        # 声发射传感器位置（三角定位）
        self.sensors = np.array([
            [1, 0.5],   # 传感器1
            [9, 0.5],   # 传感器2
            [5, 1.5]    # 传感器3
        ])
        
        # 声速（m/s）
        self.sound_speed = 5000  # 金属中的声速
        
        # 缺陷位置（动态变化）
        self.defect_positions = [
            [3, 1],     # 位置1
            [7, 0.8],   # 位置2
            [5, 0.3],   # 位置3
            [2, 1.2],   # 位置4
            [8, 1.3],   # 位置5
        ]
        
        self.current_defect_idx = 0
        self.frame_count = 0
        
        # 颜色设置
        self.sensor_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        self.wave_colors = ['#FF9999', '#7FDDDD', '#7FC7E8']
        
    def calculate_distances(self, defect_pos):
        """计算缺陷到各传感器的距离"""
        distances = []
        for sensor in self.sensors:
            dist = np.sqrt(np.sum((defect_pos - sensor)**2))
            distances.append(dist)
        return np.array(distances)
    
    def calculate_tdoa(self, defect_pos):
        """计算时间差（TDOA）"""
        distances = self.calculate_distances(defect_pos)
        times = distances / self.sound_speed
        
        # 以传感器1为参考
        tdoa_12 = times[1] - times[0]  # 传感器2相对于传感器1的时间差
        tdoa_13 = times[2] - times[0]  # 传感器3相对于传感器1的时间差
        
        return tdoa_12, tdoa_13, times
    
    def tdoa_localization(self, tdoa_12, tdoa_13):
        """基于TDOA的定位算法"""
        # 简化的双曲线定位算法
        x1, y1 = self.sensors[0]
        x2, y2 = self.sensors[1]
        x3, y3 = self.sensors[2]
        
        # 距离差
        r12 = tdoa_12 * self.sound_speed
        r13 = tdoa_13 * self.sound_speed
        
        # 双曲线方程求解（简化版）
        # 这里使用几何方法近似求解
        
        # 计算可能的位置
        estimated_positions = []
        
        # 网格搜索法找到最优位置
        x_range = np.linspace(0, self.blade_length, 50)
        y_range = np.linspace(0, self.blade_width, 20)
        
        min_error = float('inf')
        best_pos = None
        
        for x in x_range:
            for y in y_range:
                pos = np.array([x, y])
                distances = self.calculate_distances(pos)
                
                # 计算预测的TDOA
                pred_tdoa_12 = (distances[1] - distances[0]) / self.sound_speed
                pred_tdoa_13 = (distances[2] - distances[0]) / self.sound_speed
                
                # 计算误差
                error = (pred_tdoa_12 - tdoa_12)**2 + (pred_tdoa_13 - tdoa_13)**2
                
                if error < min_error:
                    min_error = error
                    best_pos = pos
        
        return best_pos
    
    def create_animation(self):
        """创建TDOA定位动画"""
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 创建动画
        anim = animation.FuncAnimation(
            self.fig, self.animate, frames=300, interval=100, blit=False, repeat=True
        )
        
        # 保存动画
        print("正在生成TDOA定位动画...")
        anim.save('tdoa_localization.gif', writer='pillow', fps=5, dpi=100)
        print("TDOA定位动画已保存为 tdoa_localization.gif")
        
        return anim
    
    def animate(self, frame):
        """动画函数"""
        # 清除图表
        self.ax1.clear()
        self.ax2.clear()
        
        # 设置图表
        self.setup_plots()
        
        # 更新缺陷位置
        if frame % 60 == 0 and frame > 0:
            self.current_defect_idx = (self.current_defect_idx + 1) % len(self.defect_positions)
        
        current_defect = self.defect_positions[self.current_defect_idx]
        
        # 计算TDOA
        tdoa_12, tdoa_13, arrival_times = self.calculate_tdoa(current_defect)
        
        # TDOA定位
        estimated_pos = self.tdoa_localization(tdoa_12, tdoa_13)
        
        # 绘制叶片轮廓
        self.draw_blade_outline()
        
        # 绘制传感器
        self.draw_sensors()
        
        # 绘制真实缺陷位置
        self.ax1.scatter(current_defect[0], current_defect[1], 
                        c='red', s=200, marker='*', 
                        label='真实缺陷位置', edgecolors='black', linewidth=2, zorder=10)
        
        # 绘制估计缺陷位置
        if estimated_pos is not None:
            self.ax1.scatter(estimated_pos[0], estimated_pos[1], 
                            c='orange', s=150, marker='x', 
                            label='估计缺陷位置', linewidth=3, zorder=9)
            
            # 计算定位误差
            error = np.sqrt(np.sum((estimated_pos - current_defect)**2))
            self.ax1.text(0.02, 0.02, f'定位误差: {error:.3f}m', 
                         transform=self.ax1.transAxes, fontsize=10,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8))
        
        # 绘制声波传播（动态效果）
        wave_phase = (frame % 30) / 30.0
        self.draw_acoustic_waves(current_defect, arrival_times, wave_phase)
        
        # 绘制双曲线（TDOA等值线）
        self.draw_tdoa_hyperbolas(tdoa_12, tdoa_13)
        
        # 右图：TDOA分析
        self.draw_tdoa_analysis(tdoa_12, tdoa_13, arrival_times, frame)
        
        # 添加图例和标题
        self.ax1.legend(loc='upper right')
        self.ax1.set_title(f'TDOA定位原理演示 - 缺陷{self.current_defect_idx+1}', 
                          fontsize=14, fontweight='bold')
        
    def setup_plots(self):
        """设置图表"""
        # 左图：定位可视化
        self.ax1.set_xlim(-0.5, 10.5)
        self.ax1.set_ylim(-0.5, 2.5)
        self.ax1.set_xlabel('距离 (m)', fontsize=12)
        self.ax1.set_ylabel('距离 (m)', fontsize=12)
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_aspect('equal')
        
        # 右图：TDOA分析
        self.ax2.set_xlim(0, 10)
        self.ax2.set_ylim(0, 10)
        self.ax2.set_xlabel('时间 (ms)', fontsize=12)
        self.ax2.set_ylabel('信号幅度', fontsize=12)
        self.ax2.set_title('声发射信号到达时间分析', fontsize=12, fontweight='bold')
        self.ax2.grid(True, alpha=0.3)
    
    def draw_blade_outline(self):
        """绘制叶片轮廓"""
        # 叶片外轮廓
        blade_x = [0, self.blade_length, self.blade_length, 0, 0]
        blade_y = [0, 0, self.blade_width, self.blade_width, 0]
        self.ax1.plot(blade_x, blade_y, 'k-', linewidth=3, alpha=0.8)
        
        # 填充叶片区域
        self.ax1.fill(blade_x, blade_y, color='lightgray', alpha=0.3)
        
        # 添加叶片标注
        self.ax1.text(5, 1, '风机叶片内腔', ha='center', va='center', 
                     fontsize=14, fontweight='bold', 
                     bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    def draw_sensors(self):
        """绘制传感器"""
        for i, (sensor, color) in enumerate(zip(self.sensors, self.sensor_colors)):
            self.ax1.scatter(sensor[0], sensor[1], c=color, s=150, 
                           marker='s', label=f'传感器{i+1}', 
                           edgecolors='black', linewidth=2, zorder=8)
            
            # 添加传感器编号
            self.ax1.text(sensor[0], sensor[1]+0.15, f'S{i+1}', 
                         ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    def draw_acoustic_waves(self, defect_pos, arrival_times, phase):
        """绘制声波传播动画"""
        for i, (sensor, color, arrival_time) in enumerate(zip(self.sensors, self.wave_colors, arrival_times)):
            # 计算当前波前位置
            max_radius = np.sqrt(np.sum((defect_pos - sensor)**2))
            current_radius = max_radius * phase
            
            if current_radius > 0:
                # 绘制波前圆圈
                circle = Circle(defect_pos, current_radius, 
                              fill=False, color=color, linewidth=2, alpha=0.6)
                self.ax1.add_patch(circle)
                
                # 如果波前到达传感器，高亮显示
                if current_radius >= max_radius * 0.95:
                    highlight_circle = Circle(sensor, 0.1, 
                                            fill=True, color=color, alpha=0.8)
                    self.ax1.add_patch(highlight_circle)
    
    def draw_tdoa_hyperbolas(self, tdoa_12, tdoa_13):
        """绘制TDOA双曲线"""
        # 绘制双曲线等值线（简化版）
        x_range = np.linspace(0, self.blade_length, 100)
        y_range = np.linspace(0, self.blade_width, 50)
        X, Y = np.meshgrid(x_range, y_range)
        
        # 计算每个点的TDOA值
        Z12 = np.zeros_like(X)
        Z13 = np.zeros_like(X)
        
        for i in range(X.shape[0]):
            for j in range(X.shape[1]):
                pos = np.array([X[i,j], Y[i,j]])
                distances = self.calculate_distances(pos)
                Z12[i,j] = (distances[1] - distances[0]) / self.sound_speed
                Z13[i,j] = (distances[2] - distances[0]) / self.sound_speed
        
        # 绘制等值线
        contour1 = self.ax1.contour(X, Y, Z12, levels=[tdoa_12], 
                                   colors=['red'], linestyles=['--'], alpha=0.7)
        contour2 = self.ax1.contour(X, Y, Z13, levels=[tdoa_13], 
                                   colors=['blue'], linestyles=['--'], alpha=0.7)
        
        # 添加等值线标签
        self.ax1.clabel(contour1, inline=True, fontsize=8, fmt='TDOA12=%.3f')
        self.ax1.clabel(contour2, inline=True, fontsize=8, fmt='TDOA13=%.3f')
    
    def draw_tdoa_analysis(self, tdoa_12, tdoa_13, arrival_times, frame):
        """绘制TDOA分析图"""
        # 清除右图
        self.ax2.clear()
        self.ax2.set_xlim(0, 5)
        self.ax2.set_ylim(-1.5, 3.5)
        self.ax2.set_xlabel('时间 (ms)', fontsize=12)
        self.ax2.set_ylabel('传感器编号', fontsize=12)
        self.ax2.set_title('声发射信号到达时间分析', fontsize=12, fontweight='bold')
        self.ax2.grid(True, alpha=0.3)
        
        # 转换到毫秒
        arrival_times_ms = arrival_times * 1000
        
        # 绘制到达时间
        for i, (time_ms, color) in enumerate(zip(arrival_times_ms, self.sensor_colors)):
            # 绘制信号脉冲
            pulse_width = 0.2
            pulse_height = 0.3
            
            # 脉冲信号
            t_pulse = np.linspace(time_ms, time_ms + pulse_width, 20)
            signal = pulse_height * np.sin(2 * np.pi * 10 * (t_pulse - time_ms))
            
            self.ax2.plot(t_pulse, signal + i, color=color, linewidth=2)
            self.ax2.fill_between(t_pulse, i, signal + i, color=color, alpha=0.3)
            
            # 标记到达时间
            self.ax2.axvline(x=time_ms, ymin=(i-0.4)/3, ymax=(i+0.4)/3, 
                           color=color, linewidth=3, alpha=0.8)
            
            # 添加时间标签
            self.ax2.text(time_ms, i+0.5, f'{time_ms:.2f}ms', 
                         ha='center', va='bottom', fontsize=10, fontweight='bold')
        
        # 设置y轴标签
        self.ax2.set_yticks([0, 1, 2])
        self.ax2.set_yticklabels(['传感器1', '传感器2', '传感器3'])
        
        # 显示TDOA值
        tdoa_text = f'TDOA₁₂ = {tdoa_12*1000:.2f}ms\nTDOA₁₃ = {tdoa_13*1000:.2f}ms'
        self.ax2.text(0.98, 0.98, tdoa_text, transform=self.ax2.transAxes, 
                     fontsize=12, fontweight='bold', ha='right', va='top',
                     bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
        
        # 绘制时间差箭头
        if len(arrival_times_ms) >= 2:
            # TDOA12箭头
            self.ax2.annotate('', xy=(arrival_times_ms[1], 0.5), 
                            xytext=(arrival_times_ms[0], 0.5),
                            arrowprops=dict(arrowstyle='<->', color='red', lw=2))
            self.ax2.text((arrival_times_ms[0] + arrival_times_ms[1])/2, 0.7, 
                         f'Δt₁₂', ha='center', va='bottom', color='red', fontweight='bold')
            
            # TDOA13箭头
            self.ax2.annotate('', xy=(arrival_times_ms[2], 1.5), 
                            xytext=(arrival_times_ms[0], 1.5),
                            arrowprops=dict(arrowstyle='<->', color='blue', lw=2))
            self.ax2.text((arrival_times_ms[0] + arrival_times_ms[2])/2, 1.7, 
                         f'Δt₁₃', ha='center', va='bottom', color='blue', fontweight='bold')

def create_tdoa_static_analysis():
    """创建TDOA静态分析图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 创建TDOA对象
    tdoa = TDOALocalizationAnimation()
    
    # 分析不同位置的缺陷
    for idx, defect_pos in enumerate(tdoa.defect_positions[:4]):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]
        
        # 设置坐标轴
        ax.set_xlim(-0.5, 10.5)
        ax.set_ylim(-0.5, 2.5)
        ax.set_xlabel('距离 (m)')
        ax.set_ylabel('距离 (m)')
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        # 绘制叶片轮廓
        blade_x = [0, tdoa.blade_length, tdoa.blade_length, 0, 0]
        blade_y = [0, 0, tdoa.blade_width, tdoa.blade_width, 0]
        ax.plot(blade_x, blade_y, 'k-', linewidth=2)
        ax.fill(blade_x, blade_y, color='lightgray', alpha=0.2)
        
        # 绘制传感器
        for i, (sensor, color) in enumerate(zip(tdoa.sensors, tdoa.sensor_colors)):
            ax.scatter(sensor[0], sensor[1], c=color, s=100, 
                      marker='s', edgecolors='black', linewidth=1)
            ax.text(sensor[0], sensor[1]+0.1, f'S{i+1}', 
                   ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        # 绘制缺陷位置
        ax.scatter(defect_pos[0], defect_pos[1], c='red', s=150, 
                  marker='*', edgecolors='black', linewidth=2)
        
        # 计算并显示TDOA
        tdoa_12, tdoa_13, arrival_times = tdoa.calculate_tdoa(defect_pos)
        estimated_pos = tdoa.tdoa_localization(tdoa_12, tdoa_13)
        
        if estimated_pos is not None:
            ax.scatter(estimated_pos[0], estimated_pos[1], c='orange', 
                      s=100, marker='x', linewidth=3)
            
            error = np.sqrt(np.sum((estimated_pos - defect_pos)**2))
            ax.text(0.02, 0.98, f'定位误差: {error:.3f}m', 
                   transform=ax.transAxes, fontsize=10,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8),
                   verticalalignment='top')
        
        # 绘制距离线
        for i, sensor in enumerate(tdoa.sensors):
            ax.plot([defect_pos[0], sensor[0]], [defect_pos[1], sensor[1]], 
                   color=tdoa.sensor_colors[i], linestyle='--', alpha=0.6)
            
            # 标注距离
            mid_x = (defect_pos[0] + sensor[0]) / 2
            mid_y = (defect_pos[1] + sensor[1]) / 2
            distance = np.sqrt(np.sum((defect_pos - sensor)**2))
            ax.text(mid_x, mid_y, f'{distance:.2f}m', 
                   fontsize=8, ha='center', va='center',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
        
        ax.set_title(f'缺陷位置{idx+1}: ({defect_pos[0]:.1f}, {defect_pos[1]:.1f})', 
                    fontweight='bold')
        
        # 添加TDOA信息
        info_text = f'TDOA₁₂: {tdoa_12*1000:.2f}ms\nTDOA₁₃: {tdoa_13*1000:.2f}ms'
        ax.text(0.98, 0.98, info_text, transform=ax.transAxes, 
               fontsize=9, ha='right', va='top',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('tdoa_static_analysis.png', dpi=300, bbox_inches='tight')
    print("TDOA静态分析图已保存为 tdoa_static_analysis.png")

def create_tdoa_showcase_html():
    """创建TDOA展示页面"""
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDOA定位法动态演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .animation-container {
            text-align: center;
            margin: 30px 0;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .animation-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        .card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .formula {
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            text-align: center;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            position: relative;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: -12px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .step h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-left: 20px;
        }
        .step p {
            margin-left: 20px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📡 TDOA定位法动态演示</h1>
        
        <div class="highlight">
            <h3>🎯 技术原理</h3>
            <p><strong>时间差定位法（TDOA）</strong> - 通过测量声发射信号到达不同传感器的时间差，利用双曲线交点确定缺陷位置</p>
        </div>

        <div class="animation-container">
            <h2>🎬 TDOA定位动态演示</h2>
            <img src="tdoa_localization.gif" alt="TDOA定位动画">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                左图：风机叶片内腔三角定位示意图 | 右图：声发射信号到达时间分析
            </p>
        </div>

        <div class="animation-container">
            <h2>📊 多位置TDOA分析对比</h2>
            <img src="tdoa_static_analysis.png" alt="TDOA静态分析">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                不同缺陷位置的TDOA定位效果对比分析
            </p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📐 数学原理</h3>
                <p><strong>TDOA计算公式：</strong></p>
                <div class="formula">
                    TDOA₁₂ = (d₂ - d₁) / v<br>
                    TDOA₁₃ = (d₃ - d₁) / v
                </div>
                <p>其中：d为距离，v为声速（5000m/s）</p>
            </div>
            
            <div class="card">
                <h3>🔧 传感器配置</h3>
                <ul>
                    <li><strong>传感器1</strong>：位置(1, 0.5)，参考传感器</li>
                    <li><strong>传感器2</strong>：位置(9, 0.5)，叶片另一端</li>
                    <li><strong>传感器3</strong>：位置(5, 1.5)，叶片中部上方</li>
                    <li><strong>配置优势</strong>：三角形布局，最优定位精度</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎯 定位精度</h3>
                <ul>
                    <li><strong>理论精度</strong>：±0.1m（理想条件下）</li>
                    <li><strong>实际精度</strong>：±0.2-0.5m（考虑噪声）</li>
                    <li><strong>影响因素</strong>：声速变化、信号噪声、传感器精度</li>
                    <li><strong>优化方法</strong>：多传感器融合、滤波算法</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>⚡ 技术优势</h3>
                <ul>
                    <li><strong>实时性</strong>：毫秒级定位响应</li>
                    <li><strong>非接触</strong>：无需直接接触缺陷</li>
                    <li><strong>高精度</strong>：米级精度定位</li>
                    <li><strong>适应性</strong>：适用于各种材料和结构</li>
                </ul>
            </div>
        </div>

        <div class="steps">
            <h2>🔄 TDOA定位算法流程</h2>
            <div class="step">
                <h4>信号采集</h4>
                <p>三个声发射传感器同步采集缺陷产生的声发射信号</p>
            </div>
            <div class="step">
                <h4>到达时间检测</h4>
                <p>通过信号处理算法精确检测信号到达各传感器的时间</p>
            </div>
            <div class="step">
                <h4>时间差计算</h4>
                <p>以传感器1为参考，计算TDOA₁₂和TDOA₁₃</p>
            </div>
            <div class="step">
                <h4>双曲线方程</h4>
                <p>根据时间差构建双曲线方程组</p>
            </div>
            <div class="step">
                <h4>交点求解</h4>
                <p>求解双曲线交点，确定缺陷位置坐标</p>
            </div>
            <div class="step">
                <h4>结果输出</h4>
                <p>输出缺陷位置坐标和定位精度评估</p>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎨 动画特色</h3>
                <ul>
                    <li><strong>声波传播</strong>：动态显示声波从缺陷向传感器传播</li>
                    <li><strong>时间差可视化</strong>：实时显示信号到达时间差</li>
                    <li><strong>双曲线显示</strong>：展示TDOA等值线</li>
                    <li><strong>定位误差</strong>：实时计算和显示定位精度</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🔍 应用场景</h3>
                <ul>
                    <li><strong>风机叶片</strong>：内腔缺陷检测定位</li>
                    <li><strong>压力容器</strong>：裂纹扩展监测</li>
                    <li><strong>桥梁结构</strong>：疲劳损伤定位</li>
                    <li><strong>航空航天</strong>：复合材料缺陷检测</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>📈 技术指标</h3>
                <ul>
                    <li><strong>定位范围</strong>：10m × 2m叶片区域</li>
                    <li><strong>响应时间</strong>：<10ms</li>
                    <li><strong>定位精度</strong>：±0.2m</li>
                    <li><strong>检测频率</strong>：20kHz-1MHz</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <h3>🚀 技术价值</h3>
            <p>TDOA定位技术为风机叶片内腔缺陷检测提供了精确的空间定位能力，结合声发射监测技术，可以实现缺陷的早期发现和精确定位，为设备维护和安全评估提供重要技术支撑。</p>
        </div>
    </div>
</body>
</html>"""
    
    with open('tdoa_showcase.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("TDOA展示页面已保存为 tdoa_showcase.html")

if __name__ == "__main__":
    # 创建TDOA动画对象
    tdoa_anim = TDOALocalizationAnimation()
    
    # 生成动画
    anim = tdoa_anim.create_animation()
    
    # 生成静态分析图
    create_tdoa_static_analysis()
    
    # 创建展示页面
    create_tdoa_showcase_html()
    
    print("TDOA定位动画生成完成！")
