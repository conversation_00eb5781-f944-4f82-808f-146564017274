import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BladeCavityDefectDataset:
    """叶片内腔缺陷数据集生成器"""
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        np.random.seed(random_state)
        
        # 缺陷类型定义
        self.defect_types = {
            0: '纤维褶皱',
            1: '裂纹', 
            2: '分层',
            3: '鼓包'
        }
        
        # 缺陷颜色映射
        self.colors = {
            0: '#FF6B6B',  # 红色 - 纤维褶皱
            1: '#4ECDC4',  # 青色 - 裂纹
            2: '#45B7D1',  # 蓝色 - 分层
            3: '#96CEB4'   # 绿色 - 鼓包
        }
        
    def generate_defect_features(self, n_samples=1000):
        """
        生成叶片内腔缺陷特征数据
        
        特征包括：
        1. 频率特征 (Hz) - 超声检测频率响应
        2. 幅度特征 (dB) - 信号幅度
        3. 相位特征 (度) - 相位偏移
        4. 厚度变化 (mm) - 壁厚变化
        5. 表面粗糙度 (μm) - 表面质量
        6. 温度分布 (°C) - 热成像温度差异
        7. 应力集中系数 - 应力分布
        8. 缺陷面积比 (%) - 缺陷占总面积比例
        """
        
        samples_per_class = n_samples // 4
        features = []
        labels = []
        
        # 1. 纤维褶皱 (Fiber Wrinkle)
        # 特征：低频响应，中等幅度，小相位偏移，轻微厚度变化
        fiber_wrinkle = self._generate_fiber_wrinkle_features(samples_per_class)
        features.append(fiber_wrinkle)
        labels.extend([0] * samples_per_class)
        
        # 2. 裂纹 (Crack)
        # 特征：高频响应，高幅度，大相位偏移，显著厚度变化
        crack = self._generate_crack_features(samples_per_class)
        features.append(crack)
        labels.extend([1] * samples_per_class)
        
        # 3. 分层 (Delamination)
        # 特征：中频响应，低幅度，中等相位偏移，厚度突变
        delamination = self._generate_delamination_features(samples_per_class)
        features.append(delamination)
        labels.extend([2] * samples_per_class)
        
        # 4. 鼓包 (Bulge)
        # 特征：低频响应，高幅度，小相位偏移，大厚度变化
        bulge = self._generate_bulge_features(samples_per_class)
        features.append(bulge)
        labels.extend([3] * samples_per_class)
        
        # 合并所有特征
        X = np.vstack(features)
        y = np.array(labels)
        
        # 创建特征名称
        feature_names = [
            '频率特征_Hz', '幅度特征_dB', '相位特征_度', '厚度变化_mm',
            '表面粗糙度_μm', '温度分布_°C', '应力集中系数', '缺陷面积比_%'
        ]
        
        return X, y, feature_names
    
    def _generate_fiber_wrinkle_features(self, n_samples):
        """生成纤维褶皱特征"""
        # 纤维褶皱：纤维排列不规则，造成轻微的结构异常
        features = np.random.multivariate_normal(
            mean=[
                2500,   # 频率特征 (Hz) - 低频
                -15,    # 幅度特征 (dB) - 中等幅度
                15,     # 相位特征 (度) - 小相位偏移
                0.05,   # 厚度变化 (mm) - 轻微变化
                3.2,    # 表面粗糙度 (μm) - 中等粗糙度
                2.5,    # 温度分布 (°C) - 小温差
                1.8,    # 应力集中系数 - 轻微应力集中
                2.1     # 缺陷面积比 (%) - 小面积
            ],
            cov=np.diag([200**2, 3**2, 5**2, 0.01**2, 0.5**2, 0.8**2, 0.3**2, 0.5**2]),
            size=n_samples
        )
        return features
    
    def _generate_crack_features(self, n_samples):
        """生成裂纹特征"""
        # 裂纹：材料断裂，产生强烈的高频响应
        features = np.random.multivariate_normal(
            mean=[
                8500,   # 频率特征 (Hz) - 高频
                -5,     # 幅度特征 (dB) - 高幅度
                45,     # 相位特征 (度) - 大相位偏移
                0.15,   # 厚度变化 (mm) - 显著变化
                5.8,    # 表面粗糙度 (μm) - 高粗糙度
                8.2,    # 温度分布 (°C) - 大温差
                3.5,    # 应力集中系数 - 高应力集中
                1.2     # 缺陷面积比 (%) - 线性缺陷，面积小
            ],
            cov=np.diag([500**2, 2**2, 8**2, 0.03**2, 0.8**2, 1.5**2, 0.5**2, 0.3**2]),
            size=n_samples
        )
        return features
    
    def _generate_delamination_features(self, n_samples):
        """生成分层特征"""
        # 分层：层间结合失效，中频响应，厚度突变
        features = np.random.multivariate_normal(
            mean=[
                5200,   # 频率特征 (Hz) - 中频
                -25,    # 幅度特征 (dB) - 低幅度
                28,     # 相位特征 (度) - 中等相位偏移
                0.08,   # 厚度变化 (mm) - 厚度突变
                2.1,    # 表面粗糙度 (μm) - 低粗糙度
                4.8,    # 温度分布 (°C) - 中等温差
                2.2,    # 应力集中系数 - 中等应力集中
                5.5     # 缺陷面积比 (%) - 大面积
            ],
            cov=np.diag([300**2, 4**2, 6**2, 0.02**2, 0.4**2, 1.0**2, 0.4**2, 1.2**2]),
            size=n_samples
        )
        return features
    
    def _generate_bulge_features(self, n_samples):
        """生成鼓包特征"""
        # 鼓包：材料变形，低频响应，大厚度变化
        features = np.random.multivariate_normal(
            mean=[
                1800,   # 频率特征 (Hz) - 低频
                -8,     # 幅度特征 (dB) - 高幅度
                12,     # 相位特征 (度) - 小相位偏移
                0.25,   # 厚度变化 (mm) - 大厚度变化
                4.5,    # 表面粗糙度 (μm) - 中高粗糙度
                6.5,    # 温度分布 (°C) - 中大温差
                2.8,    # 应力集中系数 - 中高应力集中
                8.2     # 缺陷面积比 (%) - 大面积
            ],
            cov=np.diag([150**2, 2.5**2, 4**2, 0.05**2, 0.6**2, 1.2**2, 0.4**2, 1.5**2]),
            size=n_samples
        )
        return features
    
    def create_dataset(self, n_samples=1000, save_csv=True, filename='blade_cavity_defects.csv'):
        """创建完整的数据集"""
        print(f"正在生成叶片内腔缺陷数据集 (样本数: {n_samples:,})...")

        # 对于大规模数据集，分批生成以节省内存
        if n_samples > 10000:
            print("检测到大规模数据集，将分批生成以优化内存使用...")
            batch_size = 5000
            all_features = []
            all_labels = []

            for i in range(0, n_samples, batch_size):
                current_batch_size = min(batch_size, n_samples - i)
                print(f"正在生成第 {i//batch_size + 1} 批数据 ({current_batch_size:,} 个样本)...")

                X_batch, y_batch, feature_names = self.generate_defect_features(current_batch_size)
                all_features.append(X_batch)
                all_labels.extend(y_batch)

            # 合并所有批次
            X = np.vstack(all_features)
            y = np.array(all_labels)
            print("所有批次数据合并完成")
        else:
            # 小规模数据集直接生成
            X, y, feature_names = self.generate_defect_features(n_samples)

        print("正在创建DataFrame...")
        # 创建DataFrame
        df = pd.DataFrame(X, columns=feature_names)
        df['缺陷类型'] = [self.defect_types[label] for label in y]
        df['缺陷编码'] = y

        print("正在计算衍生特征...")
        # 添加一些衍生特征
        df['频率_幅度比'] = df['频率特征_Hz'] / (abs(df['幅度特征_dB']) + 1)
        df['厚度_粗糙度比'] = df['厚度变化_mm'] / (df['表面粗糙度_μm'] + 0.1)
        df['综合缺陷指数'] = (df['应力集中系数'] * df['缺陷面积比_%'] *
                          abs(df['幅度特征_dB']) / 100)

        if save_csv:
            print(f"正在保存数据集到文件: {filename}")
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            file_size = len(df) * len(df.columns) * 8 / (1024 * 1024)  # 估算文件大小(MB)
            print(f"数据集已保存为: {filename} (约 {file_size:.1f} MB)")

        return df, X, y, feature_names
    
    def visualize_dataset(self, df, X, y, feature_names):
        """可视化数据集"""
        print("正在生成数据集可视化图表...")
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 特征分布箱线图
        ax1 = plt.subplot(3, 3, 1)
        df_melted = df.melt(id_vars=['缺陷类型'], 
                           value_vars=feature_names[:4],
                           var_name='特征', value_name='数值')
        sns.boxplot(data=df_melted, x='特征', y='数值', hue='缺陷类型', ax=ax1)
        ax1.set_title('主要特征分布对比 (1-4)')
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 特征分布箱线图 (5-8)
        ax2 = plt.subplot(3, 3, 2)
        df_melted2 = df.melt(id_vars=['缺陷类型'], 
                            value_vars=feature_names[4:8],
                            var_name='特征', value_name='数值')
        sns.boxplot(data=df_melted2, x='特征', y='数值', hue='缺陷类型', ax=ax2)
        ax2.set_title('主要特征分布对比 (5-8)')
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. 频率-幅度散点图
        ax3 = plt.subplot(3, 3, 3)
        for defect_id, defect_name in self.defect_types.items():
            mask = y == defect_id
            ax3.scatter(X[mask, 0], X[mask, 1], 
                       c=self.colors[defect_id], label=defect_name, alpha=0.6, s=30)
        ax3.set_xlabel('频率特征 (Hz)')
        ax3.set_ylabel('幅度特征 (dB)')
        ax3.set_title('频率-幅度特征分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 厚度-粗糙度散点图
        ax4 = plt.subplot(3, 3, 4)
        for defect_id, defect_name in self.defect_types.items():
            mask = y == defect_id
            ax4.scatter(X[mask, 3], X[mask, 4], 
                       c=self.colors[defect_id], label=defect_name, alpha=0.6, s=30)
        ax4.set_xlabel('厚度变化 (mm)')
        ax4.set_ylabel('表面粗糙度 (μm)')
        ax4.set_title('厚度-粗糙度特征分布')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 5. 缺陷类型分布饼图
        ax5 = plt.subplot(3, 3, 5)
        defect_counts = df['缺陷类型'].value_counts()
        colors_list = [self.colors[i] for i in range(4)]
        ax5.pie(defect_counts.values, labels=defect_counts.index, 
                colors=colors_list, autopct='%1.1f%%', startangle=90)
        ax5.set_title('缺陷类型分布')
        
        # 6. 特征相关性热力图
        ax6 = plt.subplot(3, 3, 6)
        corr_matrix = df[feature_names].corr()
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                   fmt='.2f', ax=ax6, cbar_kws={'shrink': 0.8})
        ax6.set_title('特征相关性矩阵')
        
        # 7. 各缺陷类型的雷达图数据准备
        ax7 = plt.subplot(3, 3, 7)
        defect_means = df.groupby('缺陷类型')[feature_names[:6]].mean()
        defect_means_norm = (defect_means - defect_means.min()) / (defect_means.max() - defect_means.min())
        
        x_pos = np.arange(len(feature_names[:6]))
        width = 0.2
        for i, (defect_name, color) in enumerate(zip(self.defect_types.values(), self.colors.values())):
            ax7.bar(x_pos + i*width, defect_means_norm.loc[defect_name], 
                   width, label=defect_name, color=color, alpha=0.7)
        
        ax7.set_xlabel('特征')
        ax7.set_ylabel('标准化均值')
        ax7.set_title('各缺陷类型特征对比')
        ax7.set_xticks(x_pos + width * 1.5)
        ax7.set_xticklabels([name.split('_')[0] for name in feature_names[:6]], rotation=45)
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        
        # 8. 衍生特征散点图
        ax8 = plt.subplot(3, 3, 8)
        for defect_id, defect_name in self.defect_types.items():
            mask = df['缺陷编码'] == defect_id
            ax8.scatter(df.loc[mask, '频率_幅度比'], df.loc[mask, '厚度_粗糙度比'], 
                       c=self.colors[defect_id], label=defect_name, alpha=0.6, s=30)
        ax8.set_xlabel('频率_幅度比')
        ax8.set_ylabel('厚度_粗糙度比')
        ax8.set_title('衍生特征分布')
        ax8.legend()
        ax8.grid(True, alpha=0.3)
        
        # 9. 综合缺陷指数分布
        ax9 = plt.subplot(3, 3, 9)
        for defect_name in self.defect_types.values():
            data = df[df['缺陷类型'] == defect_name]['综合缺陷指数']
            ax9.hist(data, alpha=0.6, label=defect_name, bins=20)
        ax9.set_xlabel('综合缺陷指数')
        ax9.set_ylabel('频次')
        ax9.set_title('综合缺陷指数分布')
        ax9.legend()
        ax9.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('blade_cavity_defect_dataset_visualization.png', dpi=300, bbox_inches='tight')
        print("数据集可视化图表已保存为: blade_cavity_defect_dataset_visualization.png")

        return fig

    def train_classifier(self, X, y, feature_names):
        """训练分类器并评估性能"""
        print(f"正在训练随机森林分类器 (样本数: {len(X):,})...")

        # 数据标准化
        print("正在进行数据标准化...")
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 划分训练集和测试集
        print("正在划分训练集和测试集...")
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=self.random_state, stratify=y
        )

        print(f"训练集大小: {len(X_train):,}, 测试集大小: {len(X_test):,}")

        # 对于大规模数据集，调整随机森林参数
        if len(X) > 10000:
            n_estimators = 200  # 增加树的数量
            max_depth = 15      # 增加树的深度
            min_samples_split = 10
            min_samples_leaf = 5
            print("使用大规模数据集优化参数...")
        else:
            n_estimators = 100
            max_depth = 10
            min_samples_split = 5
            min_samples_leaf = 2

        # 训练随机森林分类器
        rf_classifier = RandomForestClassifier(
            n_estimators=n_estimators,
            random_state=self.random_state,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf,
            n_jobs=-1  # 使用所有CPU核心
        )

        print("正在训练模型...")
        rf_classifier.fit(X_train, y_train)

        # 预测
        print("正在进行预测...")
        y_pred = rf_classifier.predict(X_test)

        # 特征重要性
        feature_importance = rf_classifier.feature_importances_

        # 生成分类报告
        print("\n" + "="*60)
        print("叶片内腔缺陷分类器性能报告")
        print("="*60)
        print(classification_report(y_test, y_pred,
                                  target_names=list(self.defect_types.values())))

        # 可视化结果 (对于大数据集，使用子集进行可视化)
        if len(X_test) > 2000:
            print("使用测试集子集进行可视化 (2000个样本)...")
            viz_indices = np.random.choice(len(X_test), 2000, replace=False)
            X_test_viz = X_test[viz_indices]
            y_test_viz = y_test[viz_indices]
            y_pred_viz = y_pred[viz_indices]
        else:
            X_test_viz = X_test
            y_test_viz = y_test
            y_pred_viz = y_pred

        self._plot_classification_results(X_test_viz, y_test_viz, y_pred_viz,
                                        feature_importance, feature_names,
                                        rf_classifier, scaler)

        return rf_classifier, scaler, {
            'X_train': X_train, 'X_test': X_test,
            'y_train': y_train, 'y_test': y_test,
            'y_pred': y_pred, 'feature_importance': feature_importance
        }

    def _plot_classification_results(self, X_test, y_test, y_pred,
                                   feature_importance, feature_names,
                                   classifier, scaler):
        """绘制分类结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=list(self.defect_types.values()),
                   yticklabels=list(self.defect_types.values()),
                   ax=axes[0, 0])
        axes[0, 0].set_title('混淆矩阵')
        axes[0, 0].set_xlabel('预测类别')
        axes[0, 0].set_ylabel('真实类别')

        # 2. 特征重要性
        importance_df = pd.DataFrame({
            '特征': feature_names,
            '重要性': feature_importance
        }).sort_values('重要性', ascending=True)

        axes[0, 1].barh(importance_df['特征'], importance_df['重要性'],
                       color='skyblue', alpha=0.7)
        axes[0, 1].set_title('特征重要性排序')
        axes[0, 1].set_xlabel('重要性分数')

        # 3. 预测结果散点图 (前两个主要特征)
        for defect_id, defect_name in self.defect_types.items():
            mask_true = y_test == defect_id
            mask_pred = y_pred == defect_id

            # 正确预测的点
            correct_mask = (y_test == y_pred) & (y_test == defect_id)
            if np.any(correct_mask):
                axes[1, 0].scatter(X_test[correct_mask, 0], X_test[correct_mask, 1],
                                 c=self.colors[defect_id], label=f'{defect_name}(正确)',
                                 alpha=0.7, s=50, marker='o')

            # 错误预测的点
            wrong_mask = (y_test != y_pred) & (y_test == defect_id)
            if np.any(wrong_mask):
                axes[1, 0].scatter(X_test[wrong_mask, 0], X_test[wrong_mask, 1],
                                 c=self.colors[defect_id], label=f'{defect_name}(错误)',
                                 alpha=0.7, s=50, marker='x')

        axes[1, 0].set_xlabel('标准化频率特征')
        axes[1, 0].set_ylabel('标准化幅度特征')
        axes[1, 0].set_title('分类结果可视化')
        axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 各类别分类准确率
        from sklearn.metrics import precision_score, recall_score, f1_score

        precision_scores = precision_score(y_test, y_pred, average=None)
        recall_scores = recall_score(y_test, y_pred, average=None)
        f1_scores = f1_score(y_test, y_pred, average=None)

        x = np.arange(len(self.defect_types))
        width = 0.25

        axes[1, 1].bar(x - width, precision_scores, width, label='精确率',
                      color='#FF6B6B', alpha=0.8)
        axes[1, 1].bar(x, recall_scores, width, label='召回率',
                      color='#4ECDC4', alpha=0.8)
        axes[1, 1].bar(x + width, f1_scores, width, label='F1分数',
                      color='#45B7D1', alpha=0.8)

        axes[1, 1].set_xlabel('缺陷类型')
        axes[1, 1].set_ylabel('分数')
        axes[1, 1].set_title('各缺陷类型分类性能')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(list(self.defect_types.values()), rotation=45)
        axes[1, 1].legend()
        axes[1, 1].set_ylim(0, 1.1)
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('blade_defect_classification_results.png', dpi=300, bbox_inches='tight')
        print("分类结果图表已保存为: blade_defect_classification_results.png")

    def generate_statistical_report(self, df):
        """生成统计报告"""
        print("\n" + "="*80)
        print("叶片内腔缺陷数据集统计报告")
        print("="*80)

        # 基本信息
        print(f"数据集大小: {len(df)} 个样本")
        print(f"特征数量: {len(df.columns) - 2} 个特征")  # 减去缺陷类型和编码列
        print(f"缺陷类型: {len(df['缺陷类型'].unique())} 种")

        # 各类别样本数
        print(f"\n各缺陷类型样本分布:")
        for defect_type, count in df['缺陷类型'].value_counts().items():
            print(f"  {defect_type}: {count} 个样本 ({count/len(df)*100:.1f}%)")

        # 特征统计
        print(f"\n主要特征统计信息:")
        feature_cols = [col for col in df.columns if col not in ['缺陷类型', '缺陷编码']]
        stats = df[feature_cols].describe()
        print(stats.round(3))

        # 各缺陷类型的特征均值对比
        print(f"\n各缺陷类型特征均值对比:")
        defect_means = df.groupby('缺陷类型')[feature_cols[:8]].mean()
        print(defect_means.round(3))

        return stats, defect_means


def main():
    """主函数"""
    print("叶片内腔缺陷数据集生成器")
    print("="*50)

    # 创建数据集生成器
    generator = BladeCavityDefectDataset(random_state=42)

    # 生成大规模数据集 (30,000个样本)
    print("正在生成大规模数据集 (30,000个样本)...")
    df, X, y, feature_names = generator.create_dataset(n_samples=30000, save_csv=True,
                                                      filename='blade_cavity_defects_30k.csv')

    # 生成统计报告
    stats, defect_means = generator.generate_statistical_report(df)

    # 为了可视化，使用子集数据 (避免图表过于密集)
    print("正在生成可视化图表 (使用2000个样本子集)...")
    subset_indices = np.random.choice(len(df), 2000, replace=False)
    df_subset = df.iloc[subset_indices].copy()
    X_subset = X[subset_indices]
    y_subset = y[subset_indices]

    # 可视化数据集 (使用子集)
    generator.visualize_dataset(df_subset, X_subset, y_subset, feature_names)

    # 训练分类器 (使用全部数据)
    print("正在使用全部30,000个样本训练分类器...")
    classifier, scaler, results = generator.train_classifier(X, y, feature_names)

    print(f"\n大规模数据集生成完成！")
    print(f"生成的文件:")
    print(f"  - blade_cavity_defects_30k.csv (30,000个样本的数据集)")
    print(f"  - blade_cavity_defect_dataset_visualization.png (数据可视化)")
    print(f"  - blade_defect_classification_results.png (分类结果)")

    return df, X, y, feature_names, classifier, scaler


if __name__ == "__main__":
    df, X, y, feature_names, classifier, scaler = main()
