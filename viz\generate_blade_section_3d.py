# -*- coding: utf-8 -*-
"""
Generate a 3D visualization of a wind turbine blade cross-sectional profile.
- Default airfoil: NACA 4412 (4-digit)
- Saves a PNG image to output/blade_section_3d.png
- Requires: numpy, matplotlib (uses Agg backend; no GUI needed)

Usage example:
  python viz/generate_blade_section_3d.py --naca 4412 --chord 1.2 --span 0.25 --twist 5 --resolution 300 --outfile output/blade_section_3d.png
"""

import os
import argparse
import numpy as np
import matplotlib
matplotlib.use("Agg")  # headless backend
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401 (needed for 3D projection)


def parse_args():
    p = argparse.ArgumentParser(description="Render a 3D cross-sectional airfoil (extruded) of a wind turbine blade.")
    p.add_argument("--naca", type=str, default="4412", help="NACA 4-digit code, e.g., 2412, 0012, 4412")
    p.add_argument("--chord", type=float, default=1.0, help="Chord length (meters)")
    p.add_argument("--span", type=float, default=0.2, help="Small spanwise extrusion (meters) for 3D effect")
    p.add_argument("--twist", type=float, default=0.0, help="Twist angle in degrees (rotation around spanwise axis)")
    p.add_argument("--resolution", type=int, default=200, help="Number of points along the chord")
    p.add_argument("--outfile", type=str, default="output/blade_section_3d.png", help="Output image path (PNG)")
    return p.parse_args()


def naca4_coords(code: str, n_points: int = 200) -> np.ndarray:
    """Generate airfoil coordinates for a NACA 4-digit code.
    Returns array shape (2*n_points, 2) for concatenated upper (front->back) and lower (back->front) surfaces.
    Based on classic NACA equations.
    """
    if len(code) != 4 or not code.isdigit():
        raise ValueError("NACA code must be 4 digits, e.g., '4412'.")

    m = int(code[0]) / 100.0       # maximum camber
    p = int(code[1]) / 10.0        # location of maximum camber (x/c)
    t = int(code[2:]) / 100.0      # maximum thickness

    # Cosine spacing for better nose resolution
    beta = np.linspace(0.0, np.pi, n_points)
    x = (1 - np.cos(beta)) / 2.0  # from 0 to 1

    # Thickness distribution (NACA formula)
    yt = 5 * t * (
        0.2969 * np.sqrt(x)
        - 0.1260 * x
        - 0.3516 * x**2
        + 0.2843 * x**3
        - 0.1015 * x**4
    )

    # Camber line and slope
    yc = np.zeros_like(x)
    dyc_dx = np.zeros_like(x)

    for i, xi in enumerate(x):
        if p == 0:
            yc[i] = 0.0
            dyc_dx[i] = 0.0
        elif xi < p:
            yc[i] = (m / p**2) * (2 * p * xi - xi**2)
            dyc_dx[i] = (2 * m / p**2) * (p - xi)
        else:
            yc[i] = (m / (1 - p) ** 2) * ((1 - 2 * p) + 2 * p * xi - xi**2)
            dyc_dx[i] = (2 * m / (1 - p) ** 2) * (p - xi)

    theta = np.arctan(dyc_dx)

    # Upper and lower surface
    xu = x - yt * np.sin(theta)
    yu = yc + yt * np.cos(theta)
    xl = x + yt * np.sin(theta)
    yl = yc - yt * np.cos(theta)

    # Build closed loop: upper surface (leading->trailing), then lower (trailing->leading)
    x_coords = np.concatenate([xu, xl[::-1]])
    y_coords = np.concatenate([yu, yl[::-1]])

    return np.stack([x_coords, y_coords], axis=1)


def apply_chord_and_twist(coords: np.ndarray, chord: float, twist_deg: float) -> np.ndarray:
    """Scale by chord and rotate around spanwise axis (z up, x streamwise). For a 2D profile in x-y plane, a twist
    around the z (or span) depends on convention. Here we rotate in-plane around the origin as a simple depiction."""
    # Scale
    pts = coords.copy()
    pts *= chord

    if abs(twist_deg) > 1e-6:
        th = np.deg2rad(twist_deg)
        c, s = np.cos(th), np.sin(th)
        R = np.array([[c, -s], [s, c]])
        pts = pts @ R.T

    return pts


def extrude_profile_3d(profile_2d: np.ndarray, span: float, ny: int = 20):
    """Extrude a 2D profile (x,y) along spanwise Y-axis to get 3D surface grids X, Y, Z.
    We'll interpret original axes as: x->X, y->Z, and extrude along Y for a nicer view."""
    x2d = profile_2d[:, 0]
    z2d = profile_2d[:, 1]

    y_vals = np.linspace(-span / 2.0, span / 2.0, ny)

    X = np.tile(x2d[:, None], (1, ny))
    Y = np.tile(y_vals[None, :], (x2d.shape[0], 1))
    Z = np.tile(z2d[:, None], (1, ny))

    return X, Y, Z


def render_airfoil_3d(code: str, chord: float, span: float, twist: float, resolution: int, outfile: str):
    coords = naca4_coords(code, n_points=resolution)
    coords = apply_chord_and_twist(coords, chord=chord, twist_deg=twist)

    X, Y, Z = extrude_profile_3d(coords, span=span, ny=max(10, resolution // 20))

    # Figure
    fig = plt.figure(figsize=(10, 6), dpi=150)
    ax = fig.add_subplot(111, projection='3d')

    # Surface coloring based on thickness (Z)
    surf = ax.plot_surface(X, Y, Z, cmap='viridis', edgecolor='k', linewidth=0.2, antialiased=True, alpha=0.95)

    # Leading/trailing edges
    ax.plot(X[0, :], Y[0, :], Z[0, :], color='red', linewidth=1.2, label='Leading Edge')
    ax.plot(X[-1, :], Y[-1, :], Z[-1, :], color='black', linewidth=1.2, label='Trailing Edge')

    # Aesthetics
    ax.set_title(f"Wind Turbine Blade Section (NACA {code}, chord={chord}m, span={span}m, twist={twist}°)")
    ax.set_xlabel('X (chord)')
    ax.set_ylabel('Y (span)')
    ax.set_zlabel('Z (thickness/camber)')
    ax.view_init(elev=22, azim=-60)
    # Aspect ratio (fallback for older Matplotlib without set_box_aspect)
    try:
        ax.set_box_aspect([1.5, 0.5, 0.3])  # Matplotlib >= 3.3
    except Exception:
        # Fallback: approximate aspect by setting axis limits proportionally
        xr = X.max() - X.min()
        yr = Y.max() - Y.min()
        zr = Z.max() - Z.min()
        xc = (X.max() + X.min()) / 2.0
        yc = (Y.max() + Y.min()) / 2.0
        zc = (Z.max() + Z.min()) / 2.0
        target = np.array([1.5, 0.5, 0.3])
        target = target / target.max()
        scale = max(xr, yr, zr) / target.max()
        half = 0.5 * scale * target
        ax.set_xlim(xc - half[0], xc + half[0])
        ax.set_ylim(yc - half[1], yc + half[1])
        ax.set_zlim(zc - half[2], zc + half[2])
    ax.grid(True, alpha=0.2)
    ax.legend(loc='upper right')

    cbar = fig.colorbar(surf, shrink=0.7, aspect=20, pad=0.05)
    cbar.set_label('Camber/Thickness Value')

    # Ensure output dir
    outdir = os.path.dirname(outfile)
    if outdir and not os.path.exists(outdir):
        os.makedirs(outdir, exist_ok=True)

    plt.tight_layout()
    fig.savefig(outfile, bbox_inches='tight')
    plt.close(fig)
    print(f"Saved 3D blade section image to: {outfile}")


if __name__ == "__main__":
    args = parse_args()
    render_airfoil_3d(code=args.naca, chord=args.chord, span=args.span, twist=args.twist, resolution=args.resolution, outfile=args.outfile)

