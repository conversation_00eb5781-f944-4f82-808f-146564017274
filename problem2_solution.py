"""
问题2详细求解：已知零配件和成品次品率情况下的生产过程各阶段决策
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from itertools import product

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ProductionDecisionOptimizer:
    """生产决策优化器"""
    
    def __init__(self):
        # 基础参数（根据题目给定）
        self.p1 = 0.10  # 零配件1次品率
        self.p2 = 0.10  # 零配件2次品率
        self.c1 = 4     # 零配件1购买成本
        self.c2 = 18    # 零配件2购买成本
        self.ca = 6     # 装配成本
        self.ct1 = 2    # 零配件1检测成本
        self.ct2 = 3    # 零配件2检测成本
        self.cd = 5     # 拆解成本
        self.s = 56     # 成品市场售价
        self.cr = 6     # 调换损失
        
        # 计算成品次品率
        self.p_product = self.p1 + self.p2 - self.p1 * self.p2
        
    def calculate_scenario_profit(self, inspect_1, inspect_2, disassemble_defects, inspect_products):
        """
        计算特定决策组合下的期望利润
        
        参数:
        inspect_1: 是否检测零配件1
        inspect_2: 是否检测零配件2
        disassemble_defects: 是否拆解不合格成品
        inspect_products: 是否检测成品
        """
        
        # 第一阶段：零配件检测决策
        if inspect_1:
            # 检测零配件1，剔除次品
            qualified_rate_1 = 1.0
            cost_per_unit_1 = self.c1 + self.ct1
        else:
            # 不检测，直接使用
            qualified_rate_1 = 1 - self.p1
            cost_per_unit_1 = self.c1
            
        if inspect_2:
            # 检测零配件2，剔除次品
            qualified_rate_2 = 1.0
            cost_per_unit_2 = self.c2 + self.ct2
        else:
            # 不检测，直接使用
            qualified_rate_2 = 1 - self.p2
            cost_per_unit_2 = self.c2
        
        # 第二阶段：装配
        # 成品合格率取决于零配件检测情况
        if inspect_1 and inspect_2:
            product_qualified_rate = 1.0  # 两个零配件都合格
        elif inspect_1 and not inspect_2:
            product_qualified_rate = 1 - self.p2  # 只有零配件2可能有问题
        elif not inspect_1 and inspect_2:
            product_qualified_rate = 1 - self.p1  # 只有零配件1可能有问题
        else:
            product_qualified_rate = (1 - self.p1) * (1 - self.p2)  # 两个都可能有问题
        
        product_defect_rate = 1 - product_qualified_rate
        
        # 第三阶段：成品检测决策
        if inspect_products:
            # 检测成品，假设检测成本为1元/件，检测准确率100%
            product_inspection_cost = 1
            # 检测后，合格品直接销售，不合格品按策略处理
            qualified_products_to_market = product_qualified_rate
            defective_products_detected = product_defect_rate
        else:
            # 不检测成品，直接销售
            product_inspection_cost = 0
            qualified_products_to_market = product_qualified_rate
            # 不合格品也会进入市场，造成调换损失
            defective_products_to_market = product_defect_rate
            defective_products_detected = 0
        
        # 第四阶段：不合格品处理决策
        if disassemble_defects and defective_products_detected > 0:
            # 拆解不合格品
            disassemble_cost = defective_products_detected * self.cd
            # 拆解后可回收部分零配件价值（假设回收率50%）
            recovery_value = defective_products_detected * 0.5 * (self.c1 + self.c2)
            defect_handling_cost = disassemble_cost - recovery_value
        else:
            # 报废不合格品
            defect_handling_cost = defective_products_detected * (cost_per_unit_1 + cost_per_unit_2 + self.ca)
        
        # 计算总期望利润
        revenue = qualified_products_to_market * self.s
        
        total_cost = (cost_per_unit_1 + cost_per_unit_2 + self.ca + 
                     product_inspection_cost + defect_handling_cost)
        
        # 市场调换损失（如果不合格品进入市场）
        if not inspect_products:
            market_loss = defective_products_to_market * self.cr
        else:
            market_loss = 0
        
        expected_profit = revenue - total_cost - market_loss
        
        return {
            'expected_profit': expected_profit,
            'revenue': revenue,
            'total_cost': total_cost,
            'market_loss': market_loss,
            'product_qualified_rate': product_qualified_rate,
            'defect_handling_cost': defect_handling_cost
        }
    
    def comprehensive_analysis(self):
        """全面分析所有可能的决策组合"""
        print("=" * 80)
        print("问题2：生产过程各阶段决策全面分析")
        print("=" * 80)
        
        # 所有可能的决策组合
        decisions = list(product([True, False], repeat=4))
        decision_names = ['检测零配件1', '检测零配件2', '拆解不合格品', '检测成品']
        
        results = []
        
        for i, decision in enumerate(decisions):
            inspect_1, inspect_2, disassemble, inspect_products = decision
            
            result = self.calculate_scenario_profit(inspect_1, inspect_2, disassemble, inspect_products)
            
            results.append({
                '方案编号': f'方案{i+1:02d}',
                '检测零配件1': '是' if inspect_1 else '否',
                '检测零配件2': '是' if inspect_2 else '否',
                '拆解不合格品': '是' if disassemble else '否',
                '检测成品': '是' if inspect_products else '否',
                '期望利润': result['expected_profit'],
                '成品合格率': result['product_qualified_rate'],
                '总成本': result['total_cost'],
                '市场损失': result['market_loss']
            })
        
        df_results = pd.DataFrame(results)
        
        # 按期望利润排序
        df_sorted = df_results.sort_values('期望利润', ascending=False)
        
        print("所有决策方案的期望利润排序（前10名）：")
        print(df_sorted.head(10).to_string(index=False, float_format='%.2f'))
        
        # 找出最优方案
        best_solution = df_sorted.iloc[0]
        print(f"\n最优决策方案：{best_solution['方案编号']}")
        print(f"决策组合：")
        print(f"  - 检测零配件1：{best_solution['检测零配件1']}")
        print(f"  - 检测零配件2：{best_solution['检测零配件2']}")
        print(f"  - 拆解不合格品：{best_solution['拆解不合格品']}")
        print(f"  - 检测成品：{best_solution['检测成品']}")
        print(f"最大期望利润：{best_solution['期望利润']:.2f}元")
        print(f"成品合格率：{best_solution['成品合格率']:.1%}")
        
        return df_sorted, best_solution
    
    def sensitivity_analysis(self):
        """敏感性分析"""
        print(f"\n{'='*60}")
        print("敏感性分析")
        print(f"{'='*60}")
        
        # 分析次品率变化对最优决策的影响
        defect_rates = np.arange(0.05, 0.25, 0.02)
        optimal_profits = []
        optimal_strategies = []
        
        original_p1, original_p2 = self.p1, self.p2
        
        for rate in defect_rates:
            self.p1 = self.p2 = rate
            
            # 重新计算所有方案
            best_profit = -float('inf')
            best_strategy = None
            
            decisions = list(product([True, False], repeat=4))
            for decision in decisions:
                result = self.calculate_scenario_profit(*decision)
                if result['expected_profit'] > best_profit:
                    best_profit = result['expected_profit']
                    best_strategy = decision
            
            optimal_profits.append(best_profit)
            optimal_strategies.append(best_strategy)
        
        # 恢复原始值
        self.p1, self.p2 = original_p1, original_p2
        
        # 绘制敏感性分析图
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 1, 1)
        plt.plot(defect_rates, optimal_profits, 'b-o', linewidth=2, markersize=6)
        plt.xlabel('零配件次品率')
        plt.ylabel('最优期望利润')
        plt.title('次品率对最优期望利润的影响')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 1, 2)
        # 分析策略变化
        strategy_codes = []
        for strategy in optimal_strategies:
            code = sum([2**i for i, decision in enumerate(strategy) if decision])
            strategy_codes.append(code)
        
        plt.plot(defect_rates, strategy_codes, 'r-s', linewidth=2, markersize=6)
        plt.xlabel('零配件次品率')
        plt.ylabel('最优策略编码')
        plt.title('次品率对最优策略的影响')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('sensitivity_analysis_problem2.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return defect_rates, optimal_profits, optimal_strategies
    
    def cost_benefit_analysis(self):
        """成本效益分析"""
        print(f"\n{'='*60}")
        print("成本效益分析")
        print(f"{'='*60}")
        
        # 分析不同决策的成本构成
        key_scenarios = [
            (False, False, False, False),  # 什么都不做
            (True, True, False, False),    # 只检测零配件
            (False, False, True, True),    # 只处理成品
            (True, True, True, True),      # 全面质量控制
        ]
        
        scenario_names = ['无质量控制', '零配件检测', '成品处理', '全面质量控制']
        
        cost_breakdown = []
        
        for i, scenario in enumerate(key_scenarios):
            result = self.calculate_scenario_profit(*scenario)
            
            cost_breakdown.append({
                '方案': scenario_names[i],
                '期望利润': result['expected_profit'],
                '总收入': result['revenue'],
                '总成本': result['total_cost'],
                '市场损失': result['market_loss'],
                '成品合格率': result['product_qualified_rate']
            })
        
        df_cost = pd.DataFrame(cost_breakdown)
        print("关键方案的成本效益对比：")
        print(df_cost.to_string(index=False, float_format='%.2f'))
        
        # 绘制对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 利润对比
        ax1.bar(df_cost['方案'], df_cost['期望利润'], color=['red', 'orange', 'lightblue', 'green'])
        ax1.set_ylabel('期望利润')
        ax1.set_title('不同方案的期望利润对比')
        ax1.tick_params(axis='x', rotation=45)
        
        # 成本构成
        ax2.bar(df_cost['方案'], df_cost['总成本'], label='总成本', alpha=0.7)
        ax2.bar(df_cost['方案'], df_cost['市场损失'], bottom=df_cost['总成本'], 
                label='市场损失', alpha=0.7)
        ax2.set_ylabel('成本')
        ax2.set_title('不同方案的成本构成')
        ax2.legend()
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('cost_benefit_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return df_cost

def main():
    """主函数"""
    optimizer = ProductionDecisionOptimizer()
    
    # 1. 全面分析
    df_results, best_solution = optimizer.comprehensive_analysis()
    
    # 2. 敏感性分析
    defect_rates, profits, strategies = optimizer.sensitivity_analysis()
    
    # 3. 成本效益分析
    cost_analysis = optimizer.cost_benefit_analysis()
    
    # 4. 总结建议
    print(f"\n{'='*80}")
    print("总结与建议")
    print(f"{'='*80}")
    print("基于全面的数学分析，我们得出以下结论：")
    print(f"1. 最优决策方案能够实现期望利润 {best_solution['期望利润']:.2f} 元")
    print(f"2. 该方案的成品合格率为 {best_solution['成品合格率']:.1%}")
    print("3. 敏感性分析表明，当次品率较低时，适度的质量控制是最经济的")
    print("4. 当次品率较高时，需要加强质量控制措施")
    print("5. 建议企业根据实际的次品率水平动态调整质量控制策略")
    
    return optimizer, df_results, best_solution

if __name__ == "__main__":
    optimizer, results, best = main()
