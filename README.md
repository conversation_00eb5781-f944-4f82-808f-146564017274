# 🐍 贪吃蛇游戏

一个基于HTML5 Canvas和JavaScript开发的经典贪吃蛇游戏。

## 🎮 游戏特性

- **经典玩法**: 控制蛇吃食物，避免撞墙和撞到自己
- **得分系统**: 每吃一个食物得10分
- **最高分记录**: 自动保存并显示历史最高分
- **动态难度**: 随着得分增加，游戏速度会逐渐提升
- **暂停功能**: 按空格键可以暂停/继续游戏
- **美观界面**: 现代化的渐变背景和毛玻璃效果

## 🕹️ 游戏控制

- **方向键**: 控制蛇的移动方向
  - ↑ 向上
  - ↓ 向下
  - ← 向左
  - → 向右
- **空格键**: 暂停/继续游戏
- **重新开始按钮**: 重置游戏

## 🚀 如何运行

1. 确保所有文件都在同一个目录下：
   - `index.html`
   - `snake.js`

2. 用浏览器打开 `index.html` 文件即可开始游戏

## 📁 文件结构

```
youxi/
├── index.html      # 游戏主页面
├── snake.js        # 游戏逻辑
└── README.md       # 说明文档
```

## 🎯 游戏规则

1. 蛇会自动向前移动
2. 使用方向键改变蛇的移动方向
3. 吃到红色食物可以增长身体并获得分数
4. 撞到墙壁或自己的身体会导致游戏结束
5. 每50分游戏速度会增加一次

## 🏆 得分机制

- 每吃一个食物: +10分
- 最高分会自动保存在浏览器本地存储中
- 游戏速度每50分提升一次，增加挑战性

## 🎨 界面特色

- 渐变背景色
- 毛玻璃效果的游戏容器
- 平滑的动画效果
- 响应式设计
- 现代化的按钮样式

## 🔧 技术实现

- **HTML5 Canvas**: 游戏渲染
- **JavaScript**: 游戏逻辑
- **CSS3**: 界面样式和动画效果
- **LocalStorage**: 最高分数据持久化

## 🌟 未来改进方向

- 添加音效
- 增加更多游戏模式
- 添加移动端触摸控制
- 实现多人对战模式
- 添加道具系统

享受游戏吧！🎉
