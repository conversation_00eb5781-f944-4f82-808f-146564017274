# 🗄️ 缺陷数据库建立完整指南

## 📋 目录
1. [数据库建立概述](#概述)
2. [数据采集阶段](#数据采集)
3. [数据预处理](#数据预处理)
4. [特征工程](#特征工程)
5. [数据标注](#数据标注)
6. [数据库设计](#数据库设计)
7. [质量控制](#质量控制)
8. [实施时间表](#实施时间表)

---

## 🎯 概述

建立缺陷数据库是一个系统性工程，需要从数据采集、预处理、特征提取、标注到最终存储的完整流程。

### 核心目标
- 建立标准化的缺陷信号数据库
- 支持多种缺陷类型的智能识别
- 提供高质量的训练数据集
- 确保数据的可扩展性和可维护性

---

## 📡 数据采集阶段

### 1. 检测方法选择

#### 🔊 超声波检测
- **频率范围**: 2-10 MHz
- **探头类型**: 直探头、斜探头、双晶探头
- **采样率**: 50-100 MHz
- **适用缺陷**: 内部裂纹、分层、孔洞

#### 🎵 声发射监测
- **频率范围**: 100 kHz - 1 MHz
- **传感器类型**: 宽频带压电传感器
- **采样率**: 2-5 MHz
- **适用缺陷**: 裂纹扩展、纤维断裂

#### 📳 振动信号分析
- **频率范围**: 0-20 kHz
- **传感器类型**: 加速度计、位移传感器
- **采样率**: 50 kHz
- **适用缺陷**: 结构松动、鼓包变形

### 2. 数据采集策略

```python
# 采集参数配置
SAMPLING_CONFIG = {
    'ultrasonic': {
        'sampling_rate': 50e6,  # 50 MHz
        'duration': 0.001,      # 1 ms
        'channels': 4,          # 4通道
        'gain': 40              # 40 dB增益
    },
    'acoustic_emission': {
        'sampling_rate': 2e6,   # 2 MHz
        'duration': 10,         # 10 s
        'channels': 8,          # 8通道
        'threshold': 45         # 45 dB阈值
    },
    'vibration': {
        'sampling_rate': 50e3,  # 50 kHz
        'duration': 60,         # 60 s
        'channels': 3,          # 3轴加速度
        'sensitivity': 100      # 100 mV/g
    }
}
```

### 3. 空间采样策略

#### 网格化采样
- **叶片根部**: 5cm × 5cm 网格
- **叶片中部**: 10cm × 10cm 网格  
- **叶片尖部**: 15cm × 15cm 网格

#### 关键区域加密
- 应力集中区域
- 历史缺陷多发区
- 连接接头附近

---

## 🔧 数据预处理

### 1. 信号预处理流程

```python
def preprocess_signal(raw_signal, method='ultrasonic'):
    """信号预处理管道"""
    
    # 1. 去除直流分量
    signal = raw_signal - np.mean(raw_signal)
    
    # 2. 滤波处理
    if method == 'ultrasonic':
        # 带通滤波 1-15 MHz
        signal = bandpass_filter(signal, 1e6, 15e6, fs)
    elif method == 'acoustic_emission':
        # 高通滤波 100 kHz
        signal = highpass_filter(signal, 100e3, fs)
    elif method == 'vibration':
        # 低通滤波 10 kHz
        signal = lowpass_filter(signal, 10e3, fs)
    
    # 3. 噪声抑制
    signal = wavelet_denoise(signal, wavelet='db4', levels=6)
    
    # 4. 幅值归一化
    signal = signal / np.max(np.abs(signal))
    
    return signal
```

### 2. 数据质量检查

#### 自动质量评估
- **信噪比检查**: SNR > 20 dB
- **饱和度检查**: 避免信号削波
- **完整性检查**: 数据包完整性
- **一致性检查**: 多通道时间同步

#### 异常数据处理
```python
def quality_check(signal, metadata):
    """数据质量检查"""
    checks = {
        'snr': calculate_snr(signal) > 20,
        'saturation': np.max(np.abs(signal)) < 0.95,
        'completeness': len(signal) == metadata['expected_length'],
        'variance': np.var(signal) > 1e-6
    }
    return all(checks.values()), checks
```

---

## 🧠 特征工程

### 1. 时域特征提取

#### 统计特征 (10维)
```python
def extract_time_features(signal):
    """提取时域特征"""
    features = {
        '均值': np.mean(signal),
        '标准差': np.std(signal),
        '有效值': np.sqrt(np.mean(signal**2)),
        '峰值': np.max(np.abs(signal)),
        '峰峰值': np.max(signal) - np.min(signal),
        '偏度': scipy.stats.skew(signal),
        '峭度': scipy.stats.kurtosis(signal),
        '波形因子': np.sqrt(np.mean(signal**2)) / np.mean(np.abs(signal)),
        '峰值因子': np.max(np.abs(signal)) / np.sqrt(np.mean(signal**2)),
        '脉冲因子': np.max(np.abs(signal)) / np.mean(np.abs(signal))
    }
    return list(features.values())
```

### 2. 频域特征提取

#### 频谱特征 (15维)
```python
def extract_frequency_features(signal, fs):
    """提取频域特征"""
    # FFT变换
    fft_signal = np.fft.fft(signal)
    freqs = np.fft.fftfreq(len(signal), 1/fs)
    magnitude = np.abs(fft_signal[:len(fft_signal)//2])
    
    features = {
        '主频率': freqs[np.argmax(magnitude)],
        '频谱重心': np.sum(freqs * magnitude) / np.sum(magnitude),
        '频谱方差': np.sum((freqs - freq_centroid)**2 * magnitude) / np.sum(magnitude),
        '频谱偏度': calculate_spectral_skewness(freqs, magnitude),
        '频谱峭度': calculate_spectral_kurtosis(freqs, magnitude),
        '低频能量比': np.sum(magnitude[freqs <= 50]) / np.sum(magnitude),
        '中频能量比': np.sum(magnitude[(freqs > 50) & (freqs <= 200)]) / np.sum(magnitude),
        '高频能量比': np.sum(magnitude[freqs > 200]) / np.sum(magnitude),
        # ... 更多频域特征
    }
    return list(features.values())
```

### 3. 时频特征提取

#### 小波包分解特征 (20维)
```python
def extract_wavelet_features(signal):
    """提取小波包特征"""
    import pywt
    
    # 小波包分解
    wp = pywt.WaveletPacket(signal, 'db4', mode='symmetric', maxlevel=4)
    
    # 提取各频段能量
    features = []
    for node in wp.get_level(4):
        energy = np.sum(node.data**2)
        features.append(energy)
    
    return features
```

---

## 🏷️ 数据标注

### 1. 标注体系设计

#### 缺陷类型分类
```python
DEFECT_TAXONOMY = {
    'manufacturing_defects': {
        'fiber_wrinkle': '纤维褶皱',
        'void': '孔洞',
        'resin_rich': '富树脂区',
        'dry_fiber': '干纤维'
    },
    'service_defects': {
        'fatigue_crack': '疲劳裂纹',
        'delamination': '分层',
        'impact_damage': '冲击损伤',
        'erosion': '侵蚀磨损'
    },
    'structural_defects': {
        'bulge': '鼓包',
        'wrinkle': '褶皱',
        'twist': '扭曲',
        'debond': '脱粘'
    }
}
```

#### 严重程度分级
```python
SEVERITY_LEVELS = {
    1: {'name': '轻微', 'action': '继续监测', 'color': 'green'},
    2: {'name': '中等', 'action': '加强监测', 'color': 'yellow'},
    3: {'name': '严重', 'action': '计划维修', 'color': 'orange'},
    4: {'name': '危险', 'action': '立即停机', 'color': 'red'}
}
```

### 2. 标注工具设计

#### 交互式标注界面
- 信号波形显示
- 缺陷类型选择器
- 严重程度滑块
- 置信度评估
- 标注历史记录

---

## 🗃️ 数据库设计

### 1. 数据库架构

#### 核心表结构
```sql
-- 设备信息表
CREATE TABLE equipment_info (
    equipment_id VARCHAR(50) PRIMARY KEY,
    model VARCHAR(100),
    manufacturer VARCHAR(100),
    install_date DATE,
    location VARCHAR(200)
);

-- 检测记录表
CREATE TABLE inspection_records (
    inspection_id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50),
    inspection_date DATETIME,
    method VARCHAR(50),
    operator VARCHAR(50),
    weather_condition VARCHAR(100),
    FOREIGN KEY (equipment_id) REFERENCES equipment_info(equipment_id)
);

-- 原始信号表
CREATE TABLE raw_signals (
    signal_id VARCHAR(50) PRIMARY KEY,
    inspection_id VARCHAR(50),
    channel_id INT,
    sampling_rate DECIMAL(12,2),
    signal_length INT,
    signal_data LONGBLOB,
    metadata JSON,
    FOREIGN KEY (inspection_id) REFERENCES inspection_records(inspection_id)
);

-- 特征数据表
CREATE TABLE feature_data (
    feature_id VARCHAR(50) PRIMARY KEY,
    signal_id VARCHAR(50),
    feature_vector JSON,
    extraction_method VARCHAR(100),
    extraction_date DATETIME,
    FOREIGN KEY (signal_id) REFERENCES raw_signals(signal_id)
);

-- 缺陷标注表
CREATE TABLE defect_annotations (
    annotation_id VARCHAR(50) PRIMARY KEY,
    signal_id VARCHAR(50),
    defect_type VARCHAR(100),
    severity_level INT,
    confidence_score DECIMAL(3,2),
    annotator VARCHAR(50),
    annotation_date DATETIME,
    verified BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (signal_id) REFERENCES raw_signals(signal_id)
);
```

### 2. 数据存储策略

#### 分层存储
- **热数据**: 最近3个月的数据，SSD存储
- **温数据**: 3-12个月的数据，机械硬盘
- **冷数据**: 12个月以上，归档存储

#### 数据压缩
```python
def compress_signal_data(signal):
    """信号数据压缩"""
    import zlib
    import pickle
    
    # 序列化并压缩
    serialized = pickle.dumps(signal)
    compressed = zlib.compress(serialized, level=6)
    
    return compressed

def decompress_signal_data(compressed_data):
    """信号数据解压"""
    import zlib
    import pickle
    
    # 解压并反序列化
    decompressed = zlib.decompress(compressed_data)
    signal = pickle.loads(decompressed)
    
    return signal
```

---

## ⚡ 质量控制

### 1. 数据质量指标

#### 完整性指标
- 数据完整率 ≥ 95%
- 标注完整率 ≥ 90%
- 多专家标注一致性 ≥ 85%

#### 准确性指标
- 标注准确率 ≥ 95%
- 交叉验证准确率 ≥ 90%
- 专家验证通过率 ≥ 95%

### 2. 质量控制流程

```python
class DataQualityController:
    """数据质量控制器"""
    
    def __init__(self):
        self.quality_metrics = {}
        
    def validate_signal_quality(self, signal, metadata):
        """验证信号质量"""
        checks = {
            'snr_check': self._check_snr(signal),
            'length_check': self._check_length(signal, metadata),
            'range_check': self._check_amplitude_range(signal),
            'continuity_check': self._check_continuity(signal)
        }
        
        overall_quality = all(checks.values())
        return overall_quality, checks
    
    def validate_annotation_consistency(self, annotations):
        """验证标注一致性"""
        # 多专家标注一致性检查
        consistency_score = self._calculate_inter_annotator_agreement(annotations)
        return consistency_score > 0.85
```

---

## 📅 实施时间表

### 第一阶段：准备与规划 (2-3周)
- [ ] 需求分析与技术调研
- [ ] 设备选型与采购
- [ ] 人员培训与标准制定
- [ ] 数据库架构设计

### 第二阶段：数据采集 (8-12周)
- [ ] 设备安装与调试
- [ ] 采集方案实施
- [ ] 数据质量监控
- [ ] 初步数据验证

### 第三阶段：数据处理 (6-8周)
- [ ] 数据预处理管道开发
- [ ] 特征提取算法实现
- [ ] 数据清洗与验证
- [ ] 质量控制体系建立

### 第四阶段：标注与验证 (8-10周)
- [ ] 标注工具开发
- [ ] 专家标注培训
- [ ] 批量标注执行
- [ ] 标注质量验证

### 第五阶段：数据库构建 (4-6周)
- [ ] 数据库系统部署
- [ ] 数据导入与索引
- [ ] 访问接口开发
- [ ] 系统测试与优化

### 第六阶段：验证与部署 (3-4周)
- [ ] 模型训练与验证
- [ ] 系统集成测试
- [ ] 用户培训
- [ ] 正式部署上线

---

## 💡 关键成功因素

### 技术因素
1. **标准化流程**: 确保数据采集的一致性
2. **自动化工具**: 减少人工错误，提高效率
3. **质量控制**: 建立多层次的质量保证体系
4. **可扩展性**: 支持新缺陷类型和检测方法

### 管理因素
1. **专家团队**: 组建跨学科专家团队
2. **培训体系**: 建立标准化培训流程
3. **版本控制**: 数据和标注的版本管理
4. **文档管理**: 完整的技术文档体系

---

## 📊 预期成果

### 数据库规模
- **信号样本**: 100,000+ 个
- **缺陷类型**: 12+ 种
- **特征维度**: 50+ 维
- **标注质量**: 95%+ 准确率

### 应用价值
- 支持智能缺陷识别
- 提供标准化数据集
- 促进算法研究发展
- 推动行业标准建立
