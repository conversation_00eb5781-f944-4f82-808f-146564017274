import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_sample_data(n_samples=500):
    """
    生成模拟数据集
    包含3个类别的二维数据点
    """
    np.random.seed(42)
    
    # 类别1：圆形分布
    class1_center = [2, 2]
    class1_x = np.random.normal(class1_center[0], 0.8, n_samples//3)
    class1_y = np.random.normal(class1_center[1], 0.8, n_samples//3)
    class1_labels = np.zeros(n_samples//3)
    
    # 类别2：椭圆形分布
    class2_center = [6, 3]
    class2_x = np.random.normal(class2_center[0], 1.2, n_samples//3)
    class2_y = np.random.normal(class2_center[1], 0.6, n_samples//3)
    class2_labels = np.ones(n_samples//3)
    
    # 类别3：三角形分布
    class3_center = [4, 6]
    class3_x = np.random.normal(class3_center[0], 0.9, n_samples//3)
    class3_y = np.random.normal(class3_center[1], 0.9, n_samples//3)
    class3_labels = np.full(n_samples//3, 2)
    
    # 合并所有数据
    X = np.column_stack([
        np.concatenate([class1_x, class2_x, class3_x]),
        np.concatenate([class1_y, class2_y, class3_y])
    ])
    
    y = np.concatenate([class1_labels, class2_labels, class3_labels])
    
    return X, y

def plot_data_distribution(X, y, title="数据分布"):
    """
    可视化数据分布
    """
    plt.figure(figsize=(10, 8))
    
    colors = ['red', 'blue', 'green']
    labels = ['类别 1', '类别 2', '类别 3']
    
    for i in range(3):
        mask = y == i
        plt.scatter(X[mask, 0], X[mask, 1], 
                   c=colors[i], label=labels[i], alpha=0.7, s=50)
    
    plt.xlabel('特征 X1')
    plt.ylabel('特征 X2')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

def train_knn_model(X_train, y_train, k=5):
    """
    训练KNN模型
    """
    knn = KNeighborsClassifier(n_neighbors=k)
    knn.fit(X_train, y_train)
    return knn

def evaluate_model(model, X_test, y_test):
    """
    评估模型性能
    """
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"模型准确率: {accuracy:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred, target_names=['类别 1', '类别 2', '类别 3']))
    
    return y_pred

def plot_confusion_matrix(y_test, y_pred):
    """
    绘制混淆矩阵
    """
    plt.figure(figsize=(8, 6))
    cm = confusion_matrix(y_test, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['类别 1', '类别 2', '类别 3'],
                yticklabels=['类别 1', '类别 2', '类别 3'])
    plt.title('混淆矩阵')
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    plt.show()

def plot_decision_boundary(model, X, y, title="KNN决策边界"):
    """
    绘制决策边界
    """
    plt.figure(figsize=(12, 8))
    
    # 创建网格
    h = 0.1
    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))
    
    # 预测网格点
    Z = model.predict(np.c_[xx.ravel(), yy.ravel()])
    Z = Z.reshape(xx.shape)
    
    # 绘制决策边界
    colors = ['#ffaaaa', '#aaaaff', '#aaffaa']
    cmap = ListedColormap(colors)
    plt.contourf(xx, yy, Z, alpha=0.4, cmap=cmap)
    
    # 绘制数据点
    scatter_colors = ['red', 'blue', 'green']
    labels = ['类别 1', '类别 2', '类别 3']
    
    for i in range(3):
        mask = y == i
        plt.scatter(X[mask, 0], X[mask, 1], 
                   c=scatter_colors[i], label=labels[i], 
                   alpha=0.8, s=50, edgecolors='black')
    
    plt.xlabel('特征 X1')
    plt.ylabel('特征 X2')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

def compare_k_values(X_train, y_train, X_test, y_test, k_range=range(1, 21)):
    """
    比较不同K值的性能
    """
    accuracies = []
    
    for k in k_range:
        knn = KNeighborsClassifier(n_neighbors=k)
        knn.fit(X_train, y_train)
        y_pred = knn.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        accuracies.append(accuracy)
    
    plt.figure(figsize=(10, 6))
    plt.plot(k_range, accuracies, marker='o', linewidth=2, markersize=8)
    plt.xlabel('K值')
    plt.ylabel('准确率')
    plt.title('不同K值对模型性能的影响')
    plt.grid(True, alpha=0.3)
    plt.xticks(k_range)
    
    # 标注最佳K值
    best_k = k_range[np.argmax(accuracies)]
    best_accuracy = max(accuracies)
    plt.annotate(f'最佳K={best_k}\n准确率={best_accuracy:.4f}',
                xy=(best_k, best_accuracy),
                xytext=(best_k+3, best_accuracy-0.02),
                arrowprops=dict(arrowstyle='->', color='red'),
                fontsize=12, color='red')
    
    plt.show()
    
    return best_k, best_accuracy

def main():
    """
    主函数：运行完整的KNN演示
    """
    print("=" * 60)
    print("KNN算法演示 - 使用生成的模拟数据")
    print("=" * 60)
    
    # 1. 生成数据
    print("\n1. 生成模拟数据...")
    X, y = generate_sample_data(n_samples=600)
    print(f"生成数据形状: {X.shape}")
    print(f"类别分布: {np.bincount(y.astype(int))}")
    
    # 2. 可视化原始数据
    print("\n2. 可视化原始数据分布...")
    plot_data_distribution(X, y, "原始数据分布")
    
    # 3. 分割数据
    print("\n3. 分割训练集和测试集...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    print(f"训练集大小: {X_train.shape[0]}")
    print(f"测试集大小: {X_test.shape[0]}")
    
    # 4. 比较不同K值
    print("\n4. 寻找最佳K值...")
    best_k, best_accuracy = compare_k_values(X_train, y_train, X_test, y_test)
    print(f"最佳K值: {best_k}, 对应准确率: {best_accuracy:.4f}")
    
    # 5. 使用最佳K值训练模型
    print(f"\n5. 使用K={best_k}训练最终模型...")
    best_model = train_knn_model(X_train, y_train, k=best_k)
    
    # 6. 评估模型
    print("\n6. 评估模型性能...")
    y_pred = evaluate_model(best_model, X_test, y_test)
    
    # 7. 绘制混淆矩阵
    print("\n7. 绘制混淆矩阵...")
    plot_confusion_matrix(y_test, y_pred)
    
    # 8. 绘制决策边界
    print("\n8. 绘制决策边界...")
    plot_decision_boundary(best_model, X, y, f"KNN决策边界 (K={best_k})")
    
    # 9. 演示预测新数据点
    print("\n9. 演示预测新数据点...")
    new_points = np.array([[3, 4], [1, 1], [5, 6], [7, 2]])
    predictions = best_model.predict(new_points)
    probabilities = best_model.predict_proba(new_points)
    
    print("新数据点预测结果:")
    for i, (point, pred, prob) in enumerate(zip(new_points, predictions, probabilities)):
        print(f"点 {point}: 预测类别 {int(pred)}, 概率分布 {prob}")
    
    print("\n=" * 60)
    print("KNN算法演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()