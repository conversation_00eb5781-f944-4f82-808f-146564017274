import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
from sklearn.neighbors import KNeighborsClassifier
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)

def generate_sample_data():
    """生成示例数据用于KNN原理演示"""
    
    # 生成三个类别的训练数据
    # 类别1 (红色) - 纤维褶皱
    class1_x = np.random.normal(2, 0.8, 15)
    class1_y = np.random.normal(2, 0.8, 15)
    class1 = np.column_stack([class1_x, class1_y])
    
    # 类别2 (蓝色) - 裂纹
    class2_x = np.random.normal(6, 0.9, 15)
    class2_y = np.random.normal(6, 0.9, 15)
    class2 = np.column_stack([class2_x, class2_y])
    
    # 类别3 (绿色) - 分层
    class3_x = np.random.normal(2, 0.7, 15)
    class3_y = np.random.normal(6, 0.7, 15)
    class3 = np.column_stack([class3_x, class3_y])
    
    # 合并训练数据
    X_train = np.vstack([class1, class2, class3])
    y_train = np.array([0]*15 + [1]*15 + [2]*15)
    
    # 生成测试点
    test_point = np.array([4, 4])
    
    return X_train, y_train, test_point

def calculate_distances(X_train, test_point):
    """计算测试点到所有训练点的距离"""
    distances = []
    for point in X_train:
        dist = np.sqrt(np.sum((point - test_point)**2))
        distances.append(dist)
    return np.array(distances)

def create_knn_principle_diagram():
    """创建KNN分类原理图"""
    
    # 生成数据
    X_train, y_train, test_point = generate_sample_data()
    
    # 计算距离
    distances = calculate_distances(X_train, test_point)
    
    # 创建图形
    fig = plt.figure(figsize=(20, 12))
    
    # 定义颜色和标签
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    labels = ['纤维褶皱', '裂纹', '分层']
    markers = ['o', 's', '^']
    
    # 1. KNN基本原理图 (k=5)
    ax1 = plt.subplot(2, 3, 1)
    k = 5
    
    # 绘制训练数据点
    for i, (color, label, marker) in enumerate(zip(colors, labels, markers)):
        mask = y_train == i
        plt.scatter(X_train[mask, 0], X_train[mask, 1], 
                   c=color, label=f'{label} (训练数据)', 
                   s=80, alpha=0.7, marker=marker, edgecolors='black', linewidth=1)
    
    # 绘制测试点
    plt.scatter(test_point[0], test_point[1], c='red', s=200, 
               marker='*', label='测试点', edgecolors='black', linewidth=2)
    
    # 找到k个最近邻
    nearest_indices = np.argsort(distances)[:k]
    
    # 绘制到k个最近邻的连线
    for idx in nearest_indices:
        plt.plot([test_point[0], X_train[idx, 0]], 
                [test_point[1], X_train[idx, 1]], 
                'gray', linestyle='--', alpha=0.6, linewidth=1)
    
    # 高亮k个最近邻
    for idx in nearest_indices:
        plt.scatter(X_train[idx, 0], X_train[idx, 1], 
                   s=150, facecolors='none', edgecolors='red', linewidth=3)
    
    # 绘制决策圆圈
    max_dist = distances[nearest_indices[-1]]
    circle = Circle(test_point, max_dist, fill=False, color='red', 
                   linestyle='-', linewidth=2, alpha=0.8)
    ax1.add_patch(circle)
    
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.title(f'KNN分类原理 (k={k})')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    # 2. 不同k值的影响
    ax2 = plt.subplot(2, 3, 2)
    k_values = [1, 3, 5, 7]
    
    # 绘制训练数据点
    for i, (color, label, marker) in enumerate(zip(colors, labels, markers)):
        mask = y_train == i
        plt.scatter(X_train[mask, 0], X_train[mask, 1], 
                   c=color, s=60, alpha=0.7, marker=marker, edgecolors='black', linewidth=0.5)
    
    # 绘制测试点
    plt.scatter(test_point[0], test_point[1], c='red', s=150, 
               marker='*', edgecolors='black', linewidth=2)
    
    # 为不同k值绘制圆圈
    circle_colors = ['orange', 'purple', 'brown', 'pink']
    for i, k in enumerate(k_values):
        nearest_indices = np.argsort(distances)[:k]
        max_dist = distances[nearest_indices[-1]]
        circle = Circle(test_point, max_dist, fill=False, 
                       color=circle_colors[i], linestyle='-', 
                       linewidth=2, alpha=0.7, label=f'k={k}')
        ax2.add_patch(circle)
    
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.title('不同k值的影响')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. 距离计算示意图
    ax3 = plt.subplot(2, 3, 3)
    
    # 只显示部分点以便清晰展示距离计算
    selected_indices = [0, 15, 30, 10, 25]  # 每个类别选择几个点
    
    for idx in selected_indices:
        color = colors[y_train[idx]]
        marker = markers[y_train[idx]]
        plt.scatter(X_train[idx, 0], X_train[idx, 1], 
                   c=color, s=100, marker=marker, edgecolors='black', linewidth=1)
        
        # 绘制距离线
        plt.plot([test_point[0], X_train[idx, 0]], 
                [test_point[1], X_train[idx, 1]], 
                'gray', linestyle='-', alpha=0.5, linewidth=1)
        
        # 标注距离
        mid_x = (test_point[0] + X_train[idx, 0]) / 2
        mid_y = (test_point[1] + X_train[idx, 1]) / 2
        dist = distances[idx]
        plt.text(mid_x, mid_y, f'{dist:.1f}', fontsize=8, 
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 绘制测试点
    plt.scatter(test_point[0], test_point[1], c='red', s=200, 
               marker='*', edgecolors='black', linewidth=2)
    
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.title('欧几里得距离计算')
    plt.grid(True, alpha=0.3)
    
    # 4. 投票过程示意图
    ax4 = plt.subplot(2, 3, 4)
    
    k = 5
    nearest_indices = np.argsort(distances)[:k]
    nearest_labels = y_train[nearest_indices]
    
    # 统计投票
    vote_counts = np.bincount(nearest_labels, minlength=3)
    
    # 绘制投票结果
    bars = plt.bar(labels, vote_counts, color=colors, alpha=0.7, edgecolor='black')
    
    # 在柱状图上添加数值
    for bar, count in zip(bars, vote_counts):
        if count > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05, 
                     f'{count}票', ha='center', va='bottom', fontweight='bold')
    
    plt.ylabel('投票数')
    plt.title(f'k={k}时的投票过程')
    plt.ylim(0, max(vote_counts) + 1)
    
    # 标注预测结果
    predicted_class = np.argmax(vote_counts)
    plt.text(0.5, 0.9, f'预测结果: {labels[predicted_class]}', 
             transform=ax4.transAxes, fontsize=12, fontweight='bold',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.8),
             ha='center')
    
    plt.grid(True, alpha=0.3)
    
    # 5. KNN算法流程图
    ax5 = plt.subplot(2, 3, 5)
    ax5.set_xlim(0, 10)
    ax5.set_ylim(0, 10)
    ax5.axis('off')
    
    # 绘制流程框
    boxes = [
        {'xy': (5, 9), 'text': '输入测试样本', 'color': '#FFE5E5'},
        {'xy': (5, 7.5), 'text': '计算与所有训练样本的距离', 'color': '#E5F3FF'},
        {'xy': (5, 6), 'text': '选择k个最近邻', 'color': '#E5FFE5'},
        {'xy': (5, 4.5), 'text': '统计k个邻居的类别', 'color': '#FFF5E5'},
        {'xy': (5, 3), 'text': '多数投票决定分类结果', 'color': '#F5E5FF'},
        {'xy': (5, 1.5), 'text': '输出预测类别', 'color': '#FFE5F5'}
    ]
    
    for i, box in enumerate(boxes):
        # 绘制矩形框
        rect = plt.Rectangle((box['xy'][0]-1.5, box['xy'][1]-0.4), 3, 0.8, 
                           facecolor=box['color'], edgecolor='black', linewidth=1)
        ax5.add_patch(rect)
        
        # 添加文字
        plt.text(box['xy'][0], box['xy'][1], box['text'], 
                ha='center', va='center', fontsize=10, fontweight='bold')
        
        # 绘制箭头（除了最后一个框）
        if i < len(boxes) - 1:
            plt.arrow(box['xy'][0], box['xy'][1]-0.5, 0, -0.6, 
                     head_width=0.2, head_length=0.1, fc='black', ec='black')
    
    plt.title('KNN算法流程', fontsize=14, fontweight='bold', pad=20)
    
    # 6. 决策边界可视化
    ax6 = plt.subplot(2, 3, 6)
    
    # 创建网格用于绘制决策边界
    h = 0.1
    x_min, x_max = X_train[:, 0].min() - 1, X_train[:, 0].max() + 1
    y_min, y_max = X_train[:, 1].min() - 1, X_train[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                         np.arange(y_min, y_max, h))
    
    # 训练KNN分类器
    knn = KNeighborsClassifier(n_neighbors=5)
    knn.fit(X_train, y_train)
    
    # 预测网格点
    Z = knn.predict(np.c_[xx.ravel(), yy.ravel()])
    Z = Z.reshape(xx.shape)
    
    # 绘制决策边界
    plt.contourf(xx, yy, Z, alpha=0.3, colors=colors)
    
    # 绘制训练数据点
    for i, (color, label, marker) in enumerate(zip(colors, labels, markers)):
        mask = y_train == i
        plt.scatter(X_train[mask, 0], X_train[mask, 1], 
                   c=color, label=label, s=80, alpha=0.8, 
                   marker=marker, edgecolors='black', linewidth=1)
    
    # 绘制测试点
    plt.scatter(test_point[0], test_point[1], c='red', s=200, 
               marker='*', label='测试点', edgecolors='black', linewidth=2)
    
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.title('KNN决策边界 (k=5)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('knn_principle_diagram.png', dpi=300, bbox_inches='tight')
    print("KNN分类原理图已保存为 knn_principle_diagram.png")
    
    # 打印分析结果
    print("\nKNN分类原理分析:")
    print("="*50)
    print(f"测试点坐标: ({test_point[0]:.1f}, {test_point[1]:.1f})")
    
    k = 5
    nearest_indices = np.argsort(distances)[:k]
    nearest_labels = y_train[nearest_indices]
    nearest_distances = distances[nearest_indices]
    
    print(f"\nk={k}个最近邻:")
    for i, (idx, label, dist) in enumerate(zip(nearest_indices, nearest_labels, nearest_distances)):
        class_name = labels[label]
        print(f"  {i+1}. 点{idx}: {class_name}, 距离={dist:.2f}")
    
    vote_counts = np.bincount(nearest_labels, minlength=3)
    print(f"\n投票结果:")
    for i, (label, count) in enumerate(zip(labels, vote_counts)):
        print(f"  {label}: {count}票")
    
    predicted_class = np.argmax(vote_counts)
    print(f"\n预测结果: {labels[predicted_class]}")
    print(f"置信度: {vote_counts[predicted_class]}/{k} = {vote_counts[predicted_class]/k:.2f}")

if __name__ == "__main__":
    create_knn_principle_diagram()
