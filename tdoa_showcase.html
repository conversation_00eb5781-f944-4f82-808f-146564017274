
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDOA定位法动态演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .animation-container {
            text-align: center;
            margin: 30px 0;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .animation-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        .card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .formula {
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            text-align: center;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            position: relative;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: -12px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .step h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-left: 20px;
        }
        .step p {
            margin-left: 20px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📡 TDOA定位法动态演示</h1>
        
        <div class="highlight">
            <h3>🎯 技术原理</h3>
            <p><strong>时间差定位法（TDOA）</strong> - 通过测量声发射信号到达不同传感器的时间差，利用双曲线交点确定缺陷位置</p>
        </div>

        <div class="animation-container">
            <h2>🎬 TDOA定位动态演示</h2>
            <img src="tdoa_localization.gif" alt="TDOA定位动画">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                左图：风机叶片内腔三角定位示意图 | 右图：声发射信号到达时间分析
            </p>
        </div>

        <div class="animation-container">
            <h2>📊 多位置TDOA分析对比</h2>
            <img src="tdoa_static_analysis.png" alt="TDOA静态分析">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                不同缺陷位置的TDOA定位效果对比分析
            </p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📐 数学原理</h3>
                <p><strong>TDOA计算公式：</strong></p>
                <div class="formula">
                    TDOA₁₂ = (d₂ - d₁) / v<br>
                    TDOA₁₃ = (d₃ - d₁) / v
                </div>
                <p>其中：d为距离，v为声速（5000m/s）</p>
            </div>
            
            <div class="card">
                <h3>🔧 传感器配置</h3>
                <ul>
                    <li><strong>传感器1</strong>：位置(1, 0.5)，参考传感器</li>
                    <li><strong>传感器2</strong>：位置(9, 0.5)，叶片另一端</li>
                    <li><strong>传感器3</strong>：位置(5, 1.5)，叶片中部上方</li>
                    <li><strong>配置优势</strong>：三角形布局，最优定位精度</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎯 定位精度</h3>
                <ul>
                    <li><strong>理论精度</strong>：±0.1m（理想条件下）</li>
                    <li><strong>实际精度</strong>：±0.2-0.5m（考虑噪声）</li>
                    <li><strong>影响因素</strong>：声速变化、信号噪声、传感器精度</li>
                    <li><strong>优化方法</strong>：多传感器融合、滤波算法</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>⚡ 技术优势</h3>
                <ul>
                    <li><strong>实时性</strong>：毫秒级定位响应</li>
                    <li><strong>非接触</strong>：无需直接接触缺陷</li>
                    <li><strong>高精度</strong>：米级精度定位</li>
                    <li><strong>适应性</strong>：适用于各种材料和结构</li>
                </ul>
            </div>
        </div>

        <div class="steps">
            <h2>🔄 TDOA定位算法流程</h2>
            <div class="step">
                <h4>信号采集</h4>
                <p>三个声发射传感器同步采集缺陷产生的声发射信号</p>
            </div>
            <div class="step">
                <h4>到达时间检测</h4>
                <p>通过信号处理算法精确检测信号到达各传感器的时间</p>
            </div>
            <div class="step">
                <h4>时间差计算</h4>
                <p>以传感器1为参考，计算TDOA₁₂和TDOA₁₃</p>
            </div>
            <div class="step">
                <h4>双曲线方程</h4>
                <p>根据时间差构建双曲线方程组</p>
            </div>
            <div class="step">
                <h4>交点求解</h4>
                <p>求解双曲线交点，确定缺陷位置坐标</p>
            </div>
            <div class="step">
                <h4>结果输出</h4>
                <p>输出缺陷位置坐标和定位精度评估</p>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎨 动画特色</h3>
                <ul>
                    <li><strong>声波传播</strong>：动态显示声波从缺陷向传感器传播</li>
                    <li><strong>时间差可视化</strong>：实时显示信号到达时间差</li>
                    <li><strong>双曲线显示</strong>：展示TDOA等值线</li>
                    <li><strong>定位误差</strong>：实时计算和显示定位精度</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🔍 应用场景</h3>
                <ul>
                    <li><strong>风机叶片</strong>：内腔缺陷检测定位</li>
                    <li><strong>压力容器</strong>：裂纹扩展监测</li>
                    <li><strong>桥梁结构</strong>：疲劳损伤定位</li>
                    <li><strong>航空航天</strong>：复合材料缺陷检测</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>📈 技术指标</h3>
                <ul>
                    <li><strong>定位范围</strong>：10m × 2m叶片区域</li>
                    <li><strong>响应时间</strong>：<10ms</li>
                    <li><strong>定位精度</strong>：±0.2m</li>
                    <li><strong>检测频率</strong>：20kHz-1MHz</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <h3>🚀 技术价值</h3>
            <p>TDOA定位技术为风机叶片内腔缺陷检测提供了精确的空间定位能力，结合声发射监测技术，可以实现缺陷的早期发现和精确定位，为设备维护和安全评估提供重要技术支撑。</p>
        </div>
    </div>
</body>
</html>