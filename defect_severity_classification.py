import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle, Rectangle
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DefectSeverityClassifier:
    """缺陷严重程度分类器"""
    
    def __init__(self):
        self.defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
        self.severity_levels = {
            1: {'name': '轻微', 'color': '#2ECC71', 'action': '继续监测', 'risk': '低风险'},
            2: {'name': '中等', 'color': '#F39C12', 'action': '加强监测', 'risk': '中风险'},
            3: {'name': '严重', 'color': '#E67E22', 'action': '计划维修', 'risk': '高风险'},
            4: {'name': '危险', 'color': '#E74C3C', 'action': '立即停机', 'risk': '极高风险'}
        }
        
    def generate_severity_data(self):
        """生成缺陷严重程度数据"""
        np.random.seed(42)
        
        severity_data = []
        
        for defect_type in self.defect_types:
            for severity in range(1, 5):
                # 为每种缺陷类型和严重程度生成特征
                if defect_type == '裂纹':
                    # 裂纹：长度、深度、扩展速度
                    length = self._generate_crack_length(severity)
                    depth = self._generate_crack_depth(severity)
                    growth_rate = self._generate_crack_growth_rate(severity)
                    signal_amplitude = self._generate_signal_amplitude(severity, 'crack')
                    
                elif defect_type == '纤维褶皱':
                    # 纤维褶皱：面积、高度、密度
                    length = self._generate_wrinkle_area(severity)
                    depth = self._generate_wrinkle_height(severity)
                    growth_rate = self._generate_wrinkle_density(severity)
                    signal_amplitude = self._generate_signal_amplitude(severity, 'wrinkle')
                    
                elif defect_type == '分层':
                    # 分层：面积、厚度、扩展范围
                    length = self._generate_delamination_area(severity)
                    depth = self._generate_delamination_thickness(severity)
                    growth_rate = self._generate_delamination_extent(severity)
                    signal_amplitude = self._generate_signal_amplitude(severity, 'delamination')
                    
                else:  # 鼓包
                    # 鼓包：直径、高度、变形程度
                    length = self._generate_bulge_diameter(severity)
                    depth = self._generate_bulge_height(severity)
                    growth_rate = self._generate_bulge_deformation(severity)
                    signal_amplitude = self._generate_signal_amplitude(severity, 'bulge')
                
                # 生成多个样本
                for sample in range(15):  # 每个严重程度15个样本
                    # 添加随机变化
                    noise_factor = 0.1
                    sample_data = {
                        'defect_type': defect_type,
                        'severity_level': severity,
                        'severity_name': self.severity_levels[severity]['name'],
                        'length_area': length * (1 + np.random.normal(0, noise_factor)),
                        'depth_height': depth * (1 + np.random.normal(0, noise_factor)),
                        'growth_density': growth_rate * (1 + np.random.normal(0, noise_factor)),
                        'signal_amplitude': signal_amplitude * (1 + np.random.normal(0, noise_factor)),
                        'risk_score': self._calculate_risk_score(severity, defect_type),
                        'maintenance_urgency': severity,
                        'color': self.severity_levels[severity]['color']
                    }
                    severity_data.append(sample_data)
        
        return pd.DataFrame(severity_data)
    
    def _generate_crack_length(self, severity):
        """生成裂纹长度 (mm)"""
        base_values = {1: 2, 2: 8, 3: 20, 4: 50}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_crack_depth(self, severity):
        """生成裂纹深度 (mm)"""
        base_values = {1: 0.5, 2: 2, 3: 5, 4: 12}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_crack_growth_rate(self, severity):
        """生成裂纹扩展速度 (mm/cycle)"""
        base_values = {1: 0.001, 2: 0.005, 3: 0.02, 4: 0.1}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.3)
    
    def _generate_wrinkle_area(self, severity):
        """生成褶皱面积 (cm²)"""
        base_values = {1: 5, 2: 20, 3: 80, 4: 200}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_wrinkle_height(self, severity):
        """生成褶皱高度 (mm)"""
        base_values = {1: 1, 2: 3, 3: 8, 4: 15}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_wrinkle_density(self, severity):
        """生成褶皱密度 (个/cm²)"""
        base_values = {1: 0.5, 2: 2, 3: 5, 4: 10}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_delamination_area(self, severity):
        """生成分层面积 (cm²)"""
        base_values = {1: 3, 2: 15, 3: 60, 4: 150}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_delamination_thickness(self, severity):
        """生成分层厚度 (mm)"""
        base_values = {1: 0.2, 2: 1, 3: 3, 4: 8}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_delamination_extent(self, severity):
        """生成分层扩展范围 (%)"""
        base_values = {1: 5, 2: 15, 3: 35, 4: 70}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.1)
    
    def _generate_bulge_diameter(self, severity):
        """生成鼓包直径 (cm)"""
        base_values = {1: 2, 2: 6, 3: 15, 4: 30}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_bulge_height(self, severity):
        """生成鼓包高度 (mm)"""
        base_values = {1: 1, 2: 4, 3: 10, 4: 25}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.2)
    
    def _generate_bulge_deformation(self, severity):
        """生成鼓包变形程度 (%)"""
        base_values = {1: 2, 2: 8, 3: 20, 4: 45}
        return base_values[severity] + np.random.normal(0, base_values[severity] * 0.1)
    
    def _generate_signal_amplitude(self, severity, defect_type):
        """生成信号幅值"""
        base_multipliers = {
            'crack': {1: 1.2, 2: 2.5, 3: 4.8, 4: 8.5},
            'wrinkle': {1: 0.8, 2: 1.8, 3: 3.2, 4: 5.5},
            'delamination': {1: 1.0, 2: 2.2, 3: 4.0, 4: 7.0},
            'bulge': {1: 1.5, 2: 3.0, 3: 5.5, 4: 9.0}
        }
        base_value = base_multipliers[defect_type][severity]
        return base_value + np.random.normal(0, base_value * 0.15)
    
    def _calculate_risk_score(self, severity, defect_type):
        """计算风险评分 (0-100)"""
        base_scores = {1: 15, 2: 35, 3: 65, 4: 90}
        
        # 不同缺陷类型的风险系数
        risk_factors = {
            '裂纹': 1.2,      # 裂纹风险最高
            '分层': 1.1,      # 分层风险较高
            '鼓包': 1.0,      # 鼓包风险中等
            '纤维褶皱': 0.9   # 纤维褶皱风险相对较低
        }
        
        risk_score = base_scores[severity] * risk_factors[defect_type]
        return min(100, max(0, risk_score + np.random.normal(0, 5)))
    
    def create_severity_classification_chart(self, df):
        """创建严重程度分类图表"""

        fig = plt.figure(figsize=(18, 14))

        # 主标题
        fig.suptitle('缺陷严重程度分级标注系统', fontsize=22, fontweight='bold', y=0.95)
        
        # 1. 严重程度分级总览 (左上)
        ax1 = plt.subplot(3, 3, 1)
        severity_counts = df['severity_level'].value_counts().sort_index()
        colors = [self.severity_levels[level]['color'] for level in severity_counts.index]
        labels = [f"等级{level}\n{self.severity_levels[level]['name']}" for level in severity_counts.index]
        
        wedges, texts, autotexts = ax1.pie(severity_counts.values, labels=labels, autopct='%1.1f%%',
                                          colors=colors, startangle=90, textprops={'fontsize': 10})
        ax1.set_title('严重程度分级分布', fontsize=14, fontweight='bold')
        
        # 2. 缺陷类型 vs 严重程度热力图 (右上)
        ax2 = plt.subplot(3, 3, 2)
        severity_matrix = df.pivot_table(values='risk_score', index='defect_type', 
                                        columns='severity_level', aggfunc='mean')
        
        sns.heatmap(severity_matrix, annot=True, fmt='.1f', cmap='Reds', ax=ax2,
                   cbar_kws={'label': '风险评分'})
        ax2.set_title('缺陷类型-严重程度风险矩阵', fontsize=14, fontweight='bold')
        ax2.set_xlabel('严重程度等级')
        ax2.set_ylabel('缺陷类型')
        
        # 3. 信号幅值 vs 严重程度 (右中)
        ax3 = plt.subplot(3, 3, 3)
        for defect_type in self.defect_types:
            defect_data = df[df['defect_type'] == defect_type]
            ax3.scatter(defect_data['severity_level'], defect_data['signal_amplitude'], 
                       label=defect_type, alpha=0.7, s=50)
        
        ax3.set_title('信号幅值与严重程度关系', fontsize=14, fontweight='bold')
        ax3.set_xlabel('严重程度等级')
        ax3.set_ylabel('信号幅值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 各缺陷类型严重程度分布 (下排)
        for i, defect_type in enumerate(self.defect_types):
            ax = plt.subplot(3, 4, 9 + i)
            defect_data = df[df['defect_type'] == defect_type]
            
            severity_dist = defect_data['severity_level'].value_counts().sort_index()
            colors = [self.severity_levels[level]['color'] for level in severity_dist.index]
            
            bars = ax.bar(severity_dist.index, severity_dist.values, color=colors, alpha=0.8)
            ax.set_title(f'{defect_type}严重程度分布', fontsize=12, fontweight='bold')
            ax.set_xlabel('严重程度等级')
            ax.set_ylabel('样本数量')
            ax.set_xticks(range(1, 5))
            
            # 添加数值标签
            for bar, value in zip(bars, severity_dist.values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{value}', ha='center', va='bottom', fontsize=10)
        
        # 5. 风险评分分布 (左中)
        ax5 = plt.subplot(3, 3, 4)
        for severity in range(1, 5):
            severity_data = df[df['severity_level'] == severity]
            color = self.severity_levels[severity]['color']
            label = f"等级{severity} ({self.severity_levels[severity]['name']})"
            
            ax5.hist(severity_data['risk_score'], bins=15, alpha=0.7, 
                    color=color, label=label, density=True)
        
        ax5.set_title('风险评分分布', fontsize=14, fontweight='bold')
        ax5.set_xlabel('风险评分')
        ax5.set_ylabel('密度')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. 维修紧急程度矩阵 (中中)
        ax6 = plt.subplot(3, 3, 5)
        urgency_matrix = df.pivot_table(values='maintenance_urgency', index='defect_type', 
                                       columns='severity_level', aggfunc='mean')
        
        sns.heatmap(urgency_matrix, annot=True, fmt='.1f', cmap='YlOrRd', ax=ax6,
                   cbar_kws={'label': '维修紧急度'})
        ax6.set_title('维修紧急程度矩阵', fontsize=14, fontweight='bold')
        ax6.set_xlabel('严重程度等级')
        ax6.set_ylabel('缺陷类型')
        
        # 7. 缺陷特征对比雷达图 (左下)
        ax7 = plt.subplot(3, 3, 7, projection='polar')
        
        # 选择一个代表性样本进行雷达图展示
        features = ['length_area', 'depth_height', 'growth_density', 'signal_amplitude', 'risk_score']
        feature_labels = ['尺寸/面积', '深度/高度', '扩展/密度', '信号幅值', '风险评分']
        
        angles = np.linspace(0, 2 * np.pi, len(features), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for severity in range(1, 5):
            severity_data = df[df['severity_level'] == severity]
            values = []
            for feature in features:
                # 归一化到0-1范围
                feature_values = df[feature]
                normalized_value = (severity_data[feature].mean() - feature_values.min()) / (feature_values.max() - feature_values.min())
                values.append(normalized_value)
            
            values += values[:1]  # 闭合图形
            
            color = self.severity_levels[severity]['color']
            label = f"等级{severity}"
            ax7.plot(angles, values, 'o-', linewidth=2, label=label, color=color)
            ax7.fill(angles, values, alpha=0.25, color=color)
        
        ax7.set_xticks(angles[:-1])
        ax7.set_xticklabels(feature_labels)
        ax7.set_title('严重程度特征对比雷达图', fontsize=14, fontweight='bold', pad=20)
        ax7.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        # 8. 严重程度判定流程图 (中下)
        ax8 = plt.subplot(3, 3, 8)
        ax8.axis('off')
        
        # 创建判定流程
        flow_text = """
        严重程度判定标准
        
        等级1 (轻微) - 绿色
        • 裂纹长度 < 5mm
        • 信号幅值 < 2.0
        • 风险评分 < 25
        → 继续监测
        
        等级2 (中等) - 橙色  
        • 裂纹长度 5-15mm
        • 信号幅值 2.0-4.0
        • 风险评分 25-50
        → 加强监测
        
        等级3 (严重) - 深橙色
        • 裂纹长度 15-35mm  
        • 信号幅值 4.0-7.0
        • 风险评分 50-80
        → 计划维修
        
        等级4 (危险) - 红色
        • 裂纹长度 > 35mm
        • 信号幅值 > 7.0  
        • 风险评分 > 80
        → 立即停机
        """
        
        ax8.text(0.1, 0.9, flow_text, fontsize=11, ha='left', va='top', 
                transform=ax8.transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
        
        plt.subplots_adjust(top=0.92, hspace=0.5, wspace=0.5)
        plt.savefig('defect_severity_classification_chart.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 缺陷严重程度分类图表已生成: defect_severity_classification_chart.png")
    
    def create_severity_level_visualization(self, df):
        """创建严重程度等级可视化"""
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        fig.suptitle('缺陷严重程度等级标注可视化', fontsize=20, fontweight='bold')
        
        # 为每个严重程度等级创建详细展示
        for severity in range(1, 5):
            ax = axes[severity - 1]
            severity_data = df[df['severity_level'] == severity]
            
            # 创建散点图显示该等级的所有样本
            defect_colors = {'裂纹': 'red', '纤维褶皱': 'gold', '分层': 'blue', '鼓包': 'green'}
            
            for defect_type in self.defect_types:
                defect_samples = severity_data[severity_data['defect_type'] == defect_type]
                if len(defect_samples) > 0:
                    ax.scatter(defect_samples['length_area'], defect_samples['signal_amplitude'],
                             c=defect_colors[defect_type], label=defect_type, alpha=0.7, s=60)
            
            # 设置背景色
            ax.set_facecolor(self.severity_levels[severity]['color'])
            ax.patch.set_alpha(0.1)
            
            severity_info = self.severity_levels[severity]
            ax.set_title(f'等级{severity}: {severity_info["name"]} - {severity_info["action"]}', 
                        fontsize=14, fontweight='bold', 
                        color=severity_info['color'])
            ax.set_xlabel('缺陷尺寸/面积')
            ax.set_ylabel('信号幅值')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 添加风险等级标识
            risk_text = f"风险等级: {severity_info['risk']}\n平均风险评分: {severity_data['risk_score'].mean():.1f}"
            ax.text(0.02, 0.98, risk_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3", 
                   facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.92)
        plt.savefig('severity_level_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 严重程度等级可视化已生成: severity_level_visualization.png")
    
    def create_maintenance_decision_matrix(self, df):
        """创建维修决策矩阵"""
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        
        # 创建决策矩阵
        decision_data = []
        
        for _, row in df.iterrows():
            severity = row['severity_level']
            defect_type = row['defect_type']
            risk_score = row['risk_score']
            
            # 根据严重程度和缺陷类型确定维修策略
            if severity == 1:
                strategy = '定期检查'
                urgency = 1
            elif severity == 2:
                strategy = '加强监测'
                urgency = 2
            elif severity == 3:
                strategy = '计划维修'
                urgency = 3
            else:
                strategy = '紧急维修'
                urgency = 4
            
            decision_data.append({
                'defect_type': defect_type,
                'severity': severity,
                'risk_score': risk_score,
                'strategy': strategy,
                'urgency': urgency
            })
        
        decision_df = pd.DataFrame(decision_data)
        
        # 创建维修决策热力图
        decision_matrix = decision_df.pivot_table(values='urgency', index='defect_type', 
                                                 columns='severity', aggfunc='mean')
        
        # 自定义颜色映射
        colors = ['#2ECC71', '#F39C12', '#E67E22', '#E74C3C']
        cmap = LinearSegmentedColormap.from_list('severity', colors, N=4)
        
        sns.heatmap(decision_matrix, annot=True, fmt='.1f', cmap=cmap, ax=ax,
                   cbar_kws={'label': '维修紧急度'}, linewidths=1, linecolor='white')
        
        ax.set_title('缺陷维修决策矩阵', fontsize=18, fontweight='bold', pad=20)
        ax.set_xlabel('严重程度等级', fontsize=14)
        ax.set_ylabel('缺陷类型', fontsize=14)
        
        # 添加维修策略说明
        strategy_text = """
        维修策略说明:
        
        🟢 等级1 (轻微): 定期检查，3个月复查
        🟡 等级2 (中等): 加强监测，1个月复查  
        🟠 等级3 (严重): 计划维修，2周内安排
        🔴 等级4 (危险): 紧急维修，立即停机
        """
        
        ax.text(1.15, 0.5, strategy_text, transform=ax.transAxes, fontsize=12,
               verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", 
               facecolor='lightgray', alpha=0.9))
        
        plt.tight_layout()
        plt.savefig('maintenance_decision_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 维修决策矩阵已生成: maintenance_decision_matrix.png")


def main():
    """主函数"""
    print("🎨 开始创建缺陷严重程度分级标注可视化...")
    
    # 创建分类器
    classifier = DefectSeverityClassifier()
    
    # 1. 生成严重程度数据
    print("1. 生成缺陷严重程度数据...")
    df = classifier.generate_severity_data()
    print(f"   生成 {len(df)} 个标注样本")
    
    # 2. 创建严重程度分类图表
    print("2. 创建严重程度分类图表...")
    classifier.create_severity_classification_chart(df)
    
    # 3. 创建严重程度等级可视化
    print("3. 创建严重程度等级可视化...")
    classifier.create_severity_level_visualization(df)
    
    # 4. 创建维修决策矩阵
    print("4. 创建维修决策矩阵...")
    classifier.create_maintenance_decision_matrix(df)
    
    # 5. 保存数据到CSV
    print("5. 保存标注数据...")
    df.to_csv('defect_severity_annotations.csv', index=False, encoding='utf-8-sig')
    
    print("\n🎉 缺陷严重程度标注可视化完成！")
    print("生成的文件:")
    print("• defect_severity_classification_chart.png - 严重程度分类图表")
    print("• severity_level_visualization.png - 严重程度等级可视化")
    print("• maintenance_decision_matrix.png - 维修决策矩阵")
    print("• defect_severity_annotations.csv - 标注数据文件")
    
    # 打印统计信息
    print("\n📊 标注数据统计:")
    print(f"总样本数: {len(df)}")
    print("严重程度分布:")
    for severity in range(1, 5):
        count = len(df[df['severity_level'] == severity])
        name = classifier.severity_levels[severity]['name']
        print(f"  等级{severity} ({name}): {count} 个样本")
    
    print("\n缺陷类型分布:")
    for defect_type in classifier.defect_types:
        count = len(df[df['defect_type'] == defect_type])
        print(f"  {defect_type}: {count} 个样本")


if __name__ == "__main__":
    main()
