import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle
import pandas as pd
import sqlite3
import json
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FrequencyFeatureExtractor:
    """频域特征提取器"""
    
    def __init__(self, fs=1000):
        self.fs = fs
        self.defect_types = ['裂纹', '纤维褶皱', '分层', '鼓包']
        self.colors = ['red', 'gold', 'blue', 'green']
        
    def generate_sample_signals(self):
        """生成示例缺陷信号"""
        t = np.linspace(0, 2, 2000)
        signals = {}
        
        # 裂纹信号：高频冲击
        crack_signal = 0.1 * np.sin(2 * np.pi * 50 * t)
        for _ in range(3):
            impact_time = np.random.uniform(0.2, 1.8)
            impact_idx = int(impact_time * 1000)
            if impact_idx < len(crack_signal) - 50:
                crack_signal[impact_idx:impact_idx+50] += 2 * np.exp(-np.arange(50) * 0.1) * np.sin(2 * np.pi * 300 * np.arange(50) / 1000)
        crack_signal += 0.05 * np.random.randn(len(crack_signal))
        signals['裂纹'] = crack_signal
        
        # 纤维褶皱信号：中频周期性
        fiber_signal = 0.8 * np.sin(2 * np.pi * 100 * t) + 0.4 * np.sin(2 * np.pi * 180 * t)
        fiber_signal *= (1 + 0.3 * np.sin(2 * np.pi * 10 * t))
        fiber_signal += 0.08 * np.random.randn(len(fiber_signal))
        signals['纤维褶皱'] = fiber_signal
        
        # 分层信号：低频 + 间歇脉冲
        delamination_signal = 1.2 * np.sin(2 * np.pi * 30 * t)
        for _ in range(2):
            pulse_time = np.random.uniform(0.3, 1.7)
            pulse_idx = int(pulse_time * 1000)
            if pulse_idx < len(delamination_signal) - 100:
                delamination_signal[pulse_idx:pulse_idx+100] += 1.5 * np.exp(-np.arange(100) * 0.05) * np.sin(2 * np.pi * 80 * np.arange(100) / 1000)
        delamination_signal += 0.06 * np.random.randn(len(delamination_signal))
        signals['分层'] = delamination_signal
        
        # 鼓包信号：低频大幅度
        bulge_signal = 1.5 * np.sin(2 * np.pi * 15 * t) + 0.8 * np.sin(2 * np.pi * 40 * t)
        bulge_signal *= (1 + 0.4 * np.sin(2 * np.pi * 2 * t))
        bulge_signal += 0.04 * np.random.randn(len(bulge_signal))
        signals['鼓包'] = bulge_signal
        
        return signals, t
    
    def extract_frequency_features(self, signal):
        """提取频域特征"""
        # FFT变换
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/self.fs)
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        magnitude = np.abs(fft_signal[:len(fft_signal)//2])
        power = magnitude**2
        
        # 计算频域特征
        features = {}
        
        # 基础统计特征
        features['频域均值'] = np.mean(magnitude)
        features['频域标准差'] = np.std(magnitude)
        features['频域最大值'] = np.max(magnitude)
        features['频域最小值'] = np.min(magnitude)
        features['频域峰峰值'] = np.max(magnitude) - np.min(magnitude)
        
        # 主频率特征
        dominant_freq_idx = np.argmax(magnitude)
        features['主频率'] = positive_freqs[dominant_freq_idx]
        features['主频幅值'] = magnitude[dominant_freq_idx]
        
        # 频谱重心
        total_power = np.sum(power)
        if total_power > 0:
            features['频谱重心'] = np.sum(positive_freqs * power) / total_power
        else:
            features['频谱重心'] = 0
        
        # 频谱方差
        if total_power > 0:
            features['频谱方差'] = np.sum((positive_freqs - features['频谱重心'])**2 * power) / total_power
        else:
            features['频谱方差'] = 0
        
        # 频带能量分布
        low_freq_mask = positive_freqs <= 50
        mid_freq_mask = (positive_freqs > 50) & (positive_freqs <= 200)
        high_freq_mask = positive_freqs > 200
        
        features['低频能量'] = np.sum(power[low_freq_mask])
        features['中频能量'] = np.sum(power[mid_freq_mask])
        features['高频能量'] = np.sum(power[high_freq_mask])
        
        if total_power > 0:
            features['低频能量比'] = features['低频能量'] / total_power
            features['中频能量比'] = features['中频能量'] / total_power
            features['高频能量比'] = features['高频能量'] / total_power
        else:
            features['低频能量比'] = features['中频能量比'] = features['高频能量比'] = 0
        
        # 频谱平坦度
        geometric_mean = np.exp(np.mean(np.log(magnitude + 1e-10)))
        arithmetic_mean = np.mean(magnitude)
        if arithmetic_mean > 0:
            features['频谱平坦度'] = geometric_mean / arithmetic_mean
        else:
            features['频谱平坦度'] = 0
        
        # 频谱滚降点
        cumulative_power = np.cumsum(power)
        rolloff_threshold = 0.85 * total_power
        rolloff_idx = np.where(cumulative_power >= rolloff_threshold)[0]
        if len(rolloff_idx) > 0:
            features['频谱滚降点'] = positive_freqs[rolloff_idx[0]]
        else:
            features['频谱滚降点'] = positive_freqs[-1]
        
        # 有效带宽
        features['有效带宽'] = features['频谱滚降点'] - positive_freqs[0]
        
        return features, magnitude, positive_freqs
    
    def create_feature_extraction_visualization(self):
        """创建频域特征提取可视化"""
        
        # 生成示例信号
        signals, t = self.generate_sample_signals()
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        
        # 主标题
        fig.suptitle('缺陷信号频域特征提取与数据库构建过程', fontsize=24, fontweight='bold', y=0.95)
        
        # 为每种缺陷类型创建子图
        for i, (defect_type, color) in enumerate(zip(self.defect_types, self.colors)):
            signal = signals[defect_type]
            
            # 提取频域特征
            features, magnitude, freqs = self.extract_frequency_features(signal)
            
            # 时域信号子图
            ax_time = plt.subplot(4, 4, i*4 + 1)
            ax_time.plot(t, signal, color=color, linewidth=1.5)
            ax_time.set_title(f'{defect_type}缺陷时域信号', fontsize=12, fontweight='bold', color=color)
            ax_time.set_xlabel('时间 (s)', fontsize=10)
            ax_time.set_ylabel('幅值', fontsize=10)
            ax_time.grid(True, alpha=0.3)
            ax_time.set_xlim(0, 2)
            
            # 频域信号子图
            ax_freq = plt.subplot(4, 4, i*4 + 2)
            ax_freq.plot(freqs, magnitude, color=color, linewidth=1.5)
            ax_freq.set_title(f'{defect_type}频域信号', fontsize=12, fontweight='bold', color=color)
            ax_freq.set_xlabel('频率 (Hz)', fontsize=10)
            ax_freq.set_ylabel('幅值', fontsize=10)
            ax_freq.grid(True, alpha=0.3)
            ax_freq.set_xlim(0, 250)
            
            # 频带能量分布
            ax_energy = plt.subplot(4, 4, i*4 + 3)
            energy_bands = ['低频\n(0-50Hz)', '中频\n(50-200Hz)', '高频\n(>200Hz)']
            energy_values = [features['低频能量比'], features['中频能量比'], features['高频能量比']]
            bars = ax_energy.bar(energy_bands, energy_values, color=[color]*3, alpha=0.7)
            ax_energy.set_title(f'{defect_type}频带能量分布', fontsize=12, fontweight='bold', color=color)
            ax_energy.set_ylabel('能量比', fontsize=10)
            ax_energy.set_ylim(0, 1)
            
            # 在柱状图上添加数值
            for bar, value in zip(bars, energy_values):
                height = bar.get_height()
                ax_energy.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                              f'{value:.3f}', ha='center', va='bottom', fontsize=9)
            
            # 特征表格
            ax_table = plt.subplot(4, 4, i*4 + 4)
            ax_table.axis('off')
            
            # 选择关键特征显示
            key_features = {
                '主频率': f"{features['主频率']:.1f} Hz",
                '频谱重心': f"{features['频谱重心']:.1f} Hz",
                '频域最大值': f"{features['频域最大值']:.2f}",
                '频谱平坦度': f"{features['频谱平坦度']:.3f}",
                '有效带宽': f"{features['有效带宽']:.1f} Hz",
                '低频能量比': f"{features['低频能量比']:.3f}",
                '中频能量比': f"{features['中频能量比']:.3f}",
                '高频能量比': f"{features['高频能量比']:.3f}"
            }
            
            # 创建表格数据
            table_data = []
            for feature_name, feature_value in key_features.items():
                table_data.append([feature_name, feature_value])
            
            table = ax_table.table(cellText=table_data,
                                 colLabels=['特征名称', '特征值'],
                                 cellLoc='left',
                                 loc='center',
                                 colWidths=[0.6, 0.4])
            
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.5)
            
            # 设置表格样式
            for (i_row, j_col), cell in table.get_celld().items():
                if i_row == 0:  # 表头
                    cell.set_text_props(weight='bold', color='white')
                    cell.set_facecolor(color)
                else:
                    cell.set_facecolor('#f8f9fa' if i_row % 2 == 0 else 'white')
                cell.set_edgecolor('gray')
                cell.set_linewidth(0.5)
            
            ax_table.set_title(f'{defect_type}频域特征', fontsize=12, fontweight='bold', color=color)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.92, hspace=0.4, wspace=0.3)
        plt.savefig('frequency_feature_extraction_process.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 频域特征提取过程图已生成: frequency_feature_extraction_process.png")
        
        return signals
    
    def create_database_construction_flow(self):
        """创建数据库构建流程图"""
        
        fig, ax = plt.subplots(1, 1, figsize=(18, 12))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')
        
        # 标题
        ax.text(5, 9.5, '频域特征数据库构建流程', fontsize=22, fontweight='bold', ha='center')
        
        # 流程步骤
        steps = [
            ('原始缺陷信号', 1, 8, '#3498DB', '时域信号\n多种缺陷类型'),
            ('FFT变换', 3, 8, '#E74C3C', '频域转换\n幅值谱计算'),
            ('特征提取', 5, 8, '#F39C12', '18维频域特征\n统计+能量+形状'),
            ('特征向量', 7, 8, '#2ECC71', '标准化特征\n归一化处理'),
            ('数据库存储', 9, 8, '#9B59B6', 'SQLite数据库\n结构化存储')
        ]
        
        # 绘制流程步骤
        for step_name, x, y, color, description in steps:
            # 主框
            step_box = FancyBboxPatch((x-0.8, y-0.6), 1.6, 1.2, boxstyle="round,pad=0.1", 
                                     facecolor=color, alpha=0.8, edgecolor='black', linewidth=2)
            ax.add_patch(step_box)
            
            # 步骤名称
            ax.text(x, y+0.2, step_name, fontsize=12, fontweight='bold', ha='center', color='white')
            
            # 描述
            ax.text(x, y-0.2, description, fontsize=10, ha='center', color='white')
        
        # 绘制箭头
        arrow_props = dict(arrowstyle='->', lw=3, color='black')
        for i in range(len(steps)-1):
            x_start = steps[i][1] + 0.8
            x_end = steps[i+1][1] - 0.8
            y_pos = steps[i][2]
            ax.annotate('', xy=(x_end, y_pos), xytext=(x_start, y_pos), arrowprops=arrow_props)
        
        # 详细说明区域
        detail_y = 6
        ax.text(5, detail_y, '详细处理步骤', fontsize=18, fontweight='bold', ha='center')
        
        # 处理步骤详情
        process_details = [
            "1. 信号预处理：去直流、滤波、归一化",
            "2. FFT变换：计算频域幅值谱和功率谱",
            "3. 统计特征：均值、标准差、最大值、最小值等",
            "4. 频率特征：主频率、频谱重心、频谱方差",
            "5. 能量特征：低频、中频、高频能量分布",
            "6. 形状特征：频谱平坦度、滚降点、带宽",
            "7. 特征标准化：Z-score标准化处理",
            "8. 数据库存储：结构化存储，建立索引"
        ]
        
        for i, detail in enumerate(process_details):
            y_pos = detail_y - 0.5 - i * 0.4
            ax.text(0.5, y_pos, detail, fontsize=12, ha='left', va='center')
        
        # 数据库结构示意
        db_box = FancyBboxPatch((0.5, 0.5), 9, 2, boxstyle="round,pad=0.2", 
                               facecolor='lightgray', alpha=0.3, edgecolor='black')
        ax.add_patch(db_box)
        
        ax.text(5, 2.2, '频域特征数据库结构', fontsize=16, fontweight='bold', ha='center')
        
        db_structure = """
        📊 数据表结构：
        • signal_id (主键)          • 频域均值, 标准差, 最大值, 最小值     • 低频能量比, 中频能量比, 高频能量比
        • defect_type (缺陷类型)    • 主频率, 频谱重心, 频谱方差          • 频谱平坦度, 滚降点, 有效带宽
        • sampling_rate (采样率)    • 频域峰峰值, 主频幅值               • 创建时间, 更新时间
        """
        
        ax.text(5, 1.3, db_structure, fontsize=11, ha='center', va='center')
        
        plt.tight_layout()
        plt.savefig('frequency_database_construction_flow.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 数据库构建流程图已生成: frequency_database_construction_flow.png")
    
    def create_feature_database(self, signals):
        """创建频域特征数据库"""
        
        # 创建数据库连接
        conn = sqlite3.connect('frequency_features_database.db')
        cursor = conn.cursor()
        
        # 创建频域特征表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS frequency_features (
                signal_id TEXT PRIMARY KEY,
                defect_type TEXT,
                sampling_rate REAL,
                频域均值 REAL,
                频域标准差 REAL,
                频域最大值 REAL,
                频域最小值 REAL,
                频域峰峰值 REAL,
                主频率 REAL,
                主频幅值 REAL,
                频谱重心 REAL,
                频谱方差 REAL,
                低频能量 REAL,
                中频能量 REAL,
                高频能量 REAL,
                低频能量比 REAL,
                中频能量比 REAL,
                高频能量比 REAL,
                频谱平坦度 REAL,
                频谱滚降点 REAL,
                有效带宽 REAL,
                created_time TEXT
            )
        ''')
        
        # 为每种缺陷类型生成多个样本并存储
        all_features = []
        
        for defect_type in self.defect_types:
            for sample_id in range(50):  # 每种缺陷生成50个样本
                # 生成信号变体
                base_signal = signals[defect_type]
                # 添加随机变化
                noise_level = np.random.uniform(0.02, 0.08)
                signal_variant = base_signal + noise_level * np.random.randn(len(base_signal))
                
                # 提取特征
                features, _, _ = self.extract_frequency_features(signal_variant)
                
                # 准备数据库记录
                signal_id = f"{defect_type}_{sample_id:03d}"
                record = [
                    signal_id,
                    defect_type,
                    self.fs,
                    features['频域均值'],
                    features['频域标准差'],
                    features['频域最大值'],
                    features['频域最小值'],
                    features['频域峰峰值'],
                    features['主频率'],
                    features['主频幅值'],
                    features['频谱重心'],
                    features['频谱方差'],
                    features['低频能量'],
                    features['中频能量'],
                    features['高频能量'],
                    features['低频能量比'],
                    features['中频能量比'],
                    features['高频能量比'],
                    features['频谱平坦度'],
                    features['频谱滚降点'],
                    features['有效带宽'],
                    datetime.now().isoformat()
                ]
                
                # 插入数据库
                cursor.execute('''
                    INSERT OR REPLACE INTO frequency_features VALUES 
                    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', record)
                
                all_features.append(record)
        
        conn.commit()
        
        # 获取数据库统计信息
        cursor.execute('SELECT COUNT(*) FROM frequency_features')
        total_records = cursor.fetchone()[0]
        
        cursor.execute('SELECT defect_type, COUNT(*) FROM frequency_features GROUP BY defect_type')
        defect_distribution = cursor.fetchall()
        
        conn.close()
        
        print(f"✅ 频域特征数据库已创建: frequency_features_database.db")
        print(f"📊 总记录数: {total_records}")
        print("🏷️ 缺陷类型分布:")
        for defect_type, count in defect_distribution:
            print(f"   {defect_type}: {count} 个样本")
        
        return all_features
    
    def create_database_visualization(self):
        """创建数据库可视化"""
        
        # 读取数据库数据
        conn = sqlite3.connect('frequency_features_database.db')
        df = pd.read_sql_query('SELECT * FROM frequency_features', conn)
        conn.close()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        fig.suptitle('频域特征数据库可视化分析', fontsize=20, fontweight='bold')
        
        # 1. 缺陷类型分布
        defect_counts = df['defect_type'].value_counts()
        axes[0].pie(defect_counts.values, labels=defect_counts.index, autopct='%1.1f%%',
                   colors=self.colors, startangle=90)
        axes[0].set_title('缺陷类型分布', fontsize=14, fontweight='bold')
        
        # 2. 主频率分布
        for i, (defect_type, color) in enumerate(zip(self.defect_types, self.colors)):
            defect_data = df[df['defect_type'] == defect_type]
            axes[1].hist(defect_data['主频率'], bins=20, alpha=0.7, label=defect_type, color=color)
        axes[1].set_title('主频率分布', fontsize=14, fontweight='bold')
        axes[1].set_xlabel('主频率 (Hz)')
        axes[1].set_ylabel('频次')
        axes[1].legend()
        
        # 3. 频谱重心分布
        for i, (defect_type, color) in enumerate(zip(self.defect_types, self.colors)):
            defect_data = df[df['defect_type'] == defect_type]
            axes[2].hist(defect_data['频谱重心'], bins=20, alpha=0.7, label=defect_type, color=color)
        axes[2].set_title('频谱重心分布', fontsize=14, fontweight='bold')
        axes[2].set_xlabel('频谱重心 (Hz)')
        axes[2].set_ylabel('频次')
        axes[2].legend()
        
        # 4. 能量分布对比
        energy_features = ['低频能量比', '中频能量比', '高频能量比']
        x = np.arange(len(energy_features))
        width = 0.2
        
        for i, (defect_type, color) in enumerate(zip(self.defect_types, self.colors)):
            defect_data = df[df['defect_type'] == defect_type]
            energy_means = [defect_data[feature].mean() for feature in energy_features]
            axes[3].bar(x + i*width, energy_means, width, label=defect_type, color=color, alpha=0.8)
        
        axes[3].set_title('各缺陷类型能量分布对比', fontsize=14, fontweight='bold')
        axes[3].set_xlabel('频带')
        axes[3].set_ylabel('平均能量比')
        axes[3].set_xticks(x + width * 1.5)
        axes[3].set_xticklabels(energy_features)
        axes[3].legend()
        
        # 5. 特征相关性热图
        feature_cols = ['频域均值', '频域标准差', '主频率', '频谱重心', '低频能量比', '中频能量比', '高频能量比']
        correlation_matrix = df[feature_cols].corr()
        
        im = axes[4].imshow(correlation_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
        axes[4].set_title('特征相关性矩阵', fontsize=14, fontweight='bold')
        axes[4].set_xticks(range(len(feature_cols)))
        axes[4].set_yticks(range(len(feature_cols)))
        axes[4].set_xticklabels(feature_cols, rotation=45, ha='right')
        axes[4].set_yticklabels(feature_cols)
        
        # 添加相关系数文本
        for i in range(len(feature_cols)):
            for j in range(len(feature_cols)):
                text = axes[4].text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}',
                                   ha="center", va="center", color="black", fontsize=8)
        
        plt.colorbar(im, ax=axes[4])
        
        # 6. 数据库统计信息
        axes[5].axis('off')
        stats_text = f"""
        📊 数据库统计信息
        
        总样本数: {len(df):,} 个
        缺陷类型: {len(df['defect_type'].unique())} 种
        特征维度: {len(feature_cols)} 维
        
        各类型样本数:
        """
        
        for defect_type in self.defect_types:
            count = len(df[df['defect_type'] == defect_type])
            stats_text += f"\n• {defect_type}: {count} 个"
        
        stats_text += f"""
        
        特征范围:
        • 主频率: {df['主频率'].min():.1f} - {df['主频率'].max():.1f} Hz
        • 频谱重心: {df['频谱重心'].min():.1f} - {df['频谱重心'].max():.1f} Hz
        • 低频能量比: {df['低频能量比'].min():.3f} - {df['低频能量比'].max():.3f}
        """
        
        axes[5].text(0.1, 0.9, stats_text, fontsize=12, ha='left', va='top', 
                    transform=axes[5].transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig('frequency_database_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 数据库可视化图已生成: frequency_database_visualization.png")


def main():
    """主函数"""
    print("🎨 开始创建频域特征提取与数据库构建可视化...")
    
    # 创建特征提取器
    extractor = FrequencyFeatureExtractor()
    
    # 1. 创建特征提取过程可视化
    print("1. 生成特征提取过程图...")
    signals = extractor.create_feature_extraction_visualization()
    
    # 2. 创建数据库构建流程图
    print("2. 生成数据库构建流程图...")
    extractor.create_database_construction_flow()
    
    # 3. 创建并填充数据库
    print("3. 创建频域特征数据库...")
    features = extractor.create_feature_database(signals)
    
    # 4. 创建数据库可视化
    print("4. 生成数据库可视化分析图...")
    extractor.create_database_visualization()
    
    print("\n🎉 所有可视化图表生成完成！")
    print("生成的文件:")
    print("• frequency_feature_extraction_process.png - 特征提取过程图")
    print("• frequency_database_construction_flow.png - 数据库构建流程图")
    print("• frequency_database_visualization.png - 数据库可视化分析图")
    print("• frequency_features_database.db - 频域特征数据库")


if __name__ == "__main__":
    main()
