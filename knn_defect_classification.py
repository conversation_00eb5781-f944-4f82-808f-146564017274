import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
from matplotlib.patches import Ellipse
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 生成模拟缺陷信号数据
np.random.seed(42)

def generate_defect_data(n_samples=200):
    """生成四种缺陷类型的模拟信号数据"""
    
    # 纤维褶皱 - 特征：低频振动，幅度中等
    fiber_wrinkle = np.random.multivariate_normal(
        [2.5, 1.8], [[0.3, 0.1], [0.1, 0.2]], n_samples//4
    )
    
    # 裂纹 - 特征：高频振动，幅度较高
    crack = np.random.multivariate_normal(
        [4.2, 3.5], [[0.4, -0.1], [-0.1, 0.3]], n_samples//4
    )
    
    # 分层 - 特征：中频振动，幅度低
    delamination = np.random.multivariate_normal(
        [1.2, 3.8], [[0.2, 0.05], [0.05, 0.25]], n_samples//4
    )
    
    # 鼓包 - 特征：低频振动，幅度高
    bulge = np.random.multivariate_normal(
        [3.8, 1.5], [[0.35, 0.15], [0.15, 0.4]], n_samples//4
    )
    
    # 合并数据
    X = np.vstack([fiber_wrinkle, crack, delamination, bulge])
    y = np.array([0]*50 + [1]*50 + [2]*50 + [3]*50)
    
    return X, y

# 生成数据
X, y = generate_defect_data(200)

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.3, random_state=42, stratify=y
)

# 训练KNN模型
knn = KNeighborsClassifier(n_neighbors=5)
knn.fit(X_train, y_train)

# 预测
y_pred = knn.predict(X_test)

# 缺陷类型标签
defect_labels = ['纤维褶皱', '裂纹', '分层', '鼓包']
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

# 创建可视化图表
fig = plt.figure(figsize=(16, 12))

# 1. 原始数据分布图
ax1 = plt.subplot(2, 3, 1)
for i, (label, color) in enumerate(zip(defect_labels, colors)):
    mask = y == i
    plt.scatter(X[mask, 0], X[mask, 1], c=color, label=label, alpha=0.7, s=50)
plt.xlabel('特征1 (频率特征)')
plt.ylabel('特征2 (幅度特征)')
plt.title('原始缺陷信号数据分布')
plt.legend()
plt.grid(True, alpha=0.3)

# 2. KNN分类边界图
ax2 = plt.subplot(2, 3, 2)
h = 0.02
x_min, x_max = X_scaled[:, 0].min() - 1, X_scaled[:, 0].max() + 1
y_min, y_max = X_scaled[:, 1].min() - 1, X_scaled[:, 1].max() + 1
xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                     np.arange(y_min, y_max, h))

Z = knn.predict(np.c_[xx.ravel(), yy.ravel()])
Z = Z.reshape(xx.shape)

plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
for i, (label, color) in enumerate(zip(defect_labels, colors)):
    mask = y_train == i
    plt.scatter(X_train[mask, 0], X_train[mask, 1], c=color, label=label, alpha=0.8, s=50)

plt.xlabel('标准化特征1')
plt.ylabel('标准化特征2')
plt.title('KNN分类边界 (k=5)')
plt.legend()

# 3. 混淆矩阵
ax3 = plt.subplot(2, 3, 3)
cm = confusion_matrix(y_test, y_pred)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=defect_labels, yticklabels=defect_labels)
plt.title('混淆矩阵')
plt.xlabel('预测类别')
plt.ylabel('真实类别')

# 4. 分类准确率对比
ax4 = plt.subplot(2, 3, 4)
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, average='weighted')
recall = recall_score(y_test, y_pred, average='weighted')
f1 = f1_score(y_test, y_pred, average='weighted')

metrics = ['准确率', '精确率', '召回率', 'F1分数']
values = [accuracy, precision, recall, f1]

bars = plt.bar(metrics, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
plt.ylim(0, 1)
plt.title('KNN分类性能指标')
plt.ylabel('分数')

# 在柱状图上添加数值
for bar, value in zip(bars, values):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{value:.3f}', ha='center', va='bottom')

# 5. 不同k值的性能对比
ax5 = plt.subplot(2, 3, 5)
k_values = range(1, 21)
accuracies = []

for k in k_values:
    knn_temp = KNeighborsClassifier(n_neighbors=k)
    knn_temp.fit(X_train, y_train)
    y_pred_temp = knn_temp.predict(X_test)
    accuracies.append(accuracy_score(y_test, y_pred_temp))

plt.plot(k_values, accuracies, 'bo-', linewidth=2, markersize=6)
plt.xlabel('k值')
plt.ylabel('准确率')
plt.title('不同k值的分类准确率')
plt.grid(True, alpha=0.3)
plt.axvline(x=5, color='red', linestyle='--', alpha=0.7, label='选择的k=5')
plt.legend()

# 6. 各类别详细性能
ax6 = plt.subplot(2, 3, 6)
from sklearn.metrics import classification_report, precision_score, recall_score, f1_score

# 计算各类别的性能指标
precision_scores = precision_score(y_test, y_pred, average=None)
recall_scores = recall_score(y_test, y_pred, average=None)
f1_scores = f1_score(y_test, y_pred, average=None)

categories = defect_labels

x = np.arange(len(categories))
width = 0.25

plt.bar(x - width, precision_scores, width, label='精确率', color='#FF6B6B', alpha=0.8)
plt.bar(x, recall_scores, width, label='召回率', color='#4ECDC4', alpha=0.8)
plt.bar(x + width, f1_scores, width, label='F1分数', color='#45B7D1', alpha=0.8)

plt.xlabel('缺陷类型')
plt.ylabel('分数')
plt.title('各缺陷类型分类性能')
plt.xticks(x, categories, rotation=45)
plt.legend()
plt.ylim(0, 1.1)

plt.tight_layout()
plt.savefig('knn_defect_classification_results.png', dpi=300, bbox_inches='tight')
print("图表已保存为 knn_defect_classification_results.png")

# 打印详细分类报告
print("KNN缺陷信号分类详细报告:")
print("="*50)
print(f"总体准确率: {accuracy:.3f}")
print("\n各类别详细性能:")
print(classification_report(y_test, y_pred, target_names=defect_labels))

# 打印一些统计信息
print(f"\n数据集信息:")
print(f"总样本数: {len(X)}")
print(f"训练集样本数: {len(X_train)}")
print(f"测试集样本数: {len(X_test)}")
print(f"特征维度: {X.shape[1]}")
print(f"缺陷类型数: {len(defect_labels)}")
