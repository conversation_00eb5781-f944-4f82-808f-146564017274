<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风机叶片内腔缺陷频域特征数据集展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 1.1em;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .stat-item .value {
            font-size: 2em;
            font-weight: bold;
        }

        .features-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .features-table th,
        .features-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .features-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .features-table tr:hover {
            background-color: #f7fafc;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .accuracy-indicator {
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
        }

        .excellent { background-color: #48bb78; }
        .good { background-color: #38b2ac; }
        .fair { background-color: #ed8936; }

        .defect-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .defect-type {
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            color: white;
            font-weight: bold;
        }

        .defect-type.fiber { background: linear-gradient(135deg, #FF6B6B, #ff5252); }
        .defect-type.crack { background: linear-gradient(135deg, #4ECDC4, #26a69a); }
        .defect-type.delamination { background: linear-gradient(135deg, #45B7D1, #2196f3); }
        .defect-type.bulge { background: linear-gradient(135deg, #96CEB4, #66bb6a); }

        .download-section {
            text-align: center;
            margin-top: 30px;
        }

        .download-btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .download-btn:hover {
            transform: scale(1.05);
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 5px solid #ff6b6b;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌪️ 风机叶片内腔缺陷频域特征数据集</h1>
            <p>高精度频域特征数据集 - 专为风机叶片缺陷检测设计</p>
        </div>

        <div class="card">
            <h2>📊 数据集概览</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>总样本数</h3>
                    <div class="value">10,000</div>
                </div>
                <div class="stat-item">
                    <h3>特征数量</h3>
                    <div class="value">17</div>
                </div>
                <div class="stat-item">
                    <h3>缺陷类型</h3>
                    <div class="value">4</div>
                </div>
                <div class="stat-item">
                    <h3>数据平衡性</h3>
                    <div class="value">100%</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🎯 缺陷类型分布</h2>
            <div class="defect-types">
                <div class="defect-type fiber">
                    <h3>纤维褶皱</h3>
                    <p>2,500 样本 (25%)</p>
                </div>
                <div class="defect-type crack">
                    <h3>裂纹</h3>
                    <p>2,500 样本 (25%)</p>
                </div>
                <div class="defect-type delamination">
                    <h3>分层</h3>
                    <p>2,500 样本 (25%)</p>
                </div>
                <div class="defect-type bulge">
                    <h3>鼓包</h3>
                    <p>2,500 样本 (25%)</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📈 频域特征列表</h2>
            <table class="features-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>特征名称</th>
                        <th>特征描述</th>
                        <th>应用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>1</td><td>平均值</td><td>信号的统计均值</td><td>基础统计特征</td></tr>
                    <tr><td>2</td><td>方差</td><td>信号的离散程度</td><td>信号稳定性分析</td></tr>
                    <tr><td>3</td><td>均方值</td><td>信号功率特征</td><td>能量分析</td></tr>
                    <tr><td>4</td><td>均方根值</td><td>信号有效值</td><td>幅度特征</td></tr>
                    <tr><td>5</td><td>最大值</td><td>信号峰值</td><td>极值检测</td></tr>
                    <tr><td>6</td><td>最小值</td><td>信号谷值</td><td>极值检测</td></tr>
                    <tr><td>7</td><td>峰值</td><td>绝对峰值</td><td>冲击检测</td></tr>
                    <tr><td>8</td><td>峰峰值</td><td>信号动态范围</td><td>动态特征</td></tr>
                    <tr><td>9</td><td>平均绝对幅值</td><td>平均幅度</td><td>幅度统计</td></tr>
                    <tr><td>10</td><td>方根幅值</td><td>方根平均幅度</td><td>幅度特征</td></tr>
                    <tr><td>11</td><td>偏度</td><td>分布偏斜程度</td><td>分布形状</td></tr>
                    <tr><td>12</td><td>峭度</td><td>分布尖锐程度</td><td>分布形状</td></tr>
                    <tr><td>13</td><td>波形指标</td><td>波形特征</td><td>形状分析</td></tr>
                    <tr><td>14</td><td>峰值指标</td><td>峰值特征</td><td>冲击分析</td></tr>
                    <tr><td>15</td><td>脉冲指标</td><td>脉冲特征</td><td>冲击检测</td></tr>
                    <tr><td>16</td><td>裕度指标</td><td>裕度特征</td><td>安全分析</td></tr>
                    <tr><td>17</td><td>偏度指标</td><td>偏度特征</td><td>分布分析</td></tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>🎯 目标统计特征精度验证</h2>
            <div class="highlight">
                <strong>数据集生成精度极高！</strong> 所有主要统计特征均与目标值高度吻合，相对误差控制在6.5%以内。
            </div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特征名称</th>
                        <th>目标值</th>
                        <th>实际均值</th>
                        <th>相对误差(%)</th>
                        <th>精度评级</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>平均值</td>
                        <td>0.004535</td>
                        <td>0.004537</td>
                        <td>0.03</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>方差</td>
                        <td>0.133617</td>
                        <td>0.131834</td>
                        <td>1.33</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>均方值</td>
                        <td>0.017854</td>
                        <td>0.017861</td>
                        <td>0.04</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>均方根值</td>
                        <td>0.133617</td>
                        <td>0.131975</td>
                        <td>1.23</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>最大值</td>
                        <td>1.984645</td>
                        <td>1.984806</td>
                        <td>0.01</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>最小值</td>
                        <td>-1.717414</td>
                        <td>-1.718042</td>
                        <td>0.04</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>峰值</td>
                        <td>1.984645</td>
                        <td>2.060155</td>
                        <td>3.80</td>
                        <td><span class="accuracy-indicator good">良好</span></td>
                    </tr>
                    <tr>
                        <td>峰峰值</td>
                        <td>3.702059</td>
                        <td>3.704226</td>
                        <td>0.06</td>
                        <td><span class="accuracy-indicator excellent">优秀</span></td>
                    </tr>
                    <tr>
                        <td>平均绝对幅值</td>
                        <td>0.091779</td>
                        <td>0.097479</td>
                        <td>6.21</td>
                        <td><span class="accuracy-indicator good">良好</span></td>
                    </tr>
                    <tr>
                        <td>方根幅值</td>
                        <td>0.073718</td>
                        <td>0.078316</td>
                        <td>6.24</td>
                        <td><span class="accuracy-indicator good">良好</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>📊 数据可视化</h2>
            <div class="image-container">
                <img src="frequency_domain_dataset_visualization.png" alt="频域特征数据集可视化" />
                <p style="margin-top: 10px; color: #666;">数据集特征分布与统计分析可视化图表</p>
            </div>
        </div>

        <div class="card">
            <h2>💾 数据集下载</h2>
            <div class="download-section">
                <a href="frequency_domain_defects_10000.csv" class="download-btn" download>
                    📄 下载 CSV 格式 (10,000 样本)
                </a>
                <a href="frequency_domain_defects_10000.xlsx" class="download-btn" download>
                    📊 下载 Excel 格式 (含统计摘要)
                </a>
            </div>
            <div class="highlight" style="margin-top: 20px;">
                <strong>文件说明：</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>CSV文件：纯数据格式，适合机器学习和数据分析</li>
                    <li>Excel文件：包含数据和统计摘要，适合人工查看和分析</li>
                    <li>数据编码：UTF-8，支持中文字符</li>
                    <li>缺陷编码：0-纤维褶皱，1-裂纹，2-分层，3-鼓包</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>🔬 技术特点</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>高精度匹配</h3>
                    <p>统计特征与目标值精确匹配</p>
                </div>
                <div class="stat-item">
                    <h3>平衡分布</h3>
                    <p>四种缺陷类型样本均衡</p>
                </div>
                <div class="stat-item">
                    <h3>丰富特征</h3>
                    <p>17个频域统计特征</p>
                </div>
                <div class="stat-item">
                    <h3>实用性强</h3>
                    <p>适合机器学习训练</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
