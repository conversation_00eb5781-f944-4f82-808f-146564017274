import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

def generate_sample_data(n_samples=500):
    """
    生成模拟数据集
    包含3个类别的二维数据点
    """
    np.random.seed(42)
    
    # 类别1：圆形分布 (中心点: 2, 2)
    class1_x = np.random.normal(2, 0.8, n_samples//3)
    class1_y = np.random.normal(2, 0.8, n_samples//3)
    class1_labels = np.zeros(n_samples//3)
    
    # 类别2：椭圆形分布 (中心点: 6, 3)
    class2_x = np.random.normal(6, 1.2, n_samples//3)
    class2_y = np.random.normal(3, 0.6, n_samples//3)
    class2_labels = np.ones(n_samples//3)
    
    # 类别3：三角形分布 (中心点: 4, 6)
    class3_x = np.random.normal(4, 0.9, n_samples//3)
    class3_y = np.random.normal(6, 0.9, n_samples//3)
    class3_labels = np.full(n_samples//3, 2)
    
    # 合并所有数据
    X = np.column_stack([
        np.concatenate([class1_x, class2_x, class3_x]),
        np.concatenate([class1_y, class2_y, class3_y])
    ])
    
    y = np.concatenate([class1_labels, class2_labels, class3_labels])
    
    return X, y.astype(int)

def train_knn_model(X_train, y_train, k=5):
    """
    训练KNN模型
    """
    knn = KNeighborsClassifier(n_neighbors=k)
    knn.fit(X_train, y_train)
    return knn

def evaluate_model(model, X_test, y_test):
    """
    评估模型性能
    """
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"模型准确率: {accuracy:.4f}")
    print("\n详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['类别 0', '类别 1', '类别 2']))
    
    print("\n混淆矩阵:")
    cm = confusion_matrix(y_test, y_pred)
    print("      预测")
    print("     0  1  2")
    for i, row in enumerate(cm):
        print(f"真实 {i}  {row[0]:2d} {row[1]:2d} {row[2]:2d}")
    
    return y_pred

def find_best_k(X_train, y_train, X_test, y_test, k_range=range(1, 21)):
    """
    寻找最佳K值
    """
    best_k = 1
    best_accuracy = 0
    k_results = []
    
    print("\nK值性能比较:")
    print("K值  准确率")
    print("-" * 15)
    
    for k in k_range:
        knn = KNeighborsClassifier(n_neighbors=k)
        knn.fit(X_train, y_train)
        y_pred = knn.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        k_results.append((k, accuracy))
        
        print(f"{k:2d}   {accuracy:.4f}")
        
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            best_k = k
    
    print(f"\n最佳K值: {best_k}, 对应准确率: {best_accuracy:.4f}")
    return best_k, best_accuracy

def demonstrate_predictions(model, sample_points=None):
    """
    演示预测新数据点
    """
    if sample_points is None:
        sample_points = np.array([[3, 4], [1, 1], [5, 6], [7, 2], [2.5, 2.5]])
    
    predictions = model.predict(sample_points)
    probabilities = model.predict_proba(sample_points)
    
    print("\n新数据点预测演示:")
    print("数据点     预测类别  各类别概率")
    print("-" * 45)
    
    for i, (point, pred, prob) in enumerate(zip(sample_points, predictions, probabilities)):
        prob_str = " ".join([f"{p:.3f}" for p in prob])
        print(f"({point[0]:4.1f}, {point[1]:4.1f})    {pred}      [{prob_str}]")

def analyze_data_statistics(X, y):
    """
    分析数据统计信息
    """
    print("数据集统计信息:")
    print(f"总样本数: {len(X)}")
    print(f"特征维度: {X.shape[1]}")
    print(f"类别数量: {len(np.unique(y))}")
    
    print("\n各类别样本数量:")
    unique, counts = np.unique(y, return_counts=True)
    for cls, count in zip(unique, counts):
        print(f"类别 {cls}: {count} 个样本")
    
    print("\n特征统计:")
    print(f"特征1 (X1) - 均值: {X[:, 0].mean():.3f}, 标准差: {X[:, 0].std():.3f}")
    print(f"特征2 (X2) - 均值: {X[:, 1].mean():.3f}, 标准差: {X[:, 1].std():.3f}")
    
    print("\n各类别中心点:")
    for cls in unique:
        mask = y == cls
        center_x = X[mask, 0].mean()
        center_y = X[mask, 1].mean()
        print(f"类别 {cls}: ({center_x:.3f}, {center_y:.3f})")

def main():
    """
    主函数：运行完整的KNN演示
    """
    print("=" * 60)
    print("           KNN算法演示 - 使用生成的模拟数据")
    print("=" * 60)
    
    # 1. 生成数据
    print("\n步骤1: 生成模拟数据...")
    X, y = generate_sample_data(n_samples=600)
    analyze_data_statistics(X, y)
    
    # 2. 分割数据
    print(f"\n步骤2: 分割训练集和测试集...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    print(f"训练集大小: {X_train.shape[0]} 样本")
    print(f"测试集大小: {X_test.shape[0]} 样本")
    
    # 3. 寻找最佳K值
    print(f"\n步骤3: 寻找最佳K值...")
    best_k, best_accuracy = find_best_k(X_train, y_train, X_test, y_test)
    
    # 4. 使用最佳K值训练最终模型
    print(f"\n步骤4: 使用最佳K值 (K={best_k}) 训练最终模型...")
    final_model = train_knn_model(X_train, y_train, k=best_k)
    
    # 5. 详细评估模型
    print(f"\n步骤5: 详细评估模型性能...")
    y_pred = evaluate_model(final_model, X_test, y_test)
    
    # 6. 演示预测新数据点
    print(f"\n步骤6: 演示预测新数据点...")
    demonstrate_predictions(final_model)
    
    # 7. 展示模型特征
    print(f"\n步骤7: KNN模型特征分析...")
    print(f"使用的K值: {final_model.n_neighbors}")
    print(f"距离度量: {final_model.metric}")
    print(f"权重方式: {final_model.weights}")
    print(f"训练样本数: {final_model.n_samples_fit_}")
    
    # 8. 测试边界情况
    print(f"\n步骤8: 测试边界情况...")
    boundary_points = np.array([
        [3.5, 3.5],  # 类别边界附近
        [4.5, 4.0],  # 类别交界处
        [2.0, 4.0],  # 类别1和3之间
        [5.0, 2.5],  # 类别2附近
    ])
    print("边界点预测分析:")
    demonstrate_predictions(final_model, boundary_points)
    
    print("\n" + "=" * 60)
    print("           KNN算法演示完成！")
    print("=" * 60)
    
    return final_model, X, y, X_test, y_test, y_pred

if __name__ == "__main__":
    model, X, y, X_test, y_test, y_pred = main()