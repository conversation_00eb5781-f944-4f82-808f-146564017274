import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FrequencyDomainDefectDataset:
    """风机叶片内腔缺陷频域特征数据集生成器"""
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        np.random.seed(random_state)
        
        # 目标统计特征
        self.target_stats = {
            'mean': 0.004535467952873973,
            'variance': 0.13361739428910718,
            'mean_square': 0.017853608056610733,
            'rms': 0.13361739428910718,
            'max_value': 1.9846451308495212,
            'min_value': -1.7174139110666464,
            'peak_value': 1.9846451308495212,
            'peak_to_peak': 3.7020590419161676,
            'mean_absolute': 0.09177927629408723,
            'root_mean_square_amplitude': 0.07371838887961654
        }
        
        # 缺陷类型定义
        self.defect_types = {
            0: '纤维褶皱',
            1: '裂纹', 
            2: '分层',
            3: '鼓包'
        }
        
        # 缺陷颜色映射
        self.colors = {
            0: '#FF6B6B',  # 红色 - 纤维褶皱
            1: '#4ECDC4',  # 青色 - 裂纹
            2: '#45B7D1',  # 蓝色 - 分层
            3: '#96CEB4'   # 绿色 - 鼓包
        }

    def calculate_statistical_features(self, signal):
        """计算信号的统计特征"""
        # 基本统计量
        mean_val = np.mean(signal)
        variance = np.var(signal)
        mean_square = np.mean(signal**2)
        rms = np.sqrt(mean_square)
        max_val = np.max(signal)
        min_val = np.min(signal)
        peak_val = max(abs(max_val), abs(min_val))
        peak_to_peak = max_val - min_val
        mean_absolute = np.mean(np.abs(signal))
        root_mean_square_amplitude = np.sqrt(np.mean(np.abs(signal)))
        
        # 高阶统计量
        skewness = stats.skew(signal)
        kurtosis = stats.kurtosis(signal)
        
        # 形状指标
        waveform_factor = rms / mean_absolute if mean_absolute != 0 else 0
        peak_factor = peak_val / rms if rms != 0 else 0
        impulse_factor = peak_val / mean_absolute if mean_absolute != 0 else 0
        margin_factor = peak_val / root_mean_square_amplitude**2 if root_mean_square_amplitude != 0 else 0
        skewness_factor = skewness
        
        return {
            '平均值': mean_val,
            '方差': variance,
            '均方值': mean_square,
            '均方根值': rms,
            '最大值': max_val,
            '最小值': min_val,
            '峰值': peak_val,
            '峰峰值': peak_to_peak,
            '平均绝对幅值': mean_absolute,
            '方根幅值': root_mean_square_amplitude,
            '偏度': skewness,
            '峭度': kurtosis,
            '波形指标': waveform_factor,
            '峰值指标': peak_factor,
            '脉冲指标': impulse_factor,
            '裕度指标': margin_factor,
            '偏度指标': skewness_factor
        }

    def generate_target_signal(self, n_points=1024):
        """生成符合目标统计特征的信号"""
        # 直接生成符合目标统计特征的特征向量，而不是生成时域信号
        # 这样可以更精确地控制统计特征

        # 基础特征值（接近目标值，加入小的随机扰动）
        features = {}

        # 添加小的随机扰动以产生样本间的差异
        noise_scale = 0.05  # 5%的噪声水平

        features['平均值'] = self.target_stats['mean'] * (1 + np.random.normal(0, noise_scale))
        features['方差'] = self.target_stats['variance'] * (1 + np.random.normal(0, noise_scale))
        features['均方值'] = self.target_stats['mean_square'] * (1 + np.random.normal(0, noise_scale))
        features['均方根值'] = self.target_stats['rms'] * (1 + np.random.normal(0, noise_scale))
        features['最大值'] = self.target_stats['max_value'] * (1 + np.random.normal(0, noise_scale))
        features['最小值'] = self.target_stats['min_value'] * (1 + np.random.normal(0, noise_scale))
        features['峰值'] = self.target_stats['peak_value'] * (1 + np.random.normal(0, noise_scale))
        features['峰峰值'] = self.target_stats['peak_to_peak'] * (1 + np.random.normal(0, noise_scale))
        features['平均绝对幅值'] = self.target_stats['mean_absolute'] * (1 + np.random.normal(0, noise_scale))
        features['方根幅值'] = self.target_stats['root_mean_square_amplitude'] * (1 + np.random.normal(0, noise_scale))

        # 生成高阶统计量（偏度、峭度等）
        features['偏度'] = np.random.normal(0, 0.5)  # 偏度通常在-3到3之间
        features['峭度'] = np.random.normal(3, 1)    # 峭度，正态分布为3

        # 计算形状指标
        if features['平均绝对幅值'] != 0:
            features['波形指标'] = features['均方根值'] / features['平均绝对幅值']
        else:
            features['波形指标'] = 1.0

        if features['均方根值'] != 0:
            features['峰值指标'] = features['峰值'] / features['均方根值']
        else:
            features['峰值指标'] = 1.0

        if features['平均绝对幅值'] != 0:
            features['脉冲指标'] = features['峰值'] / features['平均绝对幅值']
        else:
            features['脉冲指标'] = 1.0

        if features['方根幅值'] != 0:
            features['裕度指标'] = features['峰值'] / (features['方根幅值']**2)
        else:
            features['裕度指标'] = 1.0

        features['偏度指标'] = features['偏度']

        return features

    def generate_defect_frequency_features(self, n_samples=10000):
        """生成缺陷频域特征数据集"""
        print(f"正在生成 {n_samples:,} 个频域特征样本...")

        samples_per_class = n_samples // 4
        all_features = []
        all_labels = []

        feature_names = [
            '平均值', '方差', '均方值', '均方根值', '最大值', '最小值',
            '峰值', '峰峰值', '平均绝对幅值', '方根幅值', '偏度', '峭度',
            '波形指标', '峰值指标', '脉冲指标', '裕度指标', '偏度指标'
        ]

        for defect_type in range(4):
            print(f"正在生成 {self.defect_types[defect_type]} 特征...")

            defect_features = []
            for i in range(samples_per_class):
                if i % 1000 == 0:
                    print(f"  进度: {i}/{samples_per_class}")

                # 直接生成特征向量
                features = self.generate_target_signal()

                # 根据缺陷类型调整特征
                defect_multiplier = {
                    0: 0.95 + 0.1 * np.random.random(),  # 纤维褶皱 - 轻微变化
                    1: 1.05 + 0.1 * np.random.random(),  # 裂纹 - 略微增强
                    2: 0.90 + 0.2 * np.random.random(),  # 分层 - 中等变化
                    3: 1.10 + 0.15 * np.random.random()  # 鼓包 - 较大变化
                }

                multiplier = defect_multiplier[defect_type]

                # 对某些特征进行缺陷类型相关的调整
                if defect_type == 1:  # 裂纹 - 增加峰值相关特征
                    features['峰值'] *= (1.1 + 0.1 * np.random.random())
                    features['峰值指标'] *= (1.1 + 0.1 * np.random.random())
                elif defect_type == 2:  # 分层 - 影响方差和均方根值
                    features['方差'] *= (0.9 + 0.1 * np.random.random())
                    features['均方根值'] *= (0.9 + 0.1 * np.random.random())
                elif defect_type == 3:  # 鼓包 - 影响幅值相关特征
                    features['平均绝对幅值'] *= (1.2 + 0.1 * np.random.random())
                    features['方根幅值'] *= (1.2 + 0.1 * np.random.random())

                # 构建特征向量
                feature_vector = [features[name] for name in feature_names]
                defect_features.append(feature_vector)

            all_features.extend(defect_features)
            all_labels.extend([defect_type] * samples_per_class)

        X = np.array(all_features)
        y = np.array(all_labels)

        return X, y, feature_names

    def create_dataset(self, n_samples=10000, save_files=True):
        """创建完整的频域特征数据集"""
        print("="*60)
        print("风机叶片内腔缺陷频域特征数据集生成器")
        print("="*60)
        
        # 生成特征数据
        X, y, feature_names = self.generate_defect_frequency_features(n_samples)
        
        # 创建DataFrame
        print("正在创建DataFrame...")
        df = pd.DataFrame(X, columns=feature_names)
        df['缺陷类型'] = [self.defect_types[label] for label in y]
        df['缺陷编码'] = y
        
        # 验证生成的数据集统计特征
        print("\n验证生成的数据集统计特征:")
        print("-" * 40)
        overall_stats = {}
        for feature in feature_names:
            feature_data = df[feature].values
            overall_stats[feature] = {
                '均值': np.mean(feature_data),
                '方差': np.var(feature_data),
                '最大值': np.max(feature_data),
                '最小值': np.min(feature_data)
            }
        
        # 显示主要统计特征的对比
        main_features = ['平均值', '方差', '均方值', '均方根值', '最大值', '最小值', 
                        '峰值', '峰峰值', '平均绝对幅值', '方根幅值']
        
        print(f"{'特征名称':<15} {'目标值':<15} {'实际均值':<15} {'差异':<15}")
        print("-" * 60)
        
        target_mapping = {
            '平均值': 'mean',
            '方差': 'variance', 
            '均方值': 'mean_square',
            '均方根值': 'rms',
            '最大值': 'max_value',
            '最小值': 'min_value',
            '峰值': 'peak_value',
            '峰峰值': 'peak_to_peak',
            '平均绝对幅值': 'mean_absolute',
            '方根幅值': 'root_mean_square_amplitude'
        }
        
        for feature in main_features:
            if feature in target_mapping:
                target_val = self.target_stats[target_mapping[feature]]
                actual_val = overall_stats[feature]['均值']
                diff = abs(actual_val - target_val)
                print(f"{feature:<15} {target_val:<15.6f} {actual_val:<15.6f} {diff:<15.6f}")
        
        if save_files:
            # 保存CSV文件
            csv_filename = f'frequency_domain_defects_{n_samples}.csv'
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"\n数据集已保存为: {csv_filename}")
            
            # 保存Excel文件
            excel_filename = f'frequency_domain_defects_{n_samples}.xlsx'
            with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='频域特征数据', index=False)
                
                # 添加统计摘要表
                summary_data = []
                for feature in feature_names:
                    feature_stats = df[feature].describe()
                    summary_data.append({
                        '特征名称': feature,
                        '均值': feature_stats['mean'],
                        '标准差': feature_stats['std'],
                        '最小值': feature_stats['min'],
                        '25%分位数': feature_stats['25%'],
                        '中位数': feature_stats['50%'],
                        '75%分位数': feature_stats['75%'],
                        '最大值': feature_stats['max']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计摘要', index=False)
            
            print(f"Excel文件已保存为: {excel_filename}")
        
        return df, X, y, feature_names

def main():
    """主函数"""
    # 创建数据集生成器
    generator = FrequencyDomainDefectDataset(random_state=42)
    
    # 生成10000个样本的数据集
    df, X, y, feature_names = generator.create_dataset(n_samples=10000, save_files=True)
    
    print(f"\n数据集生成完成！")
    print(f"总样本数: {len(df):,}")
    print(f"特征数量: {len(feature_names)}")
    print(f"缺陷类型: {len(df['缺陷类型'].unique())}")
    
    # 显示各类别样本分布
    print(f"\n各缺陷类型样本分布:")
    for defect_type, count in df['缺陷类型'].value_counts().items():
        print(f"  {defect_type}: {count:,} 个样本")
    
    return df, X, y, feature_names

if __name__ == "__main__":
    df, X, y, feature_names = main()
