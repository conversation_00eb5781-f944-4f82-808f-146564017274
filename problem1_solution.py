"""
问题1详细求解：供应商声称零配件次品率检测方案设计
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import binom, norm
import pandas as pd

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class InspectionSchemeDesign:
    """检测方案设计类"""
    
    def __init__(self, claimed_defect_rate=0.10):
        self.claimed_rate = claimed_defect_rate
        self.confidence_level = 0.95
        self.alpha = 1 - self.confidence_level  # 显著性水平
        
    def design_sampling_scheme(self, sample_size):
        """
        设计抽样检测方案
        
        参数:
        sample_size: 样本量
        
        返回:
        dict: 包含检测方案的详细信息
        """
        n = sample_size
        p0 = self.claimed_rate  # 原假设：次品率 <= p0
        
        # 使用二项分布设计检测方案
        # H0: p <= p0 vs H1: p > p0
        
        # 计算临界值（拒绝域的下界）
        # 如果观察到的次品数 > critical_value，则拒绝H0
        critical_value = binom.ppf(self.confidence_level, n, p0)
        
        # 计算第一类错误概率（错误拒绝合格供应商）
        type1_error = 1 - binom.cdf(critical_value, n, p0)
        
        # 计算不同真实次品率下的第二类错误概率（接受不合格供应商）
        true_rates = np.arange(0.10, 0.25, 0.01)
        type2_errors = []
        powers = []
        
        for p_true in true_rates:
            type2_error = binom.cdf(critical_value, n, p_true)
            power = 1 - type2_error
            type2_errors.append(type2_error)
            powers.append(power)
        
        return {
            'sample_size': n,
            'critical_value': critical_value,
            'type1_error': type1_error,
            'true_rates': true_rates,
            'type2_errors': type2_errors,
            'powers': powers
        }
    
    def compare_schemes(self):
        """比较不同样本量的检测方案"""
        sample_sizes = [50, 100, 200, 500]
        schemes = []
        
        for n in sample_sizes:
            scheme = self.design_sampling_scheme(n)
            schemes.append({
                '样本量': n,
                '临界值': int(scheme['critical_value']),
                '第一类错误': f"{scheme['type1_error']:.4f}",
                '检测成本': n * 2,  # 假设每个零配件检测成本为2元
                '决策规则': f"如果次品数 > {int(scheme['critical_value'])}，拒绝供应商声称"
            })
        
        df = pd.DataFrame(schemes)
        print("不同样本量的检测方案比较：")
        print(df.to_string(index=False))
        
        return schemes
    
    def plot_power_curves(self):
        """绘制功效曲线"""
        sample_sizes = [50, 100, 200, 500]
        plt.figure(figsize=(12, 8))
        
        for n in sample_sizes:
            scheme = self.design_sampling_scheme(n)
            plt.plot(scheme['true_rates'], scheme['powers'], 
                    label=f'样本量 = {n}', marker='o', markersize=4)
        
        plt.axhline(y=0.95, color='red', linestyle='--', alpha=0.7, label='期望功效 = 0.95')
        plt.axvline(x=self.claimed_rate, color='gray', linestyle='--', alpha=0.7, 
                   label=f'声称次品率 = {self.claimed_rate}')
        
        plt.xlabel('真实次品率')
        plt.ylabel('检验功效（1-β）')
        plt.title('不同样本量下的检验功效曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('power_curves.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def optimal_scheme_selection(self):
        """选择最优检测方案"""
        print("\n=== 最优检测方案选择 ===")
        
        # 考虑成本和效果的权衡
        sample_sizes = range(30, 501, 10)
        results = []
        
        for n in sample_sizes:
            scheme = self.design_sampling_scheme(n)
            
            # 计算在真实次品率为15%时的检验功效
            power_at_15 = 1 - binom.cdf(scheme['critical_value'], n, 0.15)
            
            # 检测成本
            cost = n * 2
            
            # 效用函数：功效/成本比
            utility = power_at_15 / cost if cost > 0 else 0
            
            results.append({
                'sample_size': n,
                'power_at_15': power_at_15,
                'cost': cost,
                'utility': utility
            })
        
        # 找到最优方案
        df_results = pd.DataFrame(results)
        optimal_idx = df_results['utility'].idxmax()
        optimal_scheme = df_results.iloc[optimal_idx]
        
        print(f"最优样本量: {optimal_scheme['sample_size']}")
        print(f"在15%次品率下的检验功效: {optimal_scheme['power_at_15']:.4f}")
        print(f"检测成本: {optimal_scheme['cost']}")
        print(f"效用比: {optimal_scheme['utility']:.6f}")
        
        # 绘制效用曲线
        plt.figure(figsize=(10, 6))
        plt.plot(df_results['sample_size'], df_results['utility'], 'b-', linewidth=2)
        plt.axvline(x=optimal_scheme['sample_size'], color='red', linestyle='--', 
                   label=f"最优样本量 = {optimal_scheme['sample_size']}")
        plt.xlabel('样本量')
        plt.ylabel('效用比（功效/成本）')
        plt.title('不同样本量的效用比较')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('utility_curve.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return optimal_scheme
    
    def detailed_analysis_for_specific_case(self, sample_size=100):
        """针对特定情况的详细分析"""
        print(f"\n=== 样本量为{sample_size}的详细分析 ===")
        
        scheme = self.design_sampling_scheme(sample_size)
        n = sample_size
        critical_value = int(scheme['critical_value'])
        
        print(f"样本量: {n}")
        print(f"临界值: {critical_value}")
        print(f"决策规则: 如果{n}个零配件中次品数量 > {critical_value}，则拒绝供应商声称")
        
        # 计算具体的概率
        print(f"\n概率分析:")
        print(f"在声称次品率{self.claimed_rate*100}%下:")
        print(f"  - 接受供应商的概率: {binom.cdf(critical_value, n, self.claimed_rate):.4f}")
        print(f"  - 拒绝供应商的概率: {1-binom.cdf(critical_value, n, self.claimed_rate):.4f}")
        
        # 不同真实次品率下的分析
        true_rates = [0.12, 0.15, 0.20]
        print(f"\n在不同真实次品率下的表现:")
        for rate in true_rates:
            accept_prob = binom.cdf(critical_value, n, rate)
            reject_prob = 1 - accept_prob
            print(f"  真实次品率{rate*100}%时:")
            print(f"    - 错误接受概率: {accept_prob:.4f}")
            print(f"    - 正确拒绝概率: {reject_prob:.4f}")
        
        return scheme

def main():
    """主函数"""
    print("=" * 60)
    print("问题1：供应商声称零配件次品率检测方案设计")
    print("=" * 60)
    
    # 创建检测方案设计对象
    inspector = InspectionSchemeDesign(claimed_defect_rate=0.10)
    
    # 1. 比较不同样本量的方案
    print("\n1. 不同样本量方案比较:")
    schemes = inspector.compare_schemes()
    
    # 2. 绘制功效曲线
    print("\n2. 绘制检验功效曲线:")
    inspector.plot_power_curves()
    
    # 3. 选择最优方案
    print("\n3. 最优方案选择:")
    optimal = inspector.optimal_scheme_selection()
    
    # 4. 详细分析特定情况
    print("\n4. 特定情况详细分析:")
    detailed = inspector.detailed_analysis_for_specific_case(100)
    
    # 5. 给出最终建议
    print("\n" + "=" * 60)
    print("最终建议:")
    print("=" * 60)
    print("基于成本效益分析，建议采用以下检测方案：")
    print(f"- 样本量: {int(optimal['sample_size'])}")
    print(f"- 检测成本: {optimal['cost']}元")
    print(f"- 在真实次品率15%时的检验功效: {optimal['power_at_15']:.1%}")
    print("- 该方案在控制检测成本的同时，能够有效识别次品率超标的供应商")
    
    return inspector, schemes, optimal

if __name__ == "__main__":
    inspector, schemes, optimal = main()
