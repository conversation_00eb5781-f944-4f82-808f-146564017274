<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三通道信号小波降噪结果</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }

        h2 {
            color: #2196F3;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .channel-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .channel-red {
            border-left: 5px solid #f44336;
        }

        .channel-blue {
            border-left: 5px solid #2196F3;
        }

        .channel-green {
            border-left: 5px solid #4CAF50;
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .stats-box {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }

        .description {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
            margin: 15px 0;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>三通道信号小波降噪分析结果</h1>

        <div class="description">
            <h3>项目概述</h3>
            <p>本项目生成了三个通道的含噪声信号，分别用红色、蓝色、绿色表示，并使用小波变换技术对这些信号进行降噪处理。</p>
            <ul>
                <li><strong>通道1 (红色)</strong>: 低频正弦波 + 高频成分的复合信号</li>
                <li><strong>通道2 (蓝色)</strong>: 调频信号，具有时变频率特性</li>
                <li><strong>通道3 (绿色)</strong>: 脉冲信号 + 正弦波的混合信号</li>
            </ul>
        </div>

        <h2>📊 三通道信号同轴对比</h2>
        <div class="image-container">
            <img src="three_channels_same_axis.png" alt="三通道信号同轴对比">
            <p><em>三个通道的信号在同一坐标轴内的对比显示，便于观察不同通道信号的相对关系</em></p>
        </div>

        <h2>🔍 综合信号分析对比</h2>
        <div class="image-container">
            <img src="comprehensive_signal_comparison.png" alt="综合信号对比">
            <p><em>包含同轴显示和各通道详细对比的综合分析图表</em></p>
        </div>

        <h2>📊 三通道分离对比</h2>
        <div class="image-container">
            <img src="three_channel_comparison.png" alt="三通道信号分离对比">
            <p><em>三个通道的原始信号、含噪声信号和降噪后信号的分离对比</em></p>
        </div>

        <h2>🔬 FFT频域分析</h2>
        <div class="image-container">
            <img src="fft_three_channels_same_axis.png" alt="三通道FFT频谱同轴对比">
            <p><em>三个通道的FFT频谱在同一坐标轴内的对比，清晰显示各通道的频率成分</em></p>
        </div>

        <h2>📊 FFT频域详细分析</h2>
        <div class="image-container">
            <img src="fft_frequency_analysis.png" alt="FFT频域分析">
            <p><em>三个通道的原始信号、含噪声信号和降噪后信号的FFT频域分析对比</em></p>
        </div>

        <h2>🔍 时域频域综合分析</h2>
        <div class="image-container">
            <img src="time_frequency_comprehensive_analysis.png" alt="时域频域综合分析">
            <p><em>时域信号和频域频谱的综合对比分析，全面展示小波降噪的效果</em></p>
        </div>

        <h2>📈 信噪比改善效果</h2>
        <div class="comparison-grid">
            <div class="image-container">
                <img src="snr_comparison.png" alt="信噪比对比">
                <p><em>小波降噪前后的信噪比对比</em></p>
            </div>
            <div class="stats-box">
                <h3>降噪效果统计</h3>
                <p><strong>通道1 (红色):</strong></p>
                <ul>
                    <li>降噪前SNR: 9.85 dB</li>
                    <li>降噪后SNR: 11.55 dB</li>
                    <li>SNR改善: 1.69 dB</li>
                </ul>

                <p><strong>通道2 (蓝色):</strong></p>
                <ul>
                    <li>降噪前SNR: 3.89 dB</li>
                    <li>降噪后SNR: 7.31 dB</li>
                    <li>SNR改善: 3.41 dB</li>
                </ul>

                <p><strong>通道3 (绿色):</strong></p>
                <ul>
                    <li>降噪前SNR: 9.29 dB</li>
                    <li>降噪后SNR: 12.45 dB</li>
                    <li>SNR改善: 3.16 dB</li>
                </ul>

                <div class="highlight">
                    <strong>平均SNR改善: 2.76 dB</strong>
                </div>
            </div>
        </div>

        <h2>🔍 各通道详细分析</h2>

        <div class="channel-section channel-red">
            <h3>通道1 (红色) - 复合正弦信号</h3>
            <div class="image-container">
                <img src="wavelet_analysis_channel_1.png" alt="通道1分析">
                <p><em>通道1的原始信号、含噪声信号、降噪后信号及对比分析</em></p>
            </div>
            <p>该通道包含多个频率成分的正弦波，小波降噪有效保留了主要频率成分，同时去除了高频噪声。</p>
        </div>

        <div class="channel-section channel-blue">
            <h3>通道2 (蓝色) - 调频信号</h3>
            <div class="image-container">
                <img src="wavelet_analysis_channel_2.png" alt="通道2分析">
                <p><em>通道2的原始信号、含噪声信号、降噪后信号及对比分析</em></p>
            </div>
            <p>调频信号的频率随时间变化，小波降噪在保持信号时频特性的同时，显著改善了信噪比（改善3.41 dB）。</p>
        </div>

        <div class="channel-section channel-green">
            <h3>通道3 (绿色) - 脉冲混合信号</h3>
            <div class="image-container">
                <img src="wavelet_analysis_channel_3.png" alt="通道3分析">
                <p><em>通道3的原始信号、含噪声信号、降噪后信号及对比分析</em></p>
            </div>
            <p>脉冲信号与正弦波的混合信号，小波降噪很好地保持了脉冲的尖锐特性和正弦波的平滑特性。</p>
        </div>

        <h2>🔧 技术参数</h2>
        <div class="description">
            <h3>小波降噪参数设置</h3>
            <ul>
                <li><strong>小波基函数</strong>: Daubechies 8 (db8)</li>
                <li><strong>分解层数</strong>: 6层</li>
                <li><strong>阈值模式</strong>: 软阈值 (soft thresholding)</li>
                <li><strong>阈值估计</strong>: SURE阈值方法</li>
                <li><strong>采样频率</strong>: 1000 Hz</li>
                <li><strong>信号持续时间</strong>: 2秒</li>
            </ul>

            <h3>FFT频域分析参数</h3>
            <ul>
                <li><strong>FFT算法</strong>: 快速傅里叶变换 (NumPy FFT)</li>
                <li><strong>频率分辨率</strong>: 0.5 Hz (2秒信号长度)</li>
                <li><strong>显示频率范围</strong>: 0-100 Hz (主要频率成分)</li>
                <li><strong>幅度计算</strong>: 双边谱转单边谱，归一化处理</li>
                <li><strong>窗函数</strong>: 矩形窗 (无额外窗函数)</li>
            </ul>
        </div>

        <h2>📋 结论</h2>
        <div class="highlight">
            <p>小波降噪技术结合FFT频域分析成功应用于三个不同特性的信号通道：</p>

            <h4>🎯 时域降噪效果</h4>
            <ul>
                <li>所有通道的信噪比都得到了显著改善</li>
                <li>通道2（蓝色）的改善效果最明显，SNR提升了3.41 dB</li>
                <li>小波降噪很好地保持了各种信号的特征（正弦波、调频、脉冲等）</li>
                <li>平均SNR改善达到2.76 dB，证明了小波降噪的有效性</li>
            </ul>

            <h4>🔬 频域分析结果</h4>
            <ul>
                <li><strong>通道1（红色）</strong>：主要频率成分集中在5Hz、15Hz和50Hz，降噪后高频噪声明显减少</li>
                <li><strong>通道2（蓝色）</strong>：调频信号的频谱呈现宽带特性，降噪后频谱更加清晰</li>
                <li><strong>通道3（绿色）</strong>：脉冲信号产生丰富的谐波成分，降噪有效保留了主要频率成分</li>
                <li>FFT分析验证了小波降噪在频域的有效性，噪声主要分布在高频段被成功滤除</li>
            </ul>

            <h4>✨ 技术优势</h4>
            <ul>
                <li>时频域联合分析提供了全面的信号质量评估</li>
                <li>小波降噪在保持信号特征的同时有效去除噪声</li>
                <li>FFT频域分析清晰展示了降噪前后的频率成分变化</li>
                <li>多通道同轴显示便于对比分析不同信号的特性</li>
            </ul>
        </div>
    </div>
</body>

</html>