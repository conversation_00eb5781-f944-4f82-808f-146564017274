
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有效数据筛选可视化展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .image-container {
            text-align: center;
            margin: 30px 0;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        .card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .funnel-stats {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .stat-label {
            font-weight: bold;
            color: #495057;
        }
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        .improvement {
            color: #28a745;
        }
        .loss {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 有效数据筛选可视化展示</h1>
        
        <div class="highlight">
            <h3>💎 数据筛选的价值</h3>
            <p><strong>"垃圾进，垃圾出"</strong> - 高质量的数据是机器学习成功的基础，有效的数据筛选能够显著提升模型性能和可靠性</p>
        </div>

        <div class="image-container">
            <h2>📊 完整数据筛选流程可视化</h2>
            <img src="data_filtering_visualization.png" alt="有效数据筛选可视化">
            <p style="color: #666; margin-top: 15px; font-style: italic;">
                包含12个核心模块：筛选漏斗、质量雷达图、异常检测、流程图、缺失值分析、分布对比、决策树、评分卡、结果统计、时间线、工具箱、效果对比
            </p>
        </div>

        <div class="funnel-stats">
            <h3>📈 数据筛选统计</h3>
            <div class="stat-item">
                <span class="stat-label">原始数据量</span>
                <span class="stat-value">100,000 条</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">格式检查后</span>
                <span class="stat-value">85,000 条 <span class="loss">(-15%)</span></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">完整性检查后</span>
                <span class="stat-value">72,000 条 <span class="loss">(-15.3%)</span></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">质量检查后</span>
                <span class="stat-value">58,000 条 <span class="loss">(-19.4%)</span></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">异常值过滤后</span>
                <span class="stat-value">52,000 条 <span class="loss">(-10.3%)</span></span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最终有效数据</span>
                <span class="stat-value">50,000 条 <span class="improvement">(50% 保留率)</span></span>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 筛选目标</h3>
                <ul>
                    <li><strong>提高数据质量</strong>：去除错误、缺失、异常数据</li>
                    <li><strong>确保数据一致性</strong>：统一格式和标准</li>
                    <li><strong>增强模型可靠性</strong>：为机器学习提供优质数据</li>
                    <li><strong>降低计算成本</strong>：减少无效数据处理</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🔧 筛选方法</h3>
                <ul>
                    <li><strong>格式验证</strong>：检查数据类型和格式规范</li>
                    <li><strong>完整性检查</strong>：识别和处理缺失值</li>
                    <li><strong>异常值检测</strong>：统计方法和机器学习检测</li>
                    <li><strong>业务规则验证</strong>：基于领域知识的规则</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>📊 质量提升</h3>
                <ul>
                    <li><strong>完整性</strong>：60% → 95% <span class="improvement">(+58%)</span></li>
                    <li><strong>准确性</strong>：55% → 92% <span class="improvement">(+67%)</span></li>
                    <li><strong>一致性</strong>：50% → 88% <span class="improvement">(+76%)</span></li>
                    <li><strong>有效性</strong>：45% → 94% <span class="improvement">(+109%)</span></li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🛠️ 筛选工具</h3>
                <ul>
                    <li><strong>格式验证器</strong>：自动检查数据格式</li>
                    <li><strong>异常值过滤器</strong>：统计和ML方法</li>
                    <li><strong>重复数据清理器</strong>：智能去重算法</li>
                    <li><strong>质量评估器</strong>：多维度质量评分</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>⚡ 筛选效果</h3>
                <ul>
                    <li><strong>数据质量</strong>：显著提升各项质量指标</li>
                    <li><strong>模型性能</strong>：预期提升15-30%</li>
                    <li><strong>处理效率</strong>：减少50%的无效计算</li>
                    <li><strong>可维护性</strong>：降低数据维护成本</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎨 可视化特色</h3>
                <ul>
                    <li><strong>漏斗图</strong>：直观展示筛选过程</li>
                    <li><strong>雷达图</strong>：多维度质量对比</li>
                    <li><strong>散点图</strong>：异常值检测可视化</li>
                    <li><strong>决策树</strong>：筛选规则逻辑展示</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <h3>🚀 应用价值</h3>
            <p>通过系统化的数据筛选流程，我们成功将原始数据的可用率从45%提升到94%，为后续的机器学习模型提供了高质量的训练数据，预期能够显著提升模型的准确性和可靠性。</p>
        </div>
    </div>
</body>
</html>