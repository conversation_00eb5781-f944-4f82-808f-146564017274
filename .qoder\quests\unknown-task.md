# TOPSIS多准则决策算法设计文档

## 概述

TOPSIS (Technique for Order of Preference by Similarity to Ideal Solution) 是一种多准则决策分析方法，通过计算每个方案与理想解和负理想解的距离来确定最优方案。本设计文档描述了一个完整的TOPSIS算法实现，包含数据处理、算法计算和多维度可视化功能。

### 核心功能
- 多准则决策矩阵处理
- 权重计算与归一化
- 理想解与负理想解计算
- 相对贴近度评估
- 交互式可视化展示
- 敏感性分析

## 技术架构

### 系统架构图

```mermaid
graph TB
    A[数据输入层] --> B[数据预处理模块]
    B --> C[TOPSIS算法核心]
    C --> D[结果计算模块]
    D --> E[可视化引擎]
    E --> F[交互式展示]
    
    G[权重配置] --> C
    H[准则类型配置] --> C
    
    subgraph "可视化组件"
        E --> I[雷达图]
        E --> J[柱状图]
        E --> K[散点图]
        E --> L[热力图]
        E --> M[敏感性分析图]
    end
```

### 数据流架构

```mermaid
flowchart LR
    A[原始决策矩阵] --> B[数据验证]
    B --> C[标准化处理]
    C --> D[加权标准化]
    D --> E[理想解计算]
    E --> F[距离计算]
    F --> G[相对贴近度]
    G --> H[排序结果]
    H --> I[可视化输出]
```

## 核心模块设计

### 1. TOPSIS算法核心类

```python
class TOPSISAnalyzer:
    """TOPSIS多准则决策分析器"""
    
    def __init__(self, decision_matrix, weights, criteria_types):
        """
        初始化TOPSIS分析器
        
        Args:
            decision_matrix: 决策矩阵 (m×n)
            weights: 准则权重向量 (n×1)
            criteria_types: 准则类型 ('max'/'min')
        """
        
    def normalize_matrix(self):
        """标准化决策矩阵"""
        
    def calculate_weighted_matrix(self):
        """计算加权标准化矩阵"""
        
    def find_ideal_solutions(self):
        """计算理想解和负理想解"""
        
    def calculate_distances(self):
        """计算到理想解的距离"""
        
    def calculate_closeness(self):
        """计算相对贴近度"""
        
    def get_rankings(self):
        """获取方案排序"""
```

### 2. 可视化模块设计

#### 2.1 雷达图可视化
- 展示各方案在不同准则下的表现
- 支持多方案对比
- 理想解与负理想解标记

#### 2.2 综合评价可视化
- 相对贴近度柱状图
- 方案排序展示
- 数值标签显示

#### 2.3 敏感性分析图
- 权重变化对排序的影响
- 热力图展示敏感度
- 交互式权重调节

### 3. 数据处理模块

#### 3.1 数据输入接口
- Excel文件读取
- CSV数据导入
- 手动数据输入界面
- 数据格式验证

#### 3.2 数据预处理
- 缺失值处理
- 异常值检测
- 数据类型转换
- 一致性检查

## 算法实现流程

### 步骤1: 构建决策矩阵
```
X = [x₁₁  x₁₂  ...  x₁ₙ]
    [x₂₁  x₂₂  ...  x₂ₙ]
    [...  ...  ...  ...]
    [xₘ₁  xₘ₂  ...  xₘₙ]
```

### 步骤2: 标准化处理
```
向量标准化: rᵢⱼ = xᵢⱼ / √(Σₖ₌₁ᵐ x²ₖⱼ)
```

### 步骤3: 加权标准化
```
vᵢⱼ = wⱼ × rᵢⱼ
```

### 步骤4: 确定理想解
```
正理想解: A⁺ = {v₁⁺, v₂⁺, ..., vₙ⁺}
负理想解: A⁻ = {v₁⁻, v₂⁻, ..., vₙ⁻}
```

### 步骤5: 计算距离
```
到正理想解距离: d⁺ᵢ = √(Σⱼ₌₁ⁿ (vᵢⱼ - vⱼ⁺)²)
到负理想解距离: d⁻ᵢ = √(Σⱼ₌₁ⁿ (vᵢⱼ - vⱼ⁻)²)
```

### 步骤6: 相对贴近度
```
Cᵢ = d⁻ᵢ / (d⁺ᵢ + d⁻ᵢ)
```

## 可视化设计规范

### 图表类型与用途

| 图表类型 | 用途           | 数据维度  |
| -------- | -------------- | --------- |
| 雷达图   | 方案多维度对比 | 准则×方案 |
| 柱状图   | 相对贴近度排序 | 方案×得分 |
| 散点图   | 距离分布分析   | d⁺×d⁻     |
| 热力图   | 决策矩阵展示   | 方案×准则 |
| 折线图   | 敏感性分析     | 权重×排序 |

### 色彩设计
- 主色调: 蓝色系 (#2E86AB, #A23B72, #F18F01)
- 理想解: 绿色 (#28A745)
- 负理想解: 红色 (#DC3545)
- 中性色: 灰色系用于背景和辅助元素

### 交互功能
- 鼠标悬停显示详细数值
- 点击方案高亮对应数据
- 权重滑块实时更新结果
- 图表缩放和平移功能

## 文件结构设计

```
topsis_system/
├── topsis_analyzer.py          # TOPSIS算法核心
├── data_processor.py           # 数据处理模块
├── visualization_engine.py     # 可视化引擎
├── sensitivity_analyzer.py     # 敏感性分析
├── web_interface.html          # Web交互界面
├── utils/
│   ├── file_handlers.py        # 文件处理工具
│   ├── validators.py           # 数据验证
│   └── math_utils.py           # 数学计算工具
├── templates/
│   ├── radar_chart.html        # 雷达图模板
│   ├── bar_chart.html          # 柱状图模板
│   └── heatmap.html            # 热力图模板
└── examples/
    ├── supplier_selection.py   # 供应商选择案例
    ├── project_evaluation.py   # 项目评估案例
    └── sample_data/             # 示例数据集
```

## 数据模型设计

### DecisionMatrix类
```python
class DecisionMatrix:
    """决策矩阵数据模型"""
    alternatives: List[str]      # 方案名称
    criteria: List[str]          # 准则名称
    matrix: np.ndarray          # 决策矩阵
    weights: np.ndarray         # 权重向量
    criteria_types: List[str]   # 准则类型
```

### TOPSISResult类
```python
class TOPSISResult:
    """TOPSIS分析结果"""
    normalized_matrix: np.ndarray     # 标准化矩阵
    weighted_matrix: np.ndarray       # 加权矩阵
    ideal_solution: np.ndarray        # 理想解
    negative_ideal: np.ndarray        # 负理想解
    distances_positive: np.ndarray    # 到正理想解距离
    distances_negative: np.ndarray    # 到负理想解距离
    closeness_coefficients: np.ndarray # 相对贴近度
    rankings: List[int]               # 排序结果
```

## 用户界面设计

### 主界面布局
1. **数据输入区域**
   - 文件上传按钮
   - 手动输入表格
   - 权重设置面板

2. **算法配置区域**
   - 准则类型选择
   - 标准化方法选择
   - 计算参数设置

3. **结果展示区域**
   - 排序结果表格
   - 可视化图表容器
   - 详细分析报告

4. **交互控制区域**
   - 图表类型切换
   - 敏感性分析控制
   - 导出功能按钮

### 响应式设计
- 支持桌面和平板设备
- 自适应图表大小
- 移动端友好的交互方式

## 性能优化策略

### 算法优化
- 矩阵运算向量化
- 大数据集分批处理
- 缓存中间计算结果
- 并行计算支持

### 可视化优化
- Canvas渲染大数据量图表
- 图表延迟加载
- 数据采样和聚合
- WebGL加速支持

## 扩展功能设计

### 高级分析功能
- 群决策TOPSIS
- 模糊TOPSIS
- 区间TOPSIS
- 灰色TOPSIS

### 集成功能
- 与现有数据分析模块集成
- API接口提供
- 批量分析处理
- 自动化报告生成

## 质量保证

### 算法验证
- 标准测试案例验证
- 数值精度测试
- 边界条件测试
- 性能基准测试

### 可视化测试
- 不同数据规模测试
- 浏览器兼容性测试
- 交互功能测试
- 视觉回归测试

## 部署考虑

### 本地部署
- Python环境要求
- 依赖包管理
- 配置文件设置
- 启动脚本

### Web部署
- 静态资源优化
- CDN配置
- 缓存策略
- 安全考虑