<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缺陷严重程度分级标注系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            max-width: 900px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2.2em;
            color: #4a5568;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            font-size: 1.2em;
            margin-right: 15px;
        }

        .section-description {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.8;
        }

        .image-container {
            text-align: center;
            margin: 25px 0;
        }

        .severity-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }

        .severity-image:hover {
            transform: scale(1.02);
        }

        .image-caption {
            font-style: italic;
            color: #666;
            margin-top: 15px;
            font-size: 1em;
        }

        .severity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .severity-card {
            padding: 25px;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 3px solid;
        }

        .severity-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .severity-card.level-1 {
            background: linear-gradient(135deg, #2ECC71, #27AE60);
            border-color: #27AE60;
            color: white;
        }

        .severity-card.level-2 {
            background: linear-gradient(135deg, #F39C12, #E67E22);
            border-color: #E67E22;
            color: white;
        }

        .severity-card.level-3 {
            background: linear-gradient(135deg, #E67E22, #D35400);
            border-color: #D35400;
            color: white;
        }

        .severity-card.level-4 {
            background: linear-gradient(135deg, #E74C3C, #C0392B);
            border-color: #C0392B;
            color: white;
        }

        .severity-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            text-align: center;
        }

        .severity-card .level-number {
            font-size: 3em;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .severity-card .criteria {
            margin: 15px 0;
        }

        .severity-card .criteria h4 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .severity-card .criteria ul {
            padding-left: 20px;
        }

        .severity-card .criteria li {
            margin-bottom: 5px;
            font-size: 0.95em;
        }

        .severity-card .action {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }

        .comparison-table {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            margin: 25px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            background: #667eea;
            color: white;
            padding: 15px;
            font-weight: bold;
            text-align: center;
        }

        .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            align-items: center;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-row:nth-child(even) {
            background: #f8f9fa;
        }

        .table-cell {
            text-align: center;
            padding: 8px;
        }

        .severity-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin: 0 auto;
        }

        .level-1-indicator {
            background: #2ECC71;
        }

        .level-2-indicator {
            background: #F39C12;
        }

        .level-3-indicator {
            background: #E67E22;
        }

        .level-4-indicator {
            background: #E74C3C;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 50px;
            padding: 30px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }

            .section-title {
                font-size: 1.8em;
            }

            .container {
                padding: 15px;
            }

            .section {
                padding: 20px;
            }

            .table-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ 缺陷严重程度分级标注系统</h1>
            <p>基于多维特征的智能缺陷严重程度评估与风险分级管理系统</p>
        </div>

        <!-- 严重程度分级标准 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📊</span>
                四级严重程度分级标准
            </h2>
            <p class="section-description">
                建立标准化的四级缺陷严重程度分级体系，每个等级都有明确的判定标准、风险评估和维修策略，
                确保缺陷管理的科学性和一致性。
            </p>

            <div class="severity-grid">
                <div class="severity-card level-1">
                    <div class="level-number">1</div>
                    <h3>轻微缺陷</h3>
                    <div class="criteria">
                        <h4>🔍 判定标准</h4>
                        <ul>
                            <li>裂纹长度 < 5mm</li>
                            <li>信号幅值 < 2.0</li>
                            <li>风险评分 < 25</li>
                            <li>结构完整性良好</li>
                        </ul>
                    </div>
                    <div class="action">
                        ✅ 继续监测 | 3个月复查
                    </div>
                </div>

                <div class="severity-card level-2">
                    <div class="level-number">2</div>
                    <h3>中等缺陷</h3>
                    <div class="criteria">
                        <h4>⚠️ 判定标准</h4>
                        <ul>
                            <li>裂纹长度 5-15mm</li>
                            <li>信号幅值 2.0-4.0</li>
                            <li>风险评分 25-50</li>
                            <li>局部性能下降</li>
                        </ul>
                    </div>
                    <div class="action">
                        🔍 加强监测 | 1个月复查
                    </div>
                </div>

                <div class="severity-card level-3">
                    <div class="level-number">3</div>
                    <h3>严重缺陷</h3>
                    <div class="criteria">
                        <h4>🚨 判定标准</h4>
                        <ul>
                            <li>裂纹长度 15-35mm</li>
                            <li>信号幅值 4.0-7.0</li>
                            <li>风险评分 50-80</li>
                            <li>结构性能受影响</li>
                        </ul>
                    </div>
                    <div class="action">
                        🔧 计划维修 | 2周内安排
                    </div>
                </div>

                <div class="severity-card level-4">
                    <div class="level-number">4</div>
                    <h3>危险缺陷</h3>
                    <div class="criteria">
                        <h4>🆘 判定标准</h4>
                        <ul>
                            <li>裂纹长度 > 35mm</li>
                            <li>信号幅值 > 7.0</li>
                            <li>风险评分 > 80</li>
                            <li>结构安全威胁</li>
                        </ul>
                    </div>
                    <div class="action">
                        🛑 立即停机 | 紧急维修
                    </div>
                </div>
            </div>
        </div>

        <!-- 严重程度总览 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📈</span>
                严重程度分级总览
            </h2>
            <p class="section-description">
                展示四种缺陷类型在不同严重程度下的分布情况、风险评分矩阵和信号特征关系，
                为缺陷评估提供直观的可视化参考。
            </p>

            <div class="image-container">
                <img src="defect_severity_overview.png" alt="严重程度总览" class="severity-image">
                <p class="image-caption">缺陷严重程度分级总览 - 分布、风险矩阵、信号关系</p>
            </div>

            <div class="image-container">
                <img src="severity_annotation_process.png" alt="标注过程可视化" class="severity-image">
                <p class="image-caption">严重程度标注过程完整可视化 - 从信号到决策的全流程</p>
            </div>
        </div>

        <!-- 详细分类展示 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🔍</span>
                详细分类展示
            </h2>
            <p class="section-description">
                针对四种缺陷类型（裂纹、纤维褶皱、分层、鼓包），展示每种缺陷在不同严重程度下的
                特征分布和分级标准，通过散点图直观显示缺陷尺寸与信号幅值的关系。
            </p>

            <div class="image-container">
                <img src="defect_severity_detailed_classification.png" alt="详细分类图" class="severity-image">
                <p class="image-caption">四种缺陷类型的严重程度详细分级 - 尺寸与信号幅值关系</p>
            </div>

            <div class="image-container">
                <img src="severity_signal_examples.png" alt="严重程度信号示例" class="severity-image">
                <p class="image-caption">四个严重程度等级的缺陷信号示例 - 时域与频域对比</p>
            </div>
        </div>

        <!-- 决策树 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">🌳</span>
                严重程度判定决策树
            </h2>
            <p class="section-description">
                提供结构化的缺陷严重程度判定流程，从缺陷类型识别到严重程度分级的完整决策路径，
                包含详细的判定标准和阈值设定。
            </p>

            <div class="image-container">
                <img src="severity_decision_tree.png" alt="严重程度决策树" class="severity-image">
                <p class="image-caption">缺陷严重程度判定决策树 - 分级标准与判定流程</p>
            </div>

            <div class="image-container">
                <img src="severity_comparison_chart.png" alt="严重程度对比图表" class="severity-image">
                <p class="image-caption">四种缺陷类型的严重程度风险评分对比分析</p>
            </div>
        </div>

        <!-- 风险评估矩阵 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">⚠️</span>
                风险评估与维修决策
            </h2>
            <p class="section-description">
                基于缺陷类型和严重程度的风险评估矩阵，提供量化的风险评分和相应的维修策略建议，
                支持科学的维修决策和资源配置。
            </p>

            <div class="image-container">
                <img src="risk_assessment_matrix.png" alt="风险评估矩阵" class="severity-image">
                <p class="image-caption">缺陷风险评估矩阵与维修紧急程度决策</p>
            </div>
        </div>

        <!-- 分级标准对比表 -->
        <div class="section">
            <h2 class="section-title">
                <span class="icon">📋</span>
                分级标准对比表
            </h2>

            <div class="comparison-table">
                <div class="table-header">
                    缺陷严重程度分级标准对比表
                </div>

                <div class="table-row">
                    <div class="table-cell"><strong>严重程度</strong></div>
                    <div class="table-cell"><strong>风险评分</strong></div>
                    <div class="table-cell"><strong>信号特征</strong></div>
                    <div class="table-cell"><strong>维修策略</strong></div>
                    <div class="table-cell"><strong>复查周期</strong></div>
                </div>

                <div class="table-row">
                    <div class="table-cell">
                        <div class="severity-indicator level-1-indicator"></div>
                        等级1 (轻微)
                    </div>
                    <div class="table-cell">0-25分</div>
                    <div class="table-cell">幅值<2.0, 低频为主</div>
                            <div class="table-cell">继续监测</div>
                            <div class="table-cell">3个月</div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">
                            <div class="severity-indicator level-2-indicator"></div>
                            等级2 (中等)
                        </div>
                        <div class="table-cell">25-50分</div>
                        <div class="table-cell">幅值2.0-4.0, 中频增强</div>
                        <div class="table-cell">加强监测</div>
                        <div class="table-cell">1个月</div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">
                            <div class="severity-indicator level-3-indicator"></div>
                            等级3 (严重)
                        </div>
                        <div class="table-cell">50-80分</div>
                        <div class="table-cell">幅值4.0-7.0, 高频成分</div>
                        <div class="table-cell">计划维修</div>
                        <div class="table-cell">2周</div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">
                            <div class="severity-indicator level-4-indicator"></div>
                            等级4 (危险)
                        </div>
                        <div class="table-cell">80-100分</div>
                        <div class="table-cell">幅值>7.0, 宽频特征</div>
                        <div class="table-cell">立即停机</div>
                        <div class="table-cell">立即</div>
                    </div>
                </div>
            </div>

            <!-- 应用价值 -->
            <div class="section">
                <h2 class="section-title">
                    <span class="icon">💡</span>
                    系统应用价值
                </h2>

                <div class="severity-grid">
                    <div class="severity-card level-1">
                        <h3>🎯 精准分级</h3>
                        <p>基于多维特征的四级分级体系，确保缺陷评估的准确性和一致性，减少主观判断误差。</p>
                    </div>

                    <div class="severity-card level-2">
                        <h3>⚡ 快速决策</h3>
                        <p>标准化的决策流程和自动化评估工具，支持现场快速判定和及时响应。</p>
                    </div>

                    <div class="severity-card level-3">
                        <h3>📊 数据驱动</h3>
                        <p>基于大量历史数据建立的分级标准，持续优化和更新，提高预测准确性。</p>
                    </div>

                    <div class="severity-card level-4">
                        <h3>🔧 优化维修</h3>
                        <p>科学的维修策略指导，优化资源配置，降低维修成本，提高设备可靠性。</p>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>🔬 缺陷严重程度分级标注系统 | 基于信号特征的智能评估解决方案</p>
                <p>💡 四级分级体系 + 风险量化评估 + 维修决策支持 = 全面的缺陷管理方案</p>
            </div>
        </div>
</body>

</html>