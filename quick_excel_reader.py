"""
快速Excel文件读取器
最简单的Excel文件读取方法
"""

import pandas as pd
import os

def quick_read_excel(file_path):
    """
    快速读取Excel文件
    
    使用方法:
    1. 将你的Excel文件路径替换到file_path参数中
    2. 运行这个函数
    
    参数:
    - file_path: Excel文件的完整路径
    
    返回:
    - DataFrame: 包含Excel数据的pandas DataFrame
    """
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 错误: 文件不存在 - {file_path}")
        print("请检查文件路径是否正确")
        return None
    
    try:
        print(f"📖 正在读取Excel文件: {file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 显示基本信息
        print(f"✅ 读取成功!")
        print(f"📊 数据形状: {df.shape[0]} 行, {df.shape[1]} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        # 显示前几行数据
        print(f"\n📄 前5行数据:")
        print(df.head())
        
        # 显示数据类型
        print(f"\n🔍 数据类型:")
        print(df.dtypes)
        
        # 检查缺失值
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print(f"\n⚠️  缺失值统计:")
            print(missing_values[missing_values > 0])
        else:
            print(f"\n✅ 没有缺失值")
        
        return df
        
    except Exception as e:
        print(f"❌ 读取Excel文件时出错: {str(e)}")
        print("可能的原因:")
        print("1. 文件格式不正确（请确保是.xlsx或.xls文件）")
        print("2. 文件被其他程序占用")
        print("3. 文件损坏")
        return None

# 使用示例
if __name__ == "__main__":
    print("🚀 Excel快速读取器")
    print("=" * 50)
    
    # 方法1: 直接指定文件路径
    # 请将下面的路径替换为你的Excel文件路径
    excel_file = "blade_cavity_defects.xlsx"  # 替换为你的Excel文件路径
    
    # 读取Excel文件
    data = quick_read_excel(excel_file)
    
    if data is not None:
        print(f"\n🎉 成功读取Excel文件!")
        print(f"现在你可以使用 'data' 变量来操作Excel数据")
        
        # 一些常用操作示例
        print(f"\n💡 常用操作示例:")
        print(f"1. 查看数据形状: data.shape")
        print(f"2. 查看列名: data.columns")
        print(f"3. 查看前几行: data.head()")
        print(f"4. 查看统计信息: data.describe()")
        print(f"5. 选择某列: data['列名']")
        print(f"6. 筛选数据: data[data['列名'] > 某个值]")
        print(f"7. 保存为CSV: data.to_csv('output.csv', index=False)")
    
    # 如果你想读取其他Excel文件，可以这样做：
    print(f"\n" + "=" * 50)
    print(f"📝 如何读取你自己的Excel文件:")
    print(f"1. 将你的Excel文件路径复制")
    print(f"2. 替换上面的 excel_file 变量")
    print(f"3. 重新运行这个脚本")
    print(f"\n例如:")
    print(f"excel_file = 'C:/Users/<USER>/Documents/你的文件.xlsx'")
    print(f"excel_file = 'data.xlsx'  # 如果文件在当前目录")
    
    # 显示当前目录中的Excel文件
    current_dir = "."
    excel_files = [f for f in os.listdir(current_dir) if f.endswith(('.xlsx', '.xls'))]
    
    if excel_files:
        print(f"\n📁 当前目录中的Excel文件:")
        for i, file in enumerate(excel_files, 1):
            print(f"   {i}. {file}")
    else:
        print(f"\n📁 当前目录中没有找到Excel文件")

# 更多读取选项的函数
def read_excel_with_options(file_path, sheet_name=0, header=0, usecols=None, skiprows=None):
    """
    带选项的Excel读取函数
    
    参数:
    - file_path: Excel文件路径
    - sheet_name: 工作表名称或索引（默认0，即第一个工作表）
    - header: 标题行位置（默认0，即第一行）
    - usecols: 要读取的列（例如: 'A:C' 或 [0,1,2]）
    - skiprows: 要跳过的行数
    """
    try:
        df = pd.read_excel(file_path, 
                          sheet_name=sheet_name,
                          header=header,
                          usecols=usecols,
                          skiprows=skiprows)
        
        print(f"✅ 成功读取Excel文件: {file_path}")
        print(f"📊 数据形状: {df.shape}")
        return df
        
    except Exception as e:
        print(f"❌ 读取失败: {str(e)}")
        return None

# 读取多个工作表的函数
def read_all_sheets(file_path):
    """读取Excel文件中的所有工作表"""
    try:
        # 读取所有工作表
        all_sheets = pd.read_excel(file_path, sheet_name=None)
        
        print(f"✅ 成功读取Excel文件: {file_path}")
        print(f"📋 工作表数量: {len(all_sheets)}")
        
        for sheet_name, df in all_sheets.items():
            print(f"   📄 工作表 '{sheet_name}': {df.shape}")
        
        return all_sheets
        
    except Exception as e:
        print(f"❌ 读取失败: {str(e)}")
        return None
