<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1400" height="820" viewBox="0 0 1400 820">
  <defs>
    <style>
      .title{font:700 28px 'Segoe UI',<PERSON>l; fill:#222}
      .subtitle{font:500 16px 'Segoe UI',Arial; fill:#444}
      .label{font:600 14px 'Segoe UI',Arial; fill:#222}
      .small{font:12px 'Segoe UI',Arial; fill:#444}
      .bladeEdge{fill:url(#bladeGrad); stroke:#5f6a72; stroke-width:2}
      .bladeTop{fill:url(#topGrad); stroke:#5f6a72; stroke-width:1.5}
      .bladeSide{fill:url(#sideGrad); stroke:#5f6a72; stroke-width:1.5}
      .cavityFace{fill:url(#cavityGrad); stroke:#9aa2a8; stroke-width:1}
      .sensor{fill:url(#sensorGrad); stroke:#1769aa; stroke-width:2}
      .sensorTop{fill:url(#sensorTopGrad); stroke:#0d47a1; stroke-width:1}
      .defect{fill:url(#defectGrad); stroke:#b71c1c; stroke-width:1.5}
      .beam{stroke:#1e88e5; stroke-width:3; stroke-linecap:round; opacity:.85}
      .beamGlow{stroke:#90caf9; stroke-width:8; stroke-linecap:round; opacity:.25}
      .ref{stroke:#e53935; stroke-width:2.5; stroke-dasharray:8 6; stroke-linecap:round}
      .refGlow{stroke:#ef9a9a; stroke-width:7; stroke-linecap:round; opacity:.22}
      .equip{fill:url(#equipGrad); stroke:#5e35b1; stroke-width:2}
      .panel{fill:#f7f2ff; stroke:#7e57c2; stroke-width:1}
      .glass{fill:#e3f2fd; stroke:#64b5f6; opacity:.7}
      .leader{stroke:#616161; stroke-width:1.5; fill:none}
      .legend{fill:#fff; stroke:#cfd8dc}
    </style>
    <!-- 渐变定义 -->
    <linearGradient id="bladeGrad" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#e0e0e0"/>
      <stop offset="60%" stop-color="#fafafa"/>
      <stop offset="100%" stop-color="#c7cdd1"/>
    </linearGradient>
    <linearGradient id="topGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#e8ebee"/>
      <stop offset="100%" stop-color="#c7cdd1"/>
    </linearGradient>
    <linearGradient id="sideGrad" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#b9c1c7"/>
      <stop offset="100%" stop-color="#98a3ab"/>
    </linearGradient>
    <linearGradient id="cavityGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#f5f5f5"/>
      <stop offset="100%" stop-color="#e0e0e0"/>
    </linearGradient>
    <radialGradient id="sensorGrad" cx="50%" cy="40%" r="70%">
      <stop offset="0%" stop-color="#e3f2fd"/>
      <stop offset="100%" stop-color="#bbdefb"/>
    </radialGradient>
    <linearGradient id="sensorTopGrad" x1="0" y1="0" x2="0" y2="1">
      <stop offset="0%" stop-color="#bbdefb"/>
      <stop offset="100%" stop-color="#90caf9"/>
    </linearGradient>
    <radialGradient id="defectGrad" cx="40%" cy="40%" r="60%">
      <stop offset="0%" stop-color="#ffebee"/>
      <stop offset="100%" stop-color="#ef9a9a"/>
    </radialGradient>
    <linearGradient id="equipGrad" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#ede7f6"/>
      <stop offset="100%" stop-color="#d1c4e9"/>
    </linearGradient>
    <marker id="arrowBlue" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="7" markerHeight="7" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#1e88e5"/>
    </marker>
    <marker id="arrowRed" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="7" markerHeight="7" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#e53935"/>
    </marker>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#000" flood-opacity="0.18"/>
    </filter>
  </defs>

  <!-- 标题 -->
  <text x="50" y="56" class="title">风机叶片内腔声波检测（三角定位法）三维视角实物示意图</text>
  <text x="50" y="84" class="subtitle">等距透视：切割视图展示内腔与三传感器布局、入射/反射声束及设备连接</text>

  <!-- 3D 叶片（等距） -->
  <g transform="translate(40,120)">
    <!-- 主体：前缘到后缘的斜四边形体 -->
    <polygon class="bladeTop" filter="url(#shadow)"
      points="100,20  840,120  980,300  240,200"/>
    <polygon class="bladeSide"
      points="840,120  950,90  1090,270  980,300"/>
    <polygon class="bladeEdge"
      points="100,20  240,200  980,300  950,90"/>

    <!-- 内腔切割面（开窗） -->
    <g opacity="0.96">
      <polygon class="cavityFace"
        points="240,180  820,260  910,280  330,210"/>
      <!-- 腔体内部轻微阴影 -->
      <ellipse cx="580" cy="235" rx="300" ry="40" fill="#cfd8dc" opacity="0.25"/>
    </g>

    <!-- 传感器（圆柱体-等距） -->
    <!-- S1 发射器 -->
    <g transform="translate(320,220)">
      <ellipse cx="0" cy="0" rx="22" ry="10" class="sensorTop"/>
      <rect x="-22" y="0" width="44" height="24" class="sensor"/>
      <ellipse cx="0" cy="24" rx="22" ry="10" class="sensorTop"/>
    </g>
    <text x="300" y="212" class="small">S1 发射器 (5 MHz)</text>

    <!-- S2 接收器A -->
    <g transform="translate(760,285)">
      <ellipse cx="0" cy="0" rx="20" ry="9" class="sensorTop"/>
      <rect x="-20" y="0" width="40" height="22" class="sensor"/>
      <ellipse cx="0" cy="22" rx="20" ry="9" class="sensorTop"/>
    </g>
    <text x="742" y="318" class="small">S2 接收器A</text>

    <!-- S3 接收器B -->
    <g transform="translate(700,210)">
      <ellipse cx="0" cy="0" rx="20" ry="9" class="sensorTop"/>
      <rect x="-20" y="0" width="40" height="22" class="sensor"/>
      <ellipse cx="0" cy="22" rx="20" ry="9" class="sensorTop"/>
    </g>
    <text x="682" y="202" class="small">S3 接收器B</text>

    <!-- 缺陷（球体） -->
    <g transform="translate(540,245)">
      <circle r="16" class="defect"/>
      <circle r="8" fill="#ffcdd2" opacity="0.6"/>
    </g>
    <text x="552" y="238" class="label" fill="#b71c1c">缺陷 P(x,y)</text>

    <!-- 入射声束与反射（含光晕） -->
    <!-- S1 -> 缺陷 -->
    <path d="M 320 232 C 380 238, 470 242, 540 245" class="beamGlow"/>
    <path d="M 320 232 C 380 238, 470 242, 540 245" class="beam" marker-end="url(#arrowBlue)"/>

    <!-- 缺陷 -> S2 -->
    <path d="M 540 245 C 630 265, 695 278, 760 285" class="refGlow"/>
    <path d="M 540 245 C 630 265, 695 278, 760 285" class="ref" marker-end="url(#arrowRed)"/>

    <!-- 缺陷 -> S3 -->
    <path d="M 540 245 C 600 235, 650 222, 700 210" class="refGlow"/>
    <path d="M 540 245 C 600 235, 650 222, 700 210" class="ref" marker-end="url(#arrowRed)"/>

    <!-- 测量标注（三边测距） -->
    <g>
      <path d="M 320 232 L 540 245" stroke="#64b5f6" stroke-width="2" stroke-dasharray="6 6"/>
      <text x="410" y="226" class="small" fill="#1e88e5">d1 = v·t1/2</text>
      <path d="M 760 285 L 540 245" stroke="#ef9a9a" stroke-width="2" stroke-dasharray="6 6"/>
      <text x="650" y="292" class="small" fill="#e53935">d2 = v·t2/2</text>
      <path d="M 700 210 L 540 245" stroke="#ef9a9a" stroke-width="2" stroke-dasharray="6 6"/>
      <text x="620" y="208" class="small" fill="#e53935">d3 = v·t3/2</text>
    </g>
  </g>

  <!-- 设备机柜（右侧等距箱体） -->
  <g transform="translate(980,160)">
    <!-- 箱体三面 -->
    <polygon class="equip" filter="url(#shadow)" points="0,60  180,0  180,240  0,300"/>
    <polygon class="panel" points="180,0  260,40  260,280  180,240"/>
    <polygon class="panel" points="0,60  80,100  80,340  0,300"/>

    <!-- 前面板内容 -->
    <g transform="translate(10,80)">
      <rect x="0" y="0" width="150" height="28" rx="6" class="glass"/>
      <text x="10" y="20" class="small">超声波发生器 1–10 MHz</text>
      <rect x="0" y="46" width="150" height="28" rx="6" class="glass"/>
      <text x="10" y="66" class="small">多通道接收器 100 MHz</text>
      <rect x="0" y="92" width="150" height="28" rx="6" class="glass"/>
      <text x="10" y="112" class="small">信号处理 & 定位</text>
    </g>
  </g>

  <!-- 连接线（传感器到设备） -->
  <path d="M 360 352 C 600 420, 860 320, 980 280" stroke="#7e57c2" stroke-width="2.5" fill="none" marker-end="url(#arrowBlue)"/>
  <path d="M 820 405 C 920 380, 960 340, 980 320" stroke="#7e57c2" stroke-width="2.5" fill="none" marker-end="url(#arrowBlue)"/>
  <path d="M 780 270 C 900 260, 960 260, 980 260" stroke="#7e57c2" stroke-width="2.5" fill="none" marker-end="url(#arrowBlue)"/>

  <!-- 说明标注 -->
  <g>
    <path d="M 565 230 L 640 160" class="leader"/>
    <text x="648" y="158" class="label">三角定位计算</text>
    <text x="648" y="178" class="small">(x−x1)^2+(y−y1)^2=d1^2 等式组</text>
  </g>

  <!-- 图例 -->
  <g transform="translate(50,700)">
    <rect x="0" y="0" width="880" height="90" rx="10" class="legend"/>
    <text x="16" y="26" class="label">图例</text>
    <line x1="120" y1="56" x2="200" y2="56" class="beamGlow"/>
    <line x1="120" y1="56" x2="200" y2="56" class="beam" marker-end="url(#arrowBlue)"/>
    <text x="208" y="60" class="small">入射声束</text>
    <line x1="310" y1="56" x2="390" y2="56" class="refGlow"/>
    <line x1="310" y1="56" x2="390" y2="56" class="ref" marker-end="url(#arrowRed)"/>
    <text x="398" y="60" class="small">反射波</text>
    <rect x="510" y="42" width="26" height="16" class="sensor"/>
    <ellipse cx="523" cy="42" rx="13" ry="7" class="sensorTop"/>
    <ellipse cx="523" cy="58" rx="13" ry="7" class="sensorTop"/>
    <text x="548" y="60" class="small">传感器（等距圆柱）</text>
    <circle cx="700" cy="55" r="10" class="defect"/>
    <text x="716" y="60" class="small">缺陷</text>
  </g>
</svg>

